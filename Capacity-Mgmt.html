<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT Capacity Management: A New Practice Framework</title>
    
    <!-- Tailwind CSS for rapid UI development -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for the presentation */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
            color: #1a202c;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #presentation-container {
            height: 100vh;
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        .slide {
            flex: none;
            width: 100vw;
            height: 100vh;
            scroll-snap-align: start;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem 4rem;
            position: relative;
            background-size: cover;
            background-position: center;
            overflow: auto; /* Allow content to scroll if it overflows */
        }

        /* Slide background colors */
        #slide-1 { background: linear-gradient(45deg, #0f172a, #1e293b); }
        #slide-2, #slide-4, #slide-6, #slide-8 { background-color: #ffffff; }
        #slide-3, #slide-5, #slide-7, #slide-9 { background-color: #f8fafc; }
        #slide-10 { background: linear-gradient(45deg, #0f172a, #1e293b); }

        .slide-content {
            max-width: 1200px;
            width: 100%;
        }

        /* Navigation Arrows */
        .nav-arrow {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1001; /* Ensure it's above the modal backdrop */
        }
        .nav-arrow button {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        .nav-arrow button:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        .nav-arrow button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Progress Bar */
        .progress-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            height: 5px;
            background-color: #3b82f6; /* Accent color */
            width: 0%;
            transition: width 0.2s ease-out;
            z-index: 1002;
        }

        /* Modal for Framework Diagram */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 12px;
            max-width: 80vw;
            max-height: 80vh;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }
        .modal-overlay.active .modal-content {
            transform: scale(1);
        }
        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 2rem;
            color: white;
            cursor: pointer;
        }
        
        /* Custom accent color */
        .text-accent { color: #3b82f6; }
        .bg-accent { background-color: #3b82f6; }
        .border-accent { border-color: #3b82f6; }
        
        /* Animations */
        .fade-in-up { animation: fadeInUp 0.8s ease-out forwards; opacity: 0; }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .delay-1 { animation-delay: 0.2s; } .delay-2 { animation-delay: 0.4s; }
        .delay-3 { animation-delay: 0.6s; } .delay-4 { animation-delay: 0.8s; }
        .delay-5 { animation-delay: 1.0s; } .delay-6 { animation-delay: 1.2s; }

        /* Mermaid Gantt Chart */
        .mermaid-container { transform: scale(1.15); transform-origin: center; }
        .mermaid { background-color: transparent; text-align: center; }

    </style>
</head>
<body class="overflow-hidden">

    <div id="presentation-container">
        <!-- Slide 1: Title -->
        <section id="slide-1" class="slide">
            <div class="slide-content text-white text-center">
                <div class="fade-in-up">
                    <h1 class="text-5xl md:text-7xl font-extrabold tracking-tight">IT Capacity Management</h1>
                    <h2 class="text-4xl md:text-6xl font-bold text-accent mt-2">A New Practice Framework</h2>
                </div>
                <p class="text-xl md:text-2xl text-slate-300 mt-8 fade-in-up delay-1">Operational Excellence Program Briefing</p>
                <div class="inline-block bg-slate-700 text-slate-200 px-4 py-1 rounded-full mt-4 text-sm font-semibold fade-in-up delay-2">
                    Weekly Team Update
                </div>
            </div>
        </section>

        <!-- Slide 2: What is Capacity Management? -->
        <section id="slide-2" class="slide">
            <div class="slide-content">
                <h2 class="text-4xl font-bold text-slate-800 mb-4 fade-in-up">What is <span class="text-accent">Capacity Management?</span></h2>
                <p class="text-xl text-slate-600 mb-12 fade-in-up delay-1">A mandatory process to ensure we have the <b class="text-slate-800">right capacity</b>, in the <b class="text-slate-800">right place</b>, at the <b class="text-slate-800">right time</b>, for the <b class="text-slate-800">right cost</b>.</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="bg-slate-50 p-6 rounded-lg shadow-sm fade-in-up delay-2"><i class="fa-solid fa-server text-accent text-3xl mb-4"></i><h3 class="font-bold text-lg text-slate-800 mb-2">Right Capacity</h3><p class="text-slate-600">Sufficient infra to meet performance requirements (NFRs).</p></div>
                    <div class="bg-slate-50 p-6 rounded-lg shadow-sm fade-in-up delay-3"><i class="fa-solid fa-sitemap text-accent text-3xl mb-4"></i><h3 class="font-bold text-lg text-slate-800 mb-2">Right Place</h3><p class="text-slate-600">Correct resource allocation across all architecture layers to avoid bottlenecks.</p></div>
                    <div class="bg-slate-50 p-6 rounded-lg shadow-sm fade-in-up delay-4"><i class="fa-solid fa-clock text-accent text-3xl mb-4"></i><h3 class="font-bold text-lg text-slate-800 mb-2">Right Time</h3><p class="text-slate-600">Proactive provisioning—not too early (costs) and not too late (issues).</p></div>
                    <div class="bg-slate-50 p-6 rounded-lg shadow-sm fade-in-up delay-5"><i class="fa-solid fa-dollar-sign text-accent text-3xl mb-4"></i><h3 class="font-bold text-lg text-slate-800 mb-2">Right Cost</h3><p class="text-slate-600">Avoid significant over-provisioning to manage software, power, and support costs.</p></div>
                </div>
            </div>
        </section>

        <!-- Slide 3: Benefits -->
        <section id="slide-3" class="slide">
            <div class="slide-content">
                 <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center fade-in-up">Benefits of <span class="text-accent">Effective Capacity Management</span></h2>
                 <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300 fade-in-up delay-1"><h3 class="font-bold text-xl text-slate-800 mb-2">Improved Service Availability</h3><p class="text-slate-600">Reduce or eliminate capacity-related incidents.</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300 fade-in-up delay-2"><h3 class="font-bold text-xl text-slate-800 mb-2">Investment Forecasting</h3><p class="text-slate-600">Align investment with budget cycles, avoiding unplanned spending.</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300 fade-in-up delay-3"><h3 class="font-bold text-xl text-slate-800 mb-2">Optimized Infra Performance</h3><p class="text-slate-600">Identify and resolve bottlenecks by monitoring consumption.</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300 fade-in-up delay-4"><h3 class="font-bold text-xl text-slate-800 mb-2">Confident Scaling</h3><p class="text-slate-600">Proactively manage risks associated with rapid growth.</p></div>
                    <div class="bg-white p-6 rounded-xl shadow-lg transform hover:scale-105 transition-transform duration-300 fade-in-up delay-5"><h3 class="font-bold text-xl text-slate-800 mb-2">Continual Performance Improvement</h3><p class="text-slate-600">Drive improvements by highlighting the next constraints.</p></div>
                 </div>
            </div>
        </section>

        <!-- Slide 4: Athene Tooling -->
        <section id="slide-4" class="slide">
            <div class="slide-content">
                <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center fade-in-up">Our Group Standard Tool: <span class="text-accent">Athene (SCM)</span></h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    <div class="fade-in-up delay-1"><img src="https://placehold.co/600x400/e2e8f0/3b82f6?text=Athene+Dashboard" alt="Athene Dashboard Mockup" class="rounded-lg shadow-2xl"></div>
                    <div class="space-y-6">
                        <div class="flex items-start fade-in-up delay-2"><div class="flex-shrink-0 h-10 w-10 rounded-full bg-accent text-white flex items-center justify-center text-lg"><i class="fas fa-database"></i></div><div class="ml-4"><h3 class="text-lg font-bold text-slate-800">Data Integration</h3><p class="mt-1 text-slate-600">Takes data from various sources for a unified view.</p></div></div>
                        <div class="flex items-start fade-in-up delay-3"><div class="flex-shrink-0 h-10 w-10 rounded-full bg-accent text-white flex items-center justify-center text-lg"><i class="fas fa-chart-line"></i></div><div class="ml-4"><h3 class="text-lg font-bold text-slate-800">Agile Management</h3><p class="mt-1 text-slate-600">Manages environment at a service-by-server level via dashboards.</p></div></div>
                        <div class="flex items-start fade-in-up delay-4"><div class="flex-shrink-0 h-10 w-10 rounded-full bg-accent text-white flex items-center justify-center text-lg"><i class="fas fa-brain"></i></div><div class="ml-4"><h3 class="text-lg font-bold text-slate-800">Predictive Analytics</h3><p class="mt-1 text-slate-600">Creates forward-looking projections on service performance.</p></div></div>
                        <div class="flex items-start fade-in-up delay-5"><div class="flex-shrink-0 h-10 w-10 rounded-full bg-accent text-white flex items-center justify-center text-lg"><i class="fas fa-bell"></i></div><div class="ml-4"><h3 class="text-lg font-bold text-slate-800">Automated Alerting</h3><p class="mt-1 text-slate-600">"Service View" alerts teams about potential breaches before they occur.</p></div></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 5: The New Initiative (UPDATED) -->
        <section id="slide-5" class="slide">
            <div class="slide-content text-center">
                <h2 class="text-4xl font-bold text-slate-800 mb-6 fade-in-up">What's Changing? Introducing the <span class="text-accent">CMPF</span></h2>
                <p class="text-2xl text-slate-600 max-w-4xl mx-auto mb-10 fade-in-up delay-1">
                    We are establishing a <b class="text-slate-900">Capacity Management Practice Framework (CMPF)</b> to ensure consistency and manage services at scale.
                </p>
                <div id="diagram-placeholder" class="bg-white p-8 rounded-lg shadow-xl inline-block cursor-pointer hover:shadow-2xl hover:scale-105 transition-all duration-300 fade-in-up delay-2">
                    <img src="https://placehold.co/800x500/e2e8f0/3b82f6?text=Framework+Diagram" alt="Framework Diagram Placeholder" class="rounded-md">
                    <p class="text-slate-500 mt-4 font-semibold">Click to enlarge framework diagram</p>
                </div>
            </div>
        </section>

        <!-- Slide 6: Core Objectives -->
        <section id="slide-6" class="slide">
            <div class="slide-content">
                <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center fade-in-up">Key Objectives of the <span class="text-accent">New Framework</span></h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="text-center p-6 border-l-4 border-accent bg-slate-50 rounded-r-lg fade-in-up delay-1"><h3 class="text-xl font-bold text-slate-800 mb-2">Business Alignment</h3><p class="text-slate-600">Incorporate business growth plans into capacity planning.</p></div>
                    <div class="text-center p-6 border-l-4 border-accent bg-slate-50 rounded-r-lg fade-in-up delay-2"><h3 class="text-xl font-bold text-slate-800 mb-2">Expanded Scope</h3><p class="text-slate-600">Move beyond just infrastructure to include IT Asset and Business Service levels.</p></div>
                    <div class="text-center p-6 border-l-4 border-accent bg-slate-50 rounded-r-lg fade-in-up delay-3"><h3 class="text-xl font-bold text-slate-800 mb-2">Consistency</h3><p class="text-slate-600">Enable a single, consistent practice across all teams.</p></div>
                    <div class="text-center p-6 border-l-4 border-accent bg-slate-50 rounded-r-lg fade-in-up delay-4"><h3 class="text-xl font-bold text-slate-800 mb-2">Proactive Risk Management</h3><p class="text-slate-600">Use KPIs and KCIs to manage risks proactively.</p></div>
                    <div class="text-center p-6 border-l-4 border-accent bg-slate-50 rounded-r-lg fade-in-up delay-5"><h3 class="text-xl font-bold text-slate-800 mb-2">Resource Efficiency</h3><p class="text-slate-600">Improve the efficiency of resource allocation.</p></div>
                </div>
            </div>
        </section>

        <!-- Slide 7: Delivery Milestones (UPDATED) -->
        <section id="slide-7" class="slide">
            <div class="slide-content w-full">
                <h2 class="text-4xl font-bold text-slate-800 mb-16 text-center fade-in-up">Delivery <span class="text-accent">Milestones</span></h2>
                <!-- Timeline Container -->
                <div class="relative w-full fade-in-up delay-1">
                    <!-- Timeline Line -->
                    <div class="absolute top-1/2 left-0 w-full h-1 bg-blue-200" style="transform: translateY(-50%);"></div>
                    <!-- Timeline Items -->
                    <div class="relative flex justify-between items-start w-full">
                        <!-- Milestone 1 -->
                        <div class="relative w-1/4 text-center">
                            <div class="absolute top-1/2 left-1/2 w-4 h-4 bg-accent rounded-full border-4 border-white" style="transform: translate(-50%, -50%);"></div>
                            <div class="mt-8 pt-4"><h3 class="font-bold text-lg text-slate-800">May - Jul 2025</h3><p class="text-sm text-slate-500">Framework & Assessment</p></div>
                        </div>
                        <!-- Milestone 2 -->
                        <div class="relative w-1/4 text-center">
                            <div class="absolute top-1/2 left-1/2 w-4 h-4 bg-accent rounded-full border-4 border-white" style="transform: translate(-50%, -50%);"></div>
                            <div class="mb-8 pb-4"><h3 class="font-bold text-lg text-slate-800">Sep 2025</h3><p class="text-sm text-slate-500">Foundations & Controls</p></div>
                        </div>
                        <!-- Milestone 3 -->
                        <div class="relative w-1/4 text-center">
                             <div class="absolute top-1/2 left-1/2 w-4 h-4 bg-accent rounded-full border-4 border-white" style="transform: translate(-50%, -50%);"></div>
                            <div class="mt-8 pt-4"><h3 class="font-bold text-lg text-slate-800">Jan - Feb 2026</h3><p class="text-sm text-slate-500">Targeted Adoption</p></div>
                        </div>
                        <!-- Milestone 4 -->
                        <div class="relative w-1/4 text-center">
                             <div class="absolute top-1/2 left-1/2 w-4 h-4 bg-accent rounded-full border-4 border-white" style="transform: translate(-50%, -50%);"></div>
                            <div class="mb-8 pb-4"><h3 class="font-bold text-lg text-slate-800">Jun - Dec 2026</h3><p class="text-sm text-slate-500">Sustainability</p></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 8: Timeline -->
        <section id="slide-8" class="slide">
            <div class="slide-content">
                <h2 class="text-4xl font-bold text-slate-800 mb-8 text-center fade-in-up">High-Level <span class="text-accent">Timeline</span></h2>
                <div class="mermaid-container fade-in-up delay-1">
                    <div class="mermaid">
gantt
    title CMPF Development and Rollout Timeline
    dateFormat  YYYY-MM-DD
    axisFormat %b %Y

    section Framework Definition
    Define Structure & Kick-off :done, 2025-05-01, 2025-05-15
    Draft Core Capabilities     :done, 2025-05-16, 2025-05-31

    section Content Development
    Develop CMPF Content      :done, 2025-06-01, 2025-06-15
    Draft Enablement & Review :done, 2025-06-16, 2025-06-30
    
    section Finalization
    Stakeholder Feedback      :active, 2025-07-01, 2025-07-21
    Revise & Finalize         :2025-07-22, 10d

    section Piloting & Delivery
    Playbook Pilot Planning     :2025-07-15, 17d
    Playbook Pilot Execution    :2025-08-01, 31d
    Deliver Final Playbooks     :2025-09-01, 30d
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 9: Scope & How It Works (UPDATED) -->
        <section id="slide-9" class="slide">
            <div class="slide-content">
                 <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center fade-in-up">Scope & <span class="text-accent">How It Works</span></h2>
                 <div class="bg-white rounded-xl shadow-2xl p-10 grid grid-cols-1 md:grid-cols-3 gap-8 items-center fade-in-up delay-1">
                     <!-- Scope -->
                     <div class="col-span-1 border-r-2 border-slate-200 pr-8">
                         <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center"><i class="fa-solid fa-binoculars mr-3 text-accent"></i> In Scope</h3>
                         <div class="space-y-4">
                            <div class="bg-slate-100 p-4 rounded-lg"><p class="font-semibold text-slate-700">UK Important Business Services (IBS)</p></div>
                            <div class="bg-slate-100 p-4 rounded-lg"><p class="font-semibold text-slate-700">Critical Services by Line of Business</p></div>
                        </div>
                     </div>
                     <!-- How it Works -->
                     <div class="col-span-2">
                         <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center"><i class="fa-solid fa-gears mr-3 text-accent"></i> How It Works</h3>
                         <ul class="space-y-4 text-slate-600">
                             <li class="flex items-start"><span class="font-bold text-accent mr-3 mt-1"><i class="fa-solid fa-check-double"></i></span><span>The <b>Framework (CMPF)</b> provides the high-level "what" (guidelines, standards).</span></li>
                             <li class="flex items-start"><span class="font-bold text-accent mr-3 mt-1"><i class="fa-solid fa-check-double"></i></span><span><b>Playbooks</b> provide the department-specific "how-to".</span></li>
                             <li class="flex items-start"><span class="font-bold text-accent mr-3 mt-1"><i class="fa-solid fa-check-double"></i></span><span>Success depends on integration with <b>SDLC, NFRs, and Incident Management</b>.</span></li>
                             <li class="flex items-start"><span class="font-bold text-accent mr-3 mt-1"><i class="fa-solid fa-check-double"></i></span><span>Adoption supported by enhanced <b>governance, tooling, and reporting</b>.</span></li>
                         </ul>
                     </div>
                 </div>
            </div>
        </section>

        <!-- Slide 10: Closing -->
        <section id="slide-10" class="slide">
            <div class="slide-content text-center text-white">
                <div class="fade-in-up">
                    <i class="fa-solid fa-comments text-accent text-7xl mb-8"></i>
                    <h2 class="text-6xl font-bold mb-4">Questions & Discussion</h2>
                    <p class="text-3xl text-slate-300">Thank you.</p>
                </div>
            </div>
        </section>
    </div>

    <!-- Modal -->
    <div id="diagram-modal" class="modal-overlay">
        <span id="modal-close-btn" class="modal-close">&times;</span>
        <div class="modal-content">
            <img src="https://placehold.co/1200x750/e2e8f0/3b82f6?text=Framework+Diagram" alt="Enlarged Framework Diagram" class="rounded-md max-w-full max-h-full">
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-arrow">
        <button id="prevBtn"><i class="fas fa-arrow-up"></i></button>
        <button id="nextBtn"><i class="fas fa-arrow-down"></i></button>
    </div>
    
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>


    <!-- Mermaid.js for Gantt Chart -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Mermaid.js Initialization ---
            mermaid.initialize({ 
                startOnLoad: true, 
                theme: 'base', 
                themeVariables: {
                    primaryColor: '#ffffff', primaryTextColor: '#1e293b', primaryBorderColor: '#e2e8f0',
                    lineColor: '#3b82f6', textColor: '#334155', secondaryColor: '#f1f5f9', tertiaryColor: '#f8fafc',
                    ganttTaskActiveBg: '#3b82f6', ganttTaskActiveStroke: '#0284c7', ganttTaskDoneBg: '#10b981',
                    ganttTaskDoneStroke: '#059669', ganttTaskBg: '#f1f5f9', ganttTaskStroke: '#e2e8f0',
                    ganttTitleColor: '#0f172a', ganttSectionTitleColor: '#0f172a',
                }
            });

            // --- Modal Logic ---
            const diagramPlaceholder = document.getElementById('diagram-placeholder');
            const modal = document.getElementById('diagram-modal');
            const closeModalBtn = document.getElementById('modal-close-btn');

            const openModal = () => modal.classList.add('active');
            const closeModal = () => modal.classList.remove('active');

            if (diagramPlaceholder && modal && closeModalBtn) {
                diagramPlaceholder.addEventListener('click', openModal);
                closeModalBtn.addEventListener('click', closeModal);
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        closeModal();
                    }
                });
            }

            // --- Presentation Logic ---
            const slides = document.querySelectorAll('.slide');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const progressBar = document.getElementById('progressBar');
            const presentationContainer = document.getElementById('presentation-container');
            let currentSlide = 0;
            const totalSlides = slides.length;

            function goToSlide(slideIndex) {
                if (slideIndex >= 0 && slideIndex < totalSlides) {
                    presentationContainer.scrollTo({ top: slideIndex * window.innerHeight, behavior: 'smooth' });
                }
            }
            
            function updateNav() {
                prevBtn.disabled = currentSlide === 0;
                nextBtn.disabled = currentSlide === totalSlides - 1;
            }

            function updateProgressBar() {
                progressBar.style.width = `${((currentSlide + 1) / totalSlides) * 100}%`;
            }

            prevBtn.addEventListener('click', () => goToSlide(currentSlide - 1));
            nextBtn.addEventListener('click', () => goToSlide(currentSlide + 1));

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowDown' || e.key === 'ArrowRight' || e.key === 'PageDown') {
                    e.preventDefault(); goToSlide(currentSlide + 1);
                } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft' || e.key === 'PageUp') {
                    e.preventDefault(); goToSlide(currentSlide - 1);
                } else if (e.key === 'Escape' && modal.classList.contains('active')) {
                    closeModal();
                }
            });
            
            presentationContainer.addEventListener('scroll', () => {
                const newSlideIndex = Math.round(presentationContainer.scrollTop / window.innerHeight);
                if (newSlideIndex !== currentSlide) {
                    currentSlide = newSlideIndex;
                    updateNav();
                    updateProgressBar();
                }
            });
            
            updateNav();
            updateProgressBar();
        });
    </script>

</body>
</html>
