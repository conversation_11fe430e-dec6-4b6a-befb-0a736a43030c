/*!
 * ------------------------------------------- START OF THIRD PARTY NOTICE -----------------------------------------
 * 
 * This file is based on or incorporates material from the projects listed below (Third Party IP). The original copyright notice and the license under which Microsoft received such Third Party IP, are set forth below. Such licenses and notices are provided for informational purposes only. Microsoft licenses the Third Party IP to you under the licensing terms for the Microsoft product. Microsoft reserves all other rights not expressly granted under this agreement, whether by implication, estoppel or otherwise.
 * 
 *   json2.js (2016-05-01)
 *   https://github.com/douglascrockford/JSON-js
 *   License: Public Domain
 * 
 * Provided for Informational Purposes Only
 * 
 * ----------------------------------------------- END OF THIRD PARTY NOTICE ------------------------------------------
 */!function(e){function t(t){for(var n,r,i=t[0],a=t[1],s=0,u=[];s<i.length;s++)r=i[s],Object.prototype.hasOwnProperty.call(o,r)&&o[r]&&u.push(o[r][0]),o[r]=0;for(n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n]);for(c&&c(t);u.length;)u.shift()()}var n,r={},o={1:0};function i(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,i),n.l=!0,n.exports}Function.prototype.bind||(n=Array.prototype.slice,Function.prototype.bind=function(e){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var t=n.call(arguments,1),r=t.length,o=this,i=function(){},a=function(){return t.length=r,t.push.apply(t,arguments),o.apply(i.prototype.isPrototypeOf(this)?this:e,t)};return this.prototype&&(i.prototype=this.prototype),a.prototype=new i,a}),document.head=document.head||document.getElementsByTagName("head")[0],function(){function e(t){var n=this,r=0,o=null,i=[];function a(){if(i.length>0){var e=i.slice();i=[],setTimeout((function(){for(var t=0,n=e.length;t<n;++t)e[t]()}),0)}}function s(e){0===r&&(o=e,r=1,a())}function u(e){0===r&&(o=e,r=2,a())}n.then=function(t,n){return new e((function(s,u){!function(t,n,s,u){i.push((function(){var i;try{i=1===r?"function"==typeof t?t(o):o:"function"==typeof n?n(o):o}catch(a){return void u(a)}i instanceof e?i.then(s,u):2===r&&"function"!=typeof n?u(i):s(i)})),0!==r&&a()}(t,n,s,u)}))},n["catch"]=function(e){return n.then(null,e)},function(){if("function"!=typeof t)throw new TypeError("Promise: argument is not a Function object");try{t(s,u)}catch(e){u(e)}}()}function t(e,t,n,r,o){return function(i){e[t]=r?i:o?{status:"fulfilled",value:i}:{status:"rejected",reason:i},n()}}function n(n,r){return n&&n.length?new e((function(o,i){for(var a=[],s=0,u=0,c=n.length;u<c;++u){var l=n[u];if(l instanceof e){s++;var d=function(){0==--s&&o(a)};r?l.then(t(a,u,d,r),i):l.then(t(a,u,d,r,!0),t(a,u,d,r,!1))}else a[u]=l}0===s&&setTimeout((function(){o(a)}),0)})):e.resolve([])}function r(e,t){return function(){e(t)}}e.all=function(e){return n(e,!0)},e.allSettled=function(e){return n(e,!1)},e.race=function(t){return new e((function(n,o){if(t&&t.length)for(var i=0,a=t.length;i<a;++i){var s=t[i];s instanceof e?s.then(n,o):setTimeout(r(n,s),0)}}))},e.reject=function(t){return new e((function(e,n){n(t)}))},e.resolve=function(t){return t instanceof e?t:t&&"function"==typeof t.then?new e((function(e,n){t.then(e,n)})):new e((function(e){e(t)}))},window.Promise||(window.Promise=e),window.Promise.all||(window.Promise.all=e.all),window.Promise.allSettled||(window.Promise.allSettled=e.allSettled),window.Promise.race||(window.Promise.race=e.race),window.Promise.reject||(window.Promise.reject=e.reject),window.Promise.resolve||(window.Promise.resolve=e.resolve)}(),i.e=function(e){var t=[],n=o[e];if(0!==n)if(n)t.push(n[2]);else{var r=new Promise((function(t,r){n=o[e]=[t,r]}));t.push(n[2]=r);var a=window.ServerData,s=a&&a.loader&&a.loader.cdnRoots||[],u=a&&a.slMaxRetry?a.slMaxRetry:s.length-1,c=new Error;var l=function d(t,n){var r,i=document.createElement("script");i.charset="utf-8",i.timeout=120;var a=document.querySelector("script[nonce]");if(a){var l=a.nonce||a.getAttribute("nonce");i.setAttribute("nonce",l)}i.src=t,r=function(r){i.onerror=i.onload=null,clearTimeout(f);var a=o[e];if(0!==a)if(a)if(u<=0||n===u){var l=r&&("load"===r.type?"missing":r.type),p=r&&r.target&&r.target.src;c.message="Loading chunk "+e+" failed after "+(u+1)+" tries.\n("+l+": "+p+")",c.name="ChunkLoadError",c.type=l,c.request=p,a[1](c),o[e]=undefined,SRSRetry&&(window.external.notify(JSON.stringify({type:"invoke",value:{name:"CloudExperienceHost.Telemetry.logEvent",args:["MSA.ResourceDownloadError","Javascript failed to download on IDUX: "+p],context:"criticalError1"}})),window.external.notify(JSON.stringify({type:"event",value:{name:"CloudExperienceHost.done",data:"fail"}})))}else{var h=d(function(e,t){if(!t)return e;for(var n=0;n<t.length;n++)if(0==e.indexOf(t[n]))return t[(n+1)%t.length]+e.substring(t[n].length);return e}(t,s),n+1);document.head.appendChild(h)}else o[e]=undefined};var f=setTimeout((function(){r({type:"timeout",target:i})}),12e4);return i.onerror=i.onload=r,i}(function(e){return i.p+"content/js/asyncchunk/bssointerrupt_"+({0:"clienttracing"}[e]||e)+"_"+{0:"7423ac93515a2123bb59"}[e]+".js"}(e),0);document.head.appendChild(l)}return Promise.all(t)},i.m=e,i.c=r,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i.oe=function(e){throw console.error(e),e};var a=window.webpackJsonp=window.webpackJsonp||[],s=a.push.bind(a);a.push=t,a=a.slice();for(var u=0;u<a.length;u++)t(a[u]);var c=s;i(i.s=11)}([function(e,t,n){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=n(4),i=n(3),a=n(15),s=window,u=t.Object={assignRecursive:function(){return u.assignRecursiveWithCurrentDepth.apply(this,[1].concat(Array.prototype.slice.call(arguments)))},assignRecursiveWithCurrentDepth:function(e,t){if("number"==typeof e){for(var n=2,o=arguments.length;n<o;n++){var i=arguments[n];for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&""!==i[a]&&("object"===r(i[a])&&e<=3?(t[a]=t[a]||{},u.assignRecursiveWithCurrentDepth(++e,t[a],i[a])):t[a]=i[a])}return t}},clone:function(e){var t={};return e&&(t=i.parse(i.stringify(e))),t},join:function(e,t,n){var r="";return e&&u.forEach(e,(function(e,o){r&&(r+=t),r+=e+n+(o||"")})),r},forEach:function(e,t){o.utils.objectForEach(e,t)},findOwnProperty:function(e,t,n){var r;for(var o in n&&(r=t.toLowerCase()),e)if(e.hasOwnProperty(o)&&(o===t||n&&o.toLowerCase()===r))return o;return null},extend:o.utils.extend},c=t.String={trim:function(e){return e.replace(/^\s+|\s+$/g,"")},find:function(e,t,n,r){return e?n?e.toLowerCase().indexOf(t.toLowerCase(),r):e.indexOf(t,r):-1},format:a.format,doubleSplit:function(e,t,n,r,o){var i={};return e&&l.forEach(e.split(t),(function(e){if(e){var t=e.split(n),a=t[0];o&&(a=o(a)),1===t.length?i[a]=null:i[a]=r?t.slice(1):t.slice(1).join(n)}})),i},isEmailAddress:function(e){if((e=c.trim(e)).charAt(0)>"~"||-1!==e.indexOf(" "))return!1;var t=e.indexOf("@");if(-1===t||-1===e.indexOf(".",t))return!1;var n=e.split("@");if(n.length>2||n[0].length<1||n[1].length<2)return!1;if(s.ServerData.fApplyAsciiRegexOnInput){var r=new RegExp(/^[\x21-\x7E]+$/);return!!e.match(r)}return!0},isPhoneNumber:function(e){var t=e.replace(/\D+/g,"");return t.length>=4&&t.length<=50},isSkypeName:function(e){e=c.trim(e);var t=new RegExp(/^[a-zA-Z][a-zA-Z0-9.,\-_:']{0,128}$/);return!!e.match(t)},extractDomain:function(e,t,n){if(!c.isEmailAddress(e))return e;var r=c.trim(e).split("@")[1];return e=n?"@":"",t?e+r.slice(0,r.lastIndexOf(".")+1):e+r},extractDomainFromUrl:function(e){if(e){var t=document.createElement("a");return t.href=e,t.hostname}return""},extractOriginFromUrl:function(e){if(e){var t=document.createElement("a");t.href=e;var n=t.origin;return n||(n=t.protocol+"//"+t.hostname+(t.port?":"+t.port:"")),n}return""},doOriginsMatch:function(e,t){var n=c.extractOriginFromUrl(e);return c.extractOriginFromUrl(t)===n},capFirst:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},cleanseUsername:function(e,t){if(!e)return"";if(e=c.trim(e).toLowerCase(),!c.isEmailAddress(e)&&!c.isSkypeName(e)&&c.isPhoneNumber(e)){var n="";return t&&"+"===e.charAt(0)&&(n="+"),n+e.replace(/\D+/g,"")}return e},maskString:function(e,t){if(!e)return"";if(e.length<=2*t)return e;var n=e.length-2*t,r=Array(n+1).join("*");return e.substring(0,t)+r+e.substring(t+n)},utf8Encode:function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r>127&&r<2048?(t+=String.fromCharCode(r>>6|192),t+=String.fromCharCode(63&r|128)):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128),t+=String.fromCharCode(63&r|128))}return t}},l=t.Array={first:o.utils.arrayFirst,forEach:o.utils.arrayForEach,map:o.utils.arrayMap,removeItem:o.utils.arrayRemoveItem,arrayFilter:o.utils.arrayFilter,findIndex:function(e,t){if(e&&"object"===r(e)&&e.length)for(var n=0;n<e.length;n++)if(t(e[n]))return n;return-1}};t.DateTime={getCurrentTime:function(){return(new Date).getTime()},getUTCString:function(){return Date.prototype.toISOString?(new Date).toISOString():(new Date).toUTCString()}},t.ErrorData=function(e,t){var n=this;n.errorText=e,n.remediationText=t,n.toString=function(){return n.errorText}}},function(e,t,n){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=n(16),i=n(0),a=n(2),s=n(8),u=i.Object,c=i.String,l=i.Array,d=window,f=null,p={},h={},g={},m=null,v=null,b=null,y=null,S=null,C=null,E=null,w=null,P=!!d.ServerData.fUseSameSite,T=null,A=!!d.ServerData.fUseHighContrastDetectionMode;t.HttpCode={Ok:200,NotModified:304,Timeout:408,ClientClosedRequest:499};var _=t.Helper={isIEOlderThan:function(e){if(p[e]===undefined){var t=_.getIEVersion();p[e]=t&&t<e+1}return p[e]},isEdge:function(){if(null===f){f=!1;var e=_.getWindowsVersion();if(null!==e&&e>=10){var t=_.getIEVersion();f=null!==t&&t>=12}}return f},isChrome:function(){return null===m&&(m=navigator.userAgent.toLowerCase().indexOf("chrome")>-1),m},isFirefoxNewerThan:function(e){if(h[e]===undefined){var t=_.getFirefoxVersion();h[e]=t&&t>e}return h[e]},isChromeNewerThan:function(e){if(g[e]===undefined){var t=_.getChromeVersion();g[e]=t&&t>e}return g[e]},isIOSSafari:function(){if(null===v){var e=d.navigator.userAgent.toLowerCase();v=/safari/.test(e)&&/iphone|ipod|ipad/.test(e)&&!d.MSStream}return v},isIOSUIWebView:function(){if(null===b){var e=d.navigator.userAgent.toLowerCase();b=!1===/safari/.test(e)&&/iphone|ipod|ipad/.test(e)&&!d.MSStream}return b},isQtCarBrowser:function(){return null===y&&(y=navigator.userAgent.toLowerCase().indexOf("qtcarbrowser")>-1),y},isEdgeClientBrowser:function(){return null===S&&(S=navigator.userAgent.toLowerCase().indexOf("edgeclient/")>-1),S},isOnTouchStartEventSupported:function(){return"ontouchstart"in document.documentElement},getIEVersion:function(){var e=d.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);if(e.indexOf("Trident/")>0){var n=e.indexOf("rv:");return parseInt(e.substring(n+3,e.indexOf(".",n)),10)}var r=e.indexOf("Edge/");return r>0?parseInt(e.substring(r+5,e.indexOf(".",r)),10):null},getFirefoxVersion:function(){var e=d.navigator.userAgent.match(/(firefox(?=\/))\/?\s*(\d+)/i);return e&&3===e.length&&"firefox"===e[1].toLowerCase()?parseInt(e[2]):null},getChromeVersion:function(){var e=d.navigator.userAgent.match(/(chrome(?=\/))\/?\s*(\d+)/i);return e&&3===e.length&&"chrome"===e[1].toLowerCase()?parseInt(e[2]):null},getWindowsVersion:function(){return null!==new RegExp("Windows NT ([0-9]{1,}[.0-9]{0,})").exec(navigator.userAgent)?parseFloat(RegExp.$1):null},htmlEscape:function(e){if(!e)return"";var t=document.createElement("textarea");return t.innerText=e,t.innerHTML},htmlUnescape:function(e){if(!e)return"";if(e.match(/<[^<>]+>/))return e;var t=document.createElement("textarea");return t.innerHTML=e,t.value},getStackSize:function(e){var t=0,n=null==e;try{!function r(){t++,(n||t<=e)&&r()}()}catch(r){}return t},getAnimationEndEventName:function(){var e=document.createElement("div"),t={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(var n in t)if(e.style[n]!==undefined)return t[n];return""},isStackSizeGreaterThan:function(e){return e=e||0,_.getStackSize(e)>e},isSvgImgSupported:function(){return null===T&&(T=document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Image","1.1")),T},isPlaceholderAttributeAllowed:function(e){return null===E&&(E=_.isChromeNewerThan(16)||_.isEdge()||_.isFirefoxNewerThan(14)||e&&_.isIOSUIWebView()||_.isIOSSafari()||_.isQtCarBrowser()),E},isCSSAnimationSupported:function(){var e=!1,t=document.createElement("div");(e=t.style.animationName!==undefined)||(e=!!l.first(["Webkit","Moz","O"],(function(e){return t.style[e+"AnimationName"]!==undefined})));return e},isStyleSupported:function(e){return e in document.documentElement.style},isCORSSupported:function(){return d.XDomainRequest||d.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest},isHistorySupported:function(){if(null===w){if(w=d.history&&d.history.pushState&&"undefined"!=typeof d.history.state&&"undefined"!=typeof d.onpopstate)try{d.history.replaceState("__history_test",""),("__history_test"!==d.history.state||_.isEdgeClientBrowser())&&(w=!1)}catch(e){w=!1}}return w},isFidoSupportedAsync:function(e,t){if(!t){if(!(d.navigator.credentials!==undefined&&d.navigator.credentials.create!==undefined&&d.navigator.credentials.get!==undefined&&d.PublicKeyCredential!==undefined&&d.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable!==undefined))return a.resolve(!1);if(d.PublicKeyCredential.isExternalCTAP2SecurityKeySupported)return s.newPromiseWithTimeout(d.PublicKeyCredential.isExternalCTAP2SecurityKeySupported,o.PromiseTimeout,!1)}return a.resolve(e)},isChangingInputTypeSupported:function(){return!_.isIEOlderThan(9)},getComputedSpan:function(){var e=document.createElement("span");e.style.borderLeftColor="red",e.style.borderRightColor="blue",e.style.backgroundColor="Window",e.style.position="absolute",e.style.top="-999px",document.body.appendChild(e);var t=_.getComputedStyle(e),n=t.borderLeftColor,r=t.borderRightColor,o=t.backgroundColor;return document.body.removeChild(e),{borderLeftColor:n,borderRightColor:r,backgroundColor:o}},isHighContrast:function(){if(null===C){var e=_.getComputedSpan();C=e.borderLeftColor===e.borderRightColor,A&&!C&&(C=_.getIsHighContrastUsingCssMediaQuery().isHighContrast)}return C},getIsHighContrastUsingCssMediaQuery:function(){var e=document.getElementsByTagName("head")[0],t=document.createElement("style");t.innerHTML='@media (-ms-high-contrast: active) {  .high-contrast-detection::before {    content: "active";    display: none;  }}@media (-ms-high-contrast: black-on-white) {  .high-contrast-detection::before {    content: "white";    display: none;  }}@media (-ms-high-contrast: white-on-black) {  .high-contrast-detection::before {    content: "black";    display: none;  }}',e.appendChild(t);var n=document.createElement("div");n.className="high-contrast-detection",document.body.appendChild(n);var r=window.getComputedStyle(n,"::before").content,o="";return'"black"'===r?o="black":'"white"'===r&&(o="white"),document.body.removeChild(n),e.removeChild(t),{isHighContrast:-1!==['"active"','"black"','"white"'].indexOf(r),theme:o}},getHighContrastTheme:function(){function e(e,t,n){function r(e,t,n,r){return new RegExp("^rgba?\\("+t+",\\s?"+n+",\\s?"+r+"(,\\s?\\d+\\.?\\d*)?\\)$","i").test(e.trim())}for(var o=0;o<t.length;o++){var i=t[o].split(",").map(Number),a=i[0],s=i[1],u=i[2];if(r(e,a,s,u))return!0}return!!l.first(n,(function(t){return t===e.trim()}))}if(_.isHighContrast()){var t=_.getComputedSpan();if(t.backgroundColor){var n=t.backgroundColor.toLowerCase().replace(new RegExp(" ","g"),""),r=e(n,["0,0,0"],["#000000","#000"]),o=e(n,["255,255,255"],["#ffffff","#fff"]),i=e(n,["32,32,32"],["#202020"]),a=e(n,["45,50,54"],["#2d3236"]),s=e(n,["255,250,239"],["#fffaef"]);if(r||i||a)return"black";if(o||s)return"white";if(A)return _.getIsHighContrastUsingCssMediaQuery().theme}}},getComputedStyle:function(e){return document.defaultView&&document.defaultView.getComputedStyle?document.defaultView.getComputedStyle(e,null):e.currentStyle?e.currentStyle:{}},history:{pushState:function(e,t){_.isHistorySupported()&&d.history.pushState(e,t)},replaceState:function(e,t){_.isHistorySupported()&&d.history.replaceState(e,t)}},addEventListener:function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r):e.attachEvent&&e.attachEvent("on"+t,n)},removeEventListener:function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r):e.detachEvent&&e.detachEvent("on"+t,n)},getEventTarget:function(e){return e?e.target?e.target:e.srcElement?e.srcElement:null:null}},x=t.QueryString={parse:function(e){var t=e,n=null,r=null;if(e){var o=e.indexOf("?"),i=e.indexOf("#");-1!==i&&(-1===o||i<o)?(t=e.substring(0,i),r=c.doubleSplit(e.substring(i+1),"&","=")):-1!==o&&-1===i?(t=e.substring(0,o),n=c.doubleSplit(e.substring(o+1),"&","=")):-1!==o&&-1!==i&&(t=e.substring(0,o),n=c.doubleSplit(e.substring(o+1,i),"&","="),r=c.doubleSplit(e.substring(i+1),"&","="))}return{originAndPath:t,query:n,fragment:r}},join:function(e){var t=e.originAndPath||"";return e.query&&(t+="?"+u.join(e.query,"&","=")),e.fragment&&(t+="#"+u.join(e.fragment,"&","=")),t},appendCurrentQueryParameterIfNotExist:function(e){var t=x.parse(window.location.href);return u.forEach(t.query,(function(t,n){e=x.addIfNotExist(e,t,n)})),e},append:function(e,t){var n=x.parse(e),r=c.doubleSplit(t,"&","=");return n.query=n.query||{},u.forEach(r,(function(e,t){n.query[e]=t||null})),x.join(n)},addIfNotExist:function(e,t,n){n=n||"";var r=x.parse(e);return null===u.findOwnProperty(r.query||{},t,!0)&&(r.query=r.query||{},r.query[t.toLowerCase()]=n),x.join(r)},add:function(e,t){var n=x.parse(e);return e&&t&&t.length&&(n.query=n.query||{},l.forEach(t,(function(e){n.query[e[0]]=e[1]}))),x.join(n)},addFragment:function(e,t){var n="";if(e&&t&&t.length){(n=x.parse(e)).fragment=n.fragment||{};var r=[];l.forEach(t,(function(e){r.includes(e[0])||(n.fragment[e[0]]=e[1],r.push(e[0]))}))}return n},appendOrReplace:function(e,t,n,r){var o=x.parse(e);o.query=o.query||{};var i=u.findOwnProperty(o.query,t,!0);i&&delete o.query[i],o.query[t.toLowerCase()]=n;var a=x.join(o);return r&&a.length>r?e:a},remove:function(e,t){var n=x.parse(e);n.query=n.query||{};var r=u.findOwnProperty(n.query,t,!0);return r&&delete n.query[r],x.join(n)},extract:function(e,t){t||""===t||(t=document.location.search);var n=x.parse(t);n.query=n.query||{};var r=u.findOwnProperty(n.query,e,!0);return r?n.query[r]:""},appendOrReplaceFromCurrentUrl:function(e,t){var n=x.extract(t);return n?x.appendOrReplace(e,t,n):e},stripQueryStringAndFragment:function(e){return x.parse(e).originAndPath}},O=t.Cookies={expireDate:"Thu, 30-Oct-1980 16:00:00 GMT",persistTTLDays:390,cookieSafeRegex:/^[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]+$/,enabled:function(){var e="G"+(new Date).getTime();O.write("CkTst",e);var t=!!O.getCookie("CkTst");return O.remove("CkTst"),t},getCookies:function(){return c.doubleSplit(document.cookie,";","=",!1,c.trim)},getCookie:function(e){var t=O.getCookies();return t[e]?t[e]:null},getObject:function(e){var t=O.getCookie(e)||"";return c.doubleSplit(t,"&","=")},remove:function(e,t,n){var r=t||document.location.hostname,o=r.split("."),i=o.length,a=o[i-2]+"."+o[i-1],s=n||"/",u="https:"===document.location.protocol,l=u?";secure":"",d=O.getDefaultSameSiteAttribute(u);document.cookie=c.format("{0}= ;domain=.{1};path={2};expires={3}{4}{5}",e,a,s,O.expireDate,l,d),document.cookie=c.format("{0}= ;domain=.{1};path={2};expires={3}{4}{5}",e,r,s,O.expireDate,l,d)},write:function(e,t,n,r,o,i,a,s,u){var c=i?".":"",l=document.domain.split(".");o&&l.splice(0,Math.max(0,l.length-2));var d=c+l.join(".");O.writeWithExpiration(e,t,n,r?O.getPersistDate():null,d,a,s,u)},writeWithExpiration:function(e,t,n,o,i,a,s,l){if(""===t)O.remove(e,i);else{"object"===r(t)&&(t=u.join(t,"&","="));var d,f=o?";expires="+o:"",p=i?";domain="+i:"",h=a||"/",g=n?";secure":"";d=s&&"none"!==s.toLowerCase()?";SameSite="+s:O.getDefaultSameSiteAttribute(n);var m=c.format("{0};path={1}{2}{3}{4}",p,h,f,g,d);if(l){for(var v=4e3-m.length-e.length-1,b=Math.ceil(t.length/v),y=O.getCookies(),S=0;S<b;S++){var C=0===S?"":S.toString(),E=t.substring(S*v,(S+1)*v),w=c.format("{0}{1}={2}{3}",e,C,E,m);document.cookie=w}for(;;S++){var P=e+S.toString();if(!y[P])break;O.remove(P,i,a)}}else{var T=c.format("{0}={1}{2}",e,t,m);document.cookie=T}}},isCookieSafeValue:function(e){return O.cookieSafeRegex.test(e)},getDefaultSameSiteAttribute:function(e){return e&&P?";SameSite=None":""},getPersistDate:function(){var e=new Date;return e.setDate(e.getDate()+O.persistTTLDays),e.toUTCString()}}},function(e,t,n){var r=window;e.exports=r.Promise},function(module,exports){var JSON;JSON||(JSON={}),function(){"use strict";var global=Function("return this")(),JSON=global.JSON;function f(e){return e<10?"0"+e:e}JSON||(JSON={}),"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(e){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(e){return this.valueOf()});var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,(function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'}function str(e,t){var n,r,o,i,a,s=gap,u=t[e];switch(u&&"object"==typeof u&&"function"==typeof u.toJSON&&(u=u.toJSON(e)),"function"==typeof rep&&(u=rep.call(t,e,u)),typeof u){case"string":return quote(u);case"number":return isFinite(u)?String(u):"null";case"boolean":case"null":return String(u);case"object":if(!u)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(u)){for(i=u.length,n=0;n<i;n+=1)a[n]=str(n,u)||"null";return o=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+s+"]":"["+a.join(",")+"]",gap=s,o}if(rep&&"object"==typeof rep)for(i=rep.length,n=0;n<i;n+=1)"string"==typeof rep[n]&&(o=str(r=rep[n],u))&&a.push(quote(r)+(gap?": ":":")+o);else for(r in u)Object.prototype.hasOwnProperty.call(u,r)&&(o=str(r,u))&&a.push(quote(r)+(gap?": ":":")+o);return o=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+s+"}":"{"+a.join(",")+"}",gap=s,o}}"function"!=typeof JSON.stringify&&(JSON.stringify=function(e,t,n){var r;if(gap="",indent="","number"==typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(e,t){var n,r,o=e[t];if(o&&"object"==typeof o)for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&((r=walk(o,n))!==undefined?o[n]=r:delete o[n]);return reviver.call(e,t,o)}if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,(function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")}),global.JSON=JSON,module.exports=JSON}()},function(e,t,n){var r=window,o=r.document.documentMode,i=r.navigator;!function(){var t=null,a=i.userAgent,s=null,u=a.match(/MSIE ([^ ]+)/);if(u&&(t=parseInt(u[1])),"function"==typeof r.Symbol&&a.match(/AppleWebKit\/601/)&&(r.Symbol=null),t&&o&&t!==o&&Object.defineProperty)try{Object.defineProperty(i,"userAgent",{get:function(){return s}}),s=a.replace(/MSIE [^ ]+/,"MSIE "+o+".0"),e.exports=n(6),s=a}catch(c){e.exports=n(6)}else e.exports=n(6)}()},function(e,t){t.EventIds={Unknown:0,Event_PaginationControl_ViewSwitch:1e4,Api_GetOneTimeCode:2e4,Api_GetOneTimeToken:20001,Api_CanaryValidation:20002,Api_GetCustomCss:20003,Api_GetCredentialType:20004,Api_CheckSessionState:20005,Api_GetIwaSsoToken:20006,Api_OtcAuthentication:20007,Api_DeviceAuthentication:20008,Api_BeginOtcAuthentication:20009,Api_ConfirmOneTimeCode:20010,Api_BeginSessionApproval:20011,Api_EndSessionApproval:20012,Api_Forget:20013,Api_GetRecoveryCredentialType:20014,Redirect_Unknown:4e4,Redirect_MSASignUpPage:40001,Redirect_AADSignUpPage:40002,Redirect_SkipZeroTouch:40003,Redirect_ResetPasswordPage:40004,Redirect_MSAUserRecoveryPage:40005,Redirect_OtherIdpRedirection:40006,Redriect_SwitchUser:40007},t.EventLevel={None:0,Critical:1,Info:2,ApiRequest:4,CXH:8,Debug:16,Verbose:32,All:65535},t.HidingMode={None:0,Hide:1,Mask:2},t.DataPointScope={ClientEvent:1,Global:2},t.EventStage={None:0,Begin:1,End:2}},function(e,t,n){(function(t){e.exports=t.ko=n(13)}).call(this,n(12))},function(e,t){t.UsernameMaxLength=113,t.SATOTPV1Length=6,t.SATOTPLength=8,t.SAEOTPLength=8,t.PhoneNumberConfirmationLength=4,t.OneTimeCodeDefaultLength=16,t.OneTimeCodeMaxAcceptedLength=10,t.PCExperienceQS="pcexp",t.PCExperienceDisabled=t.PCExperienceQS+"=false",t.NotPreferredCredentialQs="npc",t.AnimationTimeout=700,t.PageSummaryVersion=1,t.GuidTemplate="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",t.proofUpCorrelationIdParamName="x-ms-correlation-id",t.Regex={PhoneNumberValidation:/^[0-9 ()[\].\-#*/+]+$/},t.ProofUpRedirectLandingView={AccountCompromised:1,RiskySession:2},t.LoginMode={None:0,Login:1,ForceCredType:3,LWAConsent:4,GenericError:5,ForceSignin:6,OTS:7,HIP_Login:8,HIP_Lockout:9,InviteBlocked:10,SwitchUser:11,LWADelegation:12,ServiceBlocked:13,IDPFailed:14,StrongAuthOTC:16,StrongAuthMobileOTC:25,Finish:27,LoginWizard_Login:28,StrongAuthWABOTC:30,LoginWizard_HIP_Login:32,LoginWizard_Finish:34,LoginMobile:36,ForceSigninMobile:37,GenericErrorMobile:38,LoginHost:39,ForceSigninHost:40,GenericErrorHost:42,StrongAuthHostOTC:43,HIP_LoginHost:45,HIP_LoginMobile:46,HIP_LockoutHost:47,HIP_LockoutMobile:48,SwitchUserHost:49,LoginXbox_Login:50,HIP_LoginXbox:51,FinishXbox:52,IfExistsXbox:53,StartIfExistsXbox:54,StrongAuthXboxOTC:55,LoginWPWiz_Login:56,LoginWPWiz_HIP_Login:57,LoginWPWiz_Finish:58,StrongAuthWizOTC:59,StrongAuthWPWizOTC:60,FinishWPWiz:61,SwitchUserMobile:62,LoginWPWiz_PhoneSignIn:63,LoginWPWiz_HIP_PhoneSignIn:64,Login_PhoneSignIn:65,Login_HIP_PhoneSignIn:66,LoginHost_PhoneSignIn:67,LoginHost_HIP_PhoneSignIn:68,LoginMobile_PhoneSignIn:69,LoginMobile_HIP_PhoneSignIn:70,LoginWizard_PhoneSignIn:71,LoginWizard_HIP_PhoneSignIn:72,LoginXbox_PhoneSignIn:73,LoginXbox_HIP_PhoneSignIn:74,LoginWin10:75,HIP_LoginWin10:76,FinishWin10:77,FinishBlockedWin10:78,LoginWin10_PhoneSignIn:79,HIP_LoginWin10_PhoneSignIn:80,FinishWin10_TokenBroker:81,SwitchUserWin10:82,ForceSignInXbox:88,LoginClientSDK_Login:92,LoginClientSDK_HIP_Login:93,LoginClientSDK_Finish:94,StrongAuthClientSDKOTC:95,FinishClientSDK:96,LoginClientSDK_PhoneSignIn:97,LoginClientSDK_HIP_PhoneSignIn:98,Win10InclusiveOOBE_Finish:99,Win10InclusiveOOBE_FinishBlocked:100,Tiles:102,RemoteConnect:103,FedConflict:105,Win10Host_Login:106,Win10Host_Login_PhoneSignin:107,Win10Host_Finish:108,Win10Host_StrongAuth:109,Win10Host_HIP_Login:110,Fido:111,Win10Host_HIP_Login_PhoneSignIn:112,FedLink:113,UserCredentialPolicyBlocked:114,BindFailed:115,Win10HostOOBE_HIP_Login:116,Win10HostOOBE_HIP_Login_PhoneSignIn:117,AadFedConflict:118,ProofFedConflict:119,FedBoundLink:120,FetchSessionsProgress:121,Win10Host_TransferLogin:122,TransferLogin:123,Signup:124,CredentialPicker:129,SignupBlocked:132,QrCodePin:133},t.LoginBody={Login_OTC:5},t.SessionPullFlags={Msa:1,Dsso:2},t.ResponseMode={Fragment:"Fragment",Query:"Query",FormPost:"FormPost",NotSpecified:"NotSpecified",Unsupported:"Unsupported"},t.ResponseType={code:"code",token:"token",id_token:"id_token",none:"none"},t.PaginatedState={Previous:-1,Unknown:0,Username:1,Password:2,OneTimeCode:3,RemoteNGC:4,PhoneDisambiguation:5,LwaConsent:6,IdpDisambiguation:7,IdpRedirect:8,ViewAgreement:10,LearnMore:11,Tiles:12,ConfirmSend:13,RemoteConnectCode:14,RemoteLoginPolling:15,BindRedirect:16,TermsOfUse:17,DesktopSsoProgress:18,ResetPasswordSplitter:19,Kmsi:20,CheckPasswordType:21,ChangePassword:22,Fido:23,CredentialPicker:24,Consent:25,Error:26,ConfirmSignup:27,ConfirmRecoverUsername:28,ConfirmConsentSelection:29,FedConflict:30,ProofUpRedirect:32,ProofUpRedirectLanding:33,ConditionalAccessInstallBroker:34,ConditionalAccessWorkplaceJoin:35,ConditionalAccessError:36,CreateFido:37,FedLink:38,FedLinkComplete:40,IdpRedirectSpeedbump:41,TransferLogin:42,Cmsi:43,ProofConfirmation:44,MessagePrompt:45,FinishError:46,Hip:48,LearnMoreOfflineAccount:49,TenantDisambiguation:50,AadFedConflict:51,RemoteConnectCanaryValidation:52,PartnerCanaryValidation:53,ProofFedConflict:54,FetchSessionsProgress:55,AccessPass:56,SignupUsername:57,ReportSuspiciousApp:58,MoreInfo:59,AuthenticatorAddAccountView:60,SignupCredentialPicker:61,LoginError:62,SearchOrganization:63,Ptca:64,GuestConsent:65,RemoteConnectLocation:66,AttributeCollection:67,RdpDevicePrompt:68,GuestConsentConnect:69,SeeHowDataIsManaged:70,SecurityDefaultsUpsell:71,SecurityDefaultsUpsellOptOut:72,SecurityDefaultsUpsellAutoEnabled:73,WebNativeBridge:74,TransferLoginChallengePin:75,RecoveryCredentialPicker:76,OneTimeCodeRecovery:77,PhoneLinkLearnMore:78,PhoneLinkSupport:79,CertificateInterstitialView:80,ConsentResourceApp:81,SignupBlocked:82,VCPresentation:83,QrCodeScan:84,QrCodePin:85,AttributeCollectionRedirect:86},t.PostType={Password:11,Federation:13,SHA1:15,StrongAuth:18,StrongAuthTOTP:19,LWAConsent:30,PasswordInline:20,RemoteNGC:21,SessionApproval:22,NGC:23,OtcNoPassword:24,RemoteConnect_NativePlatform:25,OTC:27,Kmsi:28,TransferTokenOTC:31,QrCodePin:32},t.UserProperty={USERNAME:"login",ERROR_CODE:"HR",ERR_MSG:"ErrorMessage",EXT_ERROR:"ExtErr",ERR_URL:"ErrUrl",DATOKEN:"DAToken",DA_SESKEY:"DASessionKey",DA_START:"DAStartTime",DA_EXPIRE:"DAExpires",STS_ILFT:"STSInlineFlowToken",SIGNINNAME:"SigninName",FIRST_NAME:"LastName",LAST_NAME:"FirstName",TILE_URL:"TileUrl",CID:"CID",PUID:"PUID"},t.DEFAULT_CHANNEL_ID="53ee284d-920a-4b59-9d30-a60315b26836",t.DEFAULT_PREFERRED_EXTENSION_ID="ppnbnpeolgkicgegkbkbjmhlideopiji",t.WebNativeBridgeSuccess="Success",t.DFPPrefix="dfp:",t.Error={S_OK:"0",InvalidRealmDiscLogin:10,UsernameInvalid:1e3,PasswordEmpty:1001,HIPEmpty:1002,AltEmailInvalid:1005,PhoneInvalid:1006,SAContainsName:1007,OTCEmpty:1009,OTCInvalid:1010,NotEnoughProofs:1013,PhoneEmpty:1015,FedUser:1016,FedUserConflict:1017,FedUserInviteBlocked:1018,EmptyFields:1020,PhoneHasSpecialChars:1021,AutoVerifyNoCodeSent:1022,ProofConfirmationEmpty:1023,ProofConfirmationInvalid:1024,TOTPInvalid:1025,SessionNotApproved:1026,PhoneNumberInvalid:1027,PhoneFormattingInvalid:1028,PollingTimedOut:1029,SendNotificationFailed:1030,Server_MessageOnly:9999,PP_E_DB_MEMBERDOESNOTEXIST:"CFFFFC15",PP_E_EXCLUDED:"80041010",PP_E_MEMBER_LOCKED:"80041011",PP_E_BAD_PASSWORD:"80041012",PP_E_MISSING_MEMBERNAME:"80041031",PP_E_MISSING_PASSWORD:"80041032",PP_E_FEDERATION_INLINELOGIN_DISALLOWED:"800478AC",PP_E_PE_RULEFALSE:"8004490C",PP_E_MOBILECREDS_PHONENUMBER_BLANK:"80045801",PP_E_MOBILECREDS_PHONENUMBER_TOOSHORT:"80045806",PP_E_MOBILECREDS_PHONENUMBER_TOOLONG:"80045807",PP_E_MOBILECREDS_PHONENUMBER_INVALID:"80045800",PP_E_NAME_BLANK:"80041100",PP_E_EMAIL_INCOMPLETE:"8004110D",PP_E_EMAIL_INVALID:"8004110B",PP_E_NAME_TOO_SHORT:"80041101",PP_E_NAME_INVALID:"80041103",PP_E_INVALIDARG:"80048388",PP_E_SA_TOOSHORT:"80041120",PP_E_SA_TOOLONG:"80041121",PP_E_INVALID_PHONENUMBER:"8004113F",PP_E_SECRETQ_CONTAINS_SECRETA:"80041165",PP_E_SECRETA_CONTAINS_SECRETQ:"8004117D",PP_E_SA_CONTAINS_MEMBERNAME:"8004116A",PP_E_STRONGPROCESS_ALTEMAILSAMEASMAILBOX:"80049C2D",PP_E_EMAIL_RIGHT_TOO_LONG:"8004110C",PP_E_NAME_TOO_LONG:"80041102",PP_E_ALIAS_AUTH_NOTPERMITTED:"8004788B",PP_E_TOTP_INVALID:"80049C34",PP_E_OLD_SKYPE_PASSWORD:"80043557",PP_E_OTT_DATA_INVALID:"8004348F",PP_E_OTT_ALREADY_CONSUMED:"80043490",PP_E_OTT_INVALID_PURPOSE:"80043496",PP_E_PPSA_RPT_NOTOADDRESS:"80048120",PP_E_STRONGPROCESS_BADDEVICENAME:"80049C22",PP_E_INLINELOGIN_INVALID_SMS:"800434E1",PP_E_INLINELOGIN_INVALID_ALT:"800434E2",PP_E_PREVIOUS_PASSWORD:"80041013",PP_E_HIP_VALIDATION_WRONG:"80045505",PP_E_HIP_VALIDATION_ERROR_FATAL:"80045537",PP_E_HIP_VALIDATION_ERROR_UNAUTHENTICATED:"********",PP_E_HIP_VALIDATION_ERROR_OTHER:"********",PP_E_SQ_CONTAINS_PASSWORD:"8004341E",PP_E_SA_CONTAINS_PASSWORD:"8004341C",PP_E_SA_CONTAINED_IN_PASSWORD:"8004341D",PP_E_LIBPHONENUMBERINTEROP_NUMBERPARSE_EXCEPTION:"********",PP_E_STRONGPROCESS_EMAIL_HAS_MOBILE_DOMAIN:"80049C33",PP_E_STRONGPROCESS_MXALIAS_NOTALLOWED:"80049C23",PP_E_INVALID_MEMBERNAME:"********",PP_E_SA_TOO_MANY_CACHE_SESSIONS:"8004A00C",PP_E_INTERFACE_DISABLED:"********",PP_E_ASSOCIATE_DUPLICATE_ACCOUNT:"********",PP_E_OAUTH_REMOTE_CONNECT_USER_CODE_MISSING_OR_INVALID:"800478C7",PP_E_LOGIN_NOPA_USER_PASSWORD_REQUIRED:"800478CE",PP_E_IDP_LINKEDIN_BINDING_NOT_ALLOWED:"800478D5",PP_E_IDP_GOOGLE_BINDING_NOT_ALLOWED:"800478D6",PP_E_IDP_GITHUB_BINDING_NOT_ALLOWED:"800478D7",PP_E_IDP_BINDING_EXISTS_SAMSUNG:"8004453E",PP_E_TRANSFER_TOKEN_INVALID_SESSION:"800435A0"},t.EstsError={UserAccountSelectionInvalid:"16001",UserUnauthorized:"50020",UserUnauthorizedApiVersionNotSupported:"500201",UserUnauthorizedMsaGuestUsersNotSupported:"500202",UserAccountNotFound:"50034",UserAccountDeleted:"500341",UserAlreadyExists:"1003037",UserAccountNotFoundNotConfiguredForRemoteNgc:"500342",UserAccountNotFoundFailedToCreateRemoteSignIn:"500343",UserAccountNotFoundForFidoSignIn:"500344",IdsLocked:"50053",InvalidPasswordLastPasswordUsed:"50054",InvalidPasswordExpiredPassword:"50055",InvalidPasswordNullPassword:"50056",UserDisabled:"50057",GuestUserDisabled:"500571",FlowTokenExpired:"50089",InvalidUserNameOrPassword:"50126",InvalidDomainName:"50128",ProtectedKeyMisuse:"50141",MissingCustomSigningKey:"50146",IdpLoopDetected:"50174",InvalidOneTimePasscode:"50181",ExpiredOneTimePasscode:"50182",OneTimePasscodeCacheError:"50183",OneTimePasscodeCacheErrorNoMoreOTPGenerated:"501831",OneTimePasscodeEntryNotExist:"50184",OneTimePasscodeMessageDeliveryFailed:"50185",InvalidPassword:"50193",InvalidOneTimePasscodeOTPNotGiven:"501811",InvalidGrantDeviceNotFound:"700003",SsoArtifactExpiredDueToConditionalAccess:"70044",SsoArtifactExpiredDueToConditionalAccessReAuth:"70046",InvalidTenantName:"90002",InvalidTenantNameEmptyGuidIdentifier:"900021",InvalidTenantNameEmptyIdentifier:"900022",InvalidTenantNameFormat:"900023",PhoneSignInBlockedByUserCredentialPolicy:"130500",AccessPassBlockedByPolicy:"130502",InvalidAccessPass:"130503",AccessPassExpired:"130504",AccessPassAlreadyUsed:"130505",PublicIdentifierSasBeginCallRetriableError:"131001",PublicIdentifierAuthUserNotAllowedByPolicy:"131010",PublicIdentifierSasBeginCallNonRetriableError:"131002",PublicIdentifierSasEndCallRetriableError:"131003",PublicIdentifierSasEndCallNonRetriableError:"131004",DeviceIsDisabled:"135011",FidoBlockedByPolicy:"135016",PasskeyBlockedByPolicyOtherAuthAppPasskeyAvailable:"1350161",PasskeyBlockedByPolicyOtherPasskeyAvailable:"1350162",PasskeyAuthInterrupted:"1350201",BlockedAdalVersion:"220300",BlockedClientId:"220400",InvalidCredentialDueToMfaClassification:"54009",ProofupBlockedDueToMfaClassification:"54010",NoEmailAddressCollectedFromExternalOidcIDP:"901011",EmailAddressCollectedFromExternalOidcIDPNotVerified:"901012",EmailAddressCollectedFromExternalOidcIDPNotPublic:"901013",NoExternalIdentifierCollectedFromExternalOidcIDP:"901014",UserVoiceAuthFailedCallWentToVoicemail:"UserVoiceAuthFailedCallWentToVoicemail",UserVoiceAuthFailedInvalidPhoneInput:"UserVoiceAuthFailedInvalidPhoneInput",UserVoiceAuthFailedPhoneHungUp:"UserVoiceAuthFailedPhoneHungUp",UserVoiceAuthFailedInvalidPhoneNumber:"UserVoiceAuthFailedInvalidPhoneNumber",UserVoiceAuthFailedInvalidExtension:"UserVoiceAuthFailedInvalidExtension",InvalidFormat:"InvalidFormat",UserAuthFailedDuplicateRequest:"UserAuthFailedDuplicateRequest",UserVoiceAuthFailedPhoneUnreachable:"UserVoiceAuthFailedPhoneUnreachable",UserVoiceAuthFailedProviderCouldntSendCall:"UserVoiceAuthFailedProviderCouldntSendCall",User2WaySMSAuthFailedProviderCouldntSendSMS:"User2WaySMSAuthFailedProviderCouldntSendSMS",SMSAuthFailedProviderCouldntSendSMS:"SMSAuthFailedProviderCouldntSendSMS",User2WaySMSAuthFailedNoResponseTimeout:"User2WaySMSAuthFailedNoResponseTimeout",SMSAuthFailedNoResponseTimeout:"SMSAuthFailedNoResponseTimeout",SMSAuthFailedWrongCodeEntered:"SMSAuthFailedWrongCodeEntered",IncorrectOTP:"IncorrectOTP",OathCodeIncorrect:"OathCodeIncorrect",OathCodeDuplicate:"OathCodeDuplicate",OathCodeOld:"OathCodeOld",ProofDataNotFound:"ProofDataNotFound",OathCodeCorrectButDeviceNotAllowed:"OathCodeCorrectButDeviceNotAllowed",OathCodeFailedMaxAllowedRetryReached:"OathCodeFailedMaxAllowedRetryReached",InvalidSession:"InvalidSession",PhoneAppNoResponse:"PhoneAppNoResponse",User2WaySMSAuthFailedWrongCodeEntered:"User2WaySMSAuthFailedWrongCodeEntered",PhoneAppInvalidResult:"PhoneAppInvalidResult",PhoneAppDenied:"PhoneAppDenied",PhoneAppTokenChanged:"PhoneAppTokenChanged",SMSAuthFailedMaxAllowedCodeRetryReached:"SMSAuthFailedMaxAllowedCodeRetryReached",PhoneAppFraudReported:"PhoneAppFraudReported",FraudCodeEntered:"FraudCodeEntered",UserIsBlocked:"UserIsBlocked",PhoneAppEntropyIncorrect:"PhoneAppEntropyIncorrect",VoiceOTPAuthFailedWrongCodeEntered:"VoiceOTPAuthFailedWrongCodeEntered",VoiceOTPAuthFailedMaxAllowedCodeRetryReached:"VoiceOTPAuthFailedMaxAllowedCodeRetryReached",AccessPassBlockedByPolicyTfa:"AccessPassBlockedByPolicy",InvalidAccessPassTfa:"InvalidAccessPass",AccessPassExpiredTfa:"AccessPassExpired",AccessPassAlreadyUsedTfa:"AccessPassAlreadyUsed",AppLockRequiredButNotUsed:"AppLockRequiredButNotUsed",IncompatibleAppVersion:"IncompatibleAppVersion",FlowTokenExpiredTfa:"FlowTokenExpired",ApplicationUsedIsNotAnApprovedAppRequiredByConditionalAccess:"530021",BlockedByConditionalAccess:"53003",BlockedByConditionalAccessForRemoteDeviceFlow:"530033",BrokerAppNotInstalled:"50127",BrokerAppNotInstalledDeviceAuthenticationFailed:"501271",DeviceIsNotWorkplaceJoined:"50129",DeviceIsNotWorkplaceJoinedForMamApp:"501291",DeviceNotCompliant:"53000",DeviceNotCompliantBrowserNotSupported:"530001",DeviceNotCompliantDeviceCompliantRequired:"530002",DeviceNotCompliantDeviceManagementRequired:"530003",DeviceNotDomainJoined:"53001",DeviceNotDomainJoinedBrowserNotSupported:"530011",ProofUpBlockedDueToRisk:"53004",ProofUpBlockedDueToUserRisk:"53011",RemediateCompliantApp:"53009",RemediateDeviceStateManagedBrowserRequired:"530081",RemediateDeviceStateWorkplaceJoinRequired:"530082",AuthenticatorAppRegistrationRequiredInterrupt:"50203",AuthenticatorAppRegistrationEnforcementInterrupt:"502031",UserStrongAuthEnrollmentRequiredInterrupt:"50072",UserStrongAuthClientAuthNRequiredInterrupt:"50074",RequiredDeviceStateNotSupported:"9001011",AdminConsentRequired:"90094",AdminConsentRequiredRequestAccess:"90095",CertificateValidationBlockedByPolicy:"500186",IssuerHintsPropagationDelay:"2205016",TenantDoesNotSupportNativeCredentialRecovery:"500207",UserDoesNotSupportNativeCredentialRecovery:"500208",CredentialDoesNotSupportNativeRecovery:"500209",QrCodeKeyInvalidKey:"130100",InvalidRequestNonce:"140000",QrPinInvalid:"1301021",InvalidGrantQrPinChanged:"1301024"},t.Fido={MaxUserPromptLength:99,FinishStates:{Success:0,Cancel:1,Error:2,NotSupported:3},UnexpectedErrorCode:9999,EdgeErrorCodes:{SyntaxError:3,NotFoundError:8,NotSupportedError:9,InvalidAccessError:15,AbortError:20}},t.IfExistsResult={Unknown:-1,Exists:0,NotExist:1,Throttled:2,Error:4,ExistsInOtherMicrosoftIDP:5,ExistsBothIDPs:6},t.ThrottleStatus={NotThrottled:0,AadThrottled:1,MsaThrottled:2},t.DomainType={Unknown:1,Consumer:2,Managed:3,Federated:4,CloudFederated:5},t.CredentialType={None:0,Password:1,RemoteNGC:2,OneTimeCode:3,Federation:4,CloudFederation:5,OtherMicrosoftIdpFederation:6,Fido:7,GitHub:8,PublicIdentifierCode:9,LinkedIn:10,RemoteLogin:11,Google:12,AccessPass:13,Facebook:14,Certificate:15,OfflineAccount:16,VerifiableCredential:17,QrCodePin:18,NoPreferredCredential:1e3},t.RemoteNgcType={PushNotification:1,ListSessions:3},t.SessionPollingType={Image:1,Json:2},t.AgreementType={Privacy:"privacy",Tou:"tou",Impressum:"impressum",A11yConforme:"a11yConforme"},t.ApiErrorCodes={GeneralError:6e3,AuthFailure:6001,InvalidArgs:6002,Generic:8e3,Timeout:8001,Aborted:8002},t.DefaultRequestTimeout=3e4,PROOF={Type:{Email:1,AltEmail:2,SMS:3,DeviceId:4,CSS:5,SQSA:6,Certificate:7,HIP:8,Birthday:9,TOTPAuthenticator:10,RecoveryCode:11,StrongTicket:13,TOTPAuthenticatorV2:14,TwoWayVoice:15,TwoWaySMS:16,FidoKey:17,AccessPass:18,TransferToken:19,CompanionApp:21,ExternalAuth:22,ConsolidatedTelephony:23,Voice:-3}},t.ContentType={Json:"application/json; charset=utf-8",FormUrlEncoded:"application/x-www-form-urlencoded"},t.BindProvider={LinkedIn:0,GitHub:1,Google:2,Samsung:3,Facebook:4},t.PromotedAltCredFlags={None:0,GitHub:1,LinkedIn:2},t.EnvironmentName={Internal:1,TestSlice:2,FirstSlice:3},t.AnimationState={Begin:0,End:-1,RenderNewView:1,AnimateNewView:2},t.AnimationName={None:0,SlideOutNext:1,SlideInNext:2,SlideOutBack:3,SlideInBack:4},t.DialogId={None:0,FidoHelp:1,GitHubHelp:2,ConsentAppInfo:3,QrCodePinHelp:4},t.KeyCode={Tab:9,Enter:13,Escape:27,Space:32,PageUp:33,PageDown:34,End:35,Home:36,ArrowUp:38,ArrowDown:40,WinKeyLeft:91,F6:117,GamePadB:196},t.ProofOfPossession={AuthenticatorKey:"cpa",CanaryTokenKey:"canary",MethodHint:"cpa_method_hint"},t.UpgradeMigrationUXId={Invalid:0,Mojang:1},t.TransferLoginStringsVariant={Default:0,Mmx:1,MmxPhoneFirst:2,AppNameOnly:3,AppNameAndUsername:4,MmxGe:5,OutlookMobileCustom:6,TeamsMobileCustom:7},t.LayoutTemplateType={Lightbox:0,VerticalSplit:1},t.StringCustomizationPageId={ConditionalAccess:0,AttributeCollection:1,MessagePage:2,ProofUpPage:3,ErrorPage:4,LoginPage:5},t.ProofUpRedirectViewType={DefaultProofUpRedirectView:0,AuthAppProofUpRedirectView:1},t.ConfirmationInputDisplayType={None:0,Retype:1,RetypeWithReveal:2},t.SecurityDefaultsUpsellAction={None:0,Upsell:1,AutoEnable:2,AutoEnableAfterPrompt:3,ReevaluateLegacy:4,AutoEnabledNotify:5},t.Branding={DefaultBackgroundColor:"#FAF9F8"},t.CredentialDeviceType={SingleDevice:"singleDevice",MultiDevice:"multiDevice"},t.AttestationParseError={Unknown:1,InvalidAuthDataSize:2,SingleDeviceBackedUp:3,CBORDataEmpty:4},t.ExternalFederatedIdpType={Google:50,Facebook:51,Apple:200},t.CameraMode={Environment:"environment",User:"user",Back:"back"},t.SignInIdentifierTypes={UPN:0,Email:1,Username:2,CustomUsername:3}},function(e,t,n){var r=n(2);t.throwUnhandledExceptionOnRejection=function(e){e["catch"]((function(e){var t=e;e instanceof Error||(t=new Error("Unhandled Promise rejection: "+e)),setTimeout((function(){throw t}),0)}))},t.newPromiseWithTimeout=function(e,t,n){return new r((function(o,i){r.resolve(e()).then(o,i),setTimeout((function(){o(n)}),t)}))}},function(e,t,n){var r=n(5),o=n(10),i=n(0),a=n(2);function s(e){var t,s=this,u=i.DateTime.getCurrentTime(),c=e&&e.fEnableClientTelemetry&&e.iClientLogLevel,l=null;function d(e){return function(){if(l)return l[e].apply(l,arguments)}}s.createLoadClientTracingPromise=function(){return new a((function(t){c&&!l?n.e(0).then(function(){var r=n(25).getInstance(e,u);l||(l=r),t()}.bind(null,n))["catch"](n.oe):t()}))},s.logRedirection=function(e,t){var n=e,r=null;return e&&"string"!=typeof e?(n=e.url,r=e.eventOptions,t=e.traceParameters?t:null,e.traceUrl&&(t?t.url=n:t=n)):t=null,r&&r.eventId&&s.logEvent({eventType:"onRedirect",eventId:r.eventId,eventLevel:r.eventLevel,eventArgs:t,eventOptions:r}),n},s.getPropertyLogOption=function(e,t){return(t=t||{}).hasOwnProperty("tracingPropertyChange")||(t.tracingPropertyChange=!0),t.eventLevel=t.eventLevel||r.EventLevel.Info,{viewModel:e,tracingOptions:t}},s.getDefaultTextBoxPropertyLogOption=function(e,t){return(t=t||{}).hasOwnProperty("hidingMode")||(t.hidingMode=r.HidingMode.None),t.rateLimit={method:"notifyWhenChangesStop"},s.getPropertyLogOption(e,t)},s.getPIITextBoxPropertyLogOption=function(e,t){return(t=t||{}).hidingMode=r.HidingMode.Mask,s.getDefaultTextBoxPropertyLogOption(e,t)},s.getPasswordTextBoxPropertyLogOption=function(e,t){return(t=t||{}).hidingMode=r.HidingMode.Hide,s.getDefaultTextBoxPropertyLogOption(e,t)},s.getDefaultEventTracingOptions=function(e,t,n){return{eventId:e,eventLevel:n||r.EventLevel.Info,hidingMode:t?r.HidingMode.None:r.HidingMode.Hide}},s.attachViewLoadClientTracingOptions=(t="attachViewLoadClientTracingOptions",function(){if(o)return o[t].apply(o,arguments)}),s.logEvent=d("logEvent"),s.logUserInteractionEvent=d("logUserInteractionEvent"),s.traceBeginRequest=d("traceBeginRequest"),s.traceEndRequest=function(e,t,n,r,o){l?l.traceEndRequest(e,t,n,r,o):o&&o()},s.setPageViewModel=d("setPageViewModel"),s.logComponentEvent=d("logComponentEvent"),s.logViewState=d("logViewState"),s.setViewViewModel=d("setViewViewModel"),s.switchView=d("switchView"),s.postEvent=d("postEvent")}var u=null;t.getInstance=function(e){return u=u||new s(e)}},function(e,t,n){var r=n(0),o=n(5),i={},a=null;t.setDataPoint=function(e,t,n,r){var i={scope:r||o.DataPointScope.ClientEvent},a=s(e);a.tracingDataPoints=a.tracingDataPoints||{},a.tracingDataPoints[t]={options:i,value:function(){return n}}};var s=t.getTracingContextObject=function(e){return e?(i[e]||(i[e]={}),i[e]):a=a||{}};t.getTracingContextObjects=function(){var e=[];return r.Object.forEach(i,(function(t,n){n&&e.push({viewModel:t,context:n})})),a&&e.push(a),e},t.registerTracingObservables=function(e,t,n){var r=s(e);r.tracingObservables=r.tracingObservables||[],r.tracingObservables.push({options:n,value:t})},t.deleteTracingContextObject=function(e){e?i[e]&&delete i[e]:a=null},t.attachViewLoadClientTracingOptions=function(e,t){s(e).viewLoadClientTracingOptions=t}},function(e,t,n){var r=window,o=n(4),i=n(14),a=n(18);i.applyExtensions(o),o.utils.registerEventHandler(r,"load",(function(){document.body.appendChild(document.createElement("div")).innerHTML=n(24),o.applyBindings(new a(r.ServerData))}))},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){var r,o,i;
/*!
 * Knockout JavaScript library v3.5.1
 * (c) The Knockout.js team - http://knockoutjs.com/
 * License: MIT (http://www.opensource.org/licenses/mit-license.php)
 */!function(a){var s=this||(0,eval)("this"),u=s.document,c=s.navigator,l=s.jQuery,d=s.JSON;l||"undefined"==typeof jQuery||(l=jQuery),function(a){o=[t,n],(i="function"==typeof(r=a)?r.apply(t,o):r)===undefined||(e.exports=i)}((function(e,t){function n(e,t){return(null===e||typeof e in C)&&e===t}function r(e,t){var n;return function(){n||(n=S.a.setTimeout((function(){n=a,e()}),t))}}function o(e,t){var n;return function(){clearTimeout(n),n=S.a.setTimeout(e,t)}}function i(e,t){t&&"change"!==t?"beforeChange"===t?this.pc(e):this.gb(e,t):this.qc(e)}function f(e,t){null!==t&&t.s&&t.s()}function p(e,t){var n=this.qd,r=n[A];r.ra||(this.Qb&&this.mb[t]?(n.uc(t,e,this.mb[t]),this.mb[t]=null,--this.Qb):r.I[t]||n.uc(t,e,r.J?{da:e}:n.$c(e)),e.Ja&&e.gd())}var h,g,m,v,b,y,S=void 0!==e?e:{};S.b=function(e,t){for(var n=e.split("."),r=S,o=0;o<n.length-1;o++)r=r[n[o]];r[n[n.length-1]]=t},S.L=function(e,t,n){e[t]=n},S.version="3.5.1",S.b("version",S.version),S.options={deferUpdates:!1,useOnlyNativeEvents:!1,foreachHidesDestroyed:!1},S.a=function(){function e(e,t){for(var n in e)o.call(e,n)&&t(n,e[n])}function t(e,t){if(t)for(var n in t)o.call(t,n)&&(e[n]=t[n]);return e}function n(e,t){return e.__proto__=t,e}function r(e,t,n,r){var o=e[t].match(b)||[];S.a.D(n.match(b),(function(e){S.a.Na(o,e,r)})),e[t]=o.join(" ")}var o=Object.prototype.hasOwnProperty,i={__proto__:[]}instanceof Array,f="function"==typeof Symbol,p={},h={};p[c&&/Firefox\/2/i.test(c.userAgent)?"KeyboardEvent":"UIEvents"]=["keyup","keydown","keypress"],p.MouseEvents="click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave".split(" "),e(p,(function(e,t){if(t.length)for(var n=0,r=t.length;n<r;n++)h[t[n]]=e}));var g,m={propertychange:!0},v=u&&function(){for(var e=3,t=u.createElement("div"),n=t.getElementsByTagName("i");t.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e",n[0];);return 4<e?e:a}(),b=/\S+/g;return{Jc:["authenticity_token",/^__RequestVerificationToken(_.*)?$/],D:function(e,t,n){for(var r=0,o=e.length;r<o;r++)t.call(n,e[r],r,e)},A:"function"==typeof Array.prototype.indexOf?function(e,t){return Array.prototype.indexOf.call(e,t)}:function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},Lb:function(e,t,n){for(var r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return e[r];return a},Pa:function(e,t){var n=S.a.A(e,t);0<n?e.splice(n,1):0===n&&e.shift()},wc:function(e){var t=[];return e&&S.a.D(e,(function(e){0>S.a.A(t,e)&&t.push(e)})),t},Mb:function(e,t,n){var r=[];if(e)for(var o=0,i=e.length;o<i;o++)r.push(t.call(n,e[o],o));return r},jb:function(e,t,n){var r=[];if(e)for(var o=0,i=e.length;o<i;o++)t.call(n,e[o],o)&&r.push(e[o]);return r},Nb:function(e,t){if(t instanceof Array)e.push.apply(e,t);else for(var n=0,r=t.length;n<r;n++)e.push(t[n]);return e},Na:function(e,t,n){var r=S.a.A(S.a.bc(e),t);0>r?n&&e.push(t):n||e.splice(r,1)},Ba:i,extend:t,setPrototypeOf:n,Ab:i?n:t,P:e,Ga:function(e,t,n){if(!e)return e;var r,i={};for(r in e)o.call(e,r)&&(i[r]=t.call(n,e[r],r,e));return i},Tb:function(e){for(;e.firstChild;)S.removeNode(e.firstChild)},Yb:function(e){for(var t=((e=S.a.la(e))[0]&&e[0].ownerDocument||u).createElement("div"),n=0,r=e.length;n<r;n++)t.appendChild(S.oa(e[n]));return t},Ca:function(e,t){for(var n=0,r=e.length,o=[];n<r;n++){var i=e[n].cloneNode(!0);o.push(t?S.oa(i):i)}return o},va:function(e,t){if(S.a.Tb(e),t)for(var n=0,r=t.length;n<r;n++)e.appendChild(t[n])},Xc:function(e,t){var n=e.nodeType?[e]:e;if(0<n.length){for(var r=n[0],o=r.parentNode,i=0,a=t.length;i<a;i++)o.insertBefore(t[i],r);for(i=0,a=n.length;i<a;i++)S.removeNode(n[i])}},Ua:function(e,t){if(e.length){for(t=8===t.nodeType&&t.parentNode||t;e.length&&e[0].parentNode!==t;)e.splice(0,1);for(;1<e.length&&e[e.length-1].parentNode!==t;)e.length--;if(1<e.length){var n=e[0],r=e[e.length-1];for(e.length=0;n!==r;)e.push(n),n=n.nextSibling;e.push(r)}}return e},Zc:function(e,t){7>v?e.setAttribute("selected",t):e.selected=t},Db:function(e){return null===e||e===a?"":e.trim?e.trim():e.toString().replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")},Ud:function(e,t){return e=e||"",!(t.length>e.length)&&e.substring(0,t.length)===t},vd:function(e,t){if(e===t)return!0;if(11===e.nodeType)return!1;if(t.contains)return t.contains(1!==e.nodeType?e.parentNode:e);if(t.compareDocumentPosition)return 16==(16&t.compareDocumentPosition(e));for(;e&&e!=t;)e=e.parentNode;return!!e},Sb:function(e){return S.a.vd(e,e.ownerDocument.documentElement)},kd:function(e){return!!S.a.Lb(e,S.a.Sb)},R:function(e){return e&&e.tagName&&e.tagName.toLowerCase()},Ac:function(e){return S.onError?function(){try{return e.apply(this,arguments)}catch(t){throw S.onError&&S.onError(t),t}}:e},setTimeout:function(e,t){return setTimeout(S.a.Ac(e),t)},Gc:function(e){setTimeout((function(){throw S.onError&&S.onError(e),e}),0)},B:function(e,t,n){var r=S.a.Ac(n);if(n=m[t],S.options.useOnlyNativeEvents||n||!l)if(n||"function"!=typeof e.addEventListener){if("undefined"==typeof e.attachEvent)throw Error("Browser doesn't support addEventListener or attachEvent");var o=function(t){r.call(e,t)},i="on"+t;e.attachEvent(i,o),S.a.K.za(e,(function(){e.detachEvent(i,o)}))}else e.addEventListener(t,r,!1);else g||(g="function"==typeof l(e).on?"on":"bind"),l(e)[g](t,r)},Fb:function(e,t){if(!e||!e.nodeType)throw Error("element must be a DOM node when calling triggerEvent");var n;if(n=!("input"!==S.a.R(e)||!e.type||"click"!=t.toLowerCase())&&("checkbox"==(n=e.type)||"radio"==n),S.options.useOnlyNativeEvents||!l||n)if("function"==typeof u.createEvent){if("function"!=typeof e.dispatchEvent)throw Error("The supplied element doesn't support dispatchEvent");(n=u.createEvent(h[t]||"HTMLEvents")).initEvent(t,!0,!0,s,0,0,0,0,0,!1,!1,!1,!1,0,e),e.dispatchEvent(n)}else if(n&&e.click)e.click();else{if("undefined"==typeof e.fireEvent)throw Error("Browser doesn't support triggering events");e.fireEvent("on"+t)}else l(e).trigger(t)},f:function(e){return S.O(e)?e():e},bc:function(e){return S.O(e)?e.v():e},Eb:function(e,t,n){var o;t&&("object"==typeof e.classList?(o=e.classList[n?"add":"remove"],S.a.D(t.match(b),(function(t){o.call(e.classList,t)}))):"string"==typeof e.className.baseVal?r(e.className,"baseVal",t,n):r(e,"className",t,n))},Bb:function(e,t){var n=S.a.f(t);null!==n&&n!==a||(n="");var r=S.h.firstChild(e);!r||3!=r.nodeType||S.h.nextSibling(r)?S.h.va(e,[e.ownerDocument.createTextNode(n)]):r.data=n,S.a.Ad(e)},Yc:function(e,t){if(e.name=t,7>=v)try{var r=e.name.replace(/[&<>'"]/g,(function(e){return"&#"+e.charCodeAt(0)+";"}));e.mergeAttributes(u.createElement("<input name='"+r+"'/>"),!1)}catch(n){}},Ad:function(e){9<=v&&(e=1==e.nodeType?e:e.parentNode).style&&(e.style.zoom=e.style.zoom)},wd:function(e){if(v){var t=e.style.width;e.style.width=0,e.style.width=t}},Pd:function(e,t){e=S.a.f(e),t=S.a.f(t);for(var n=[],r=e;r<=t;r++)n.push(r);return n},la:function(e){for(var t=[],n=0,r=e.length;n<r;n++)t.push(e[n]);return t},Da:function(e){return f?Symbol(e):e},Zd:6===v,$d:7===v,W:v,Lc:function(e,t){for(var n=S.a.la(e.getElementsByTagName("input")).concat(S.a.la(e.getElementsByTagName("textarea"))),r="string"==typeof t?function(e){return e.name===t}:function(e){return t.test(e.name)},o=[],i=n.length-1;0<=i;i--)r(n[i])&&o.push(n[i]);return o},Nd:function(e){return"string"==typeof e&&(e=S.a.Db(e))?d&&d.parse?d.parse(e):new Function("return "+e)():null},hc:function(e,t,n){if(!d||!d.stringify)throw Error("Cannot find JSON.stringify(). Some browsers (e.g., IE < 8) don't support it natively, but you can overcome this by adding a script reference to json2.js, downloadable from http://www.json.org/json2.js");return d.stringify(S.a.f(e),t,n)},Od:function(t,n,r){var o=(r=r||{}).params||{},i=r.includeFields||this.Jc,a=t;if("object"==typeof t&&"form"===S.a.R(t)){a=t.action;for(var s=i.length-1;0<=s;s--)for(var c=S.a.Lc(t,i[s]),l=c.length-1;0<=l;l--)o[c[l].name]=c[l].value}n=S.a.f(n);var d=u.createElement("form");for(var f in d.style.display="none",d.action=a,d.method="post",n)(t=u.createElement("input")).type="hidden",t.name=f,t.value=S.a.hc(S.a.f(n[f])),d.appendChild(t);e(o,(function(e,t){var n=u.createElement("input");n.type="hidden",n.name=e,n.value=t,d.appendChild(n)})),u.body.appendChild(d),r.submitter?r.submitter(d):d.submit(),setTimeout((function(){d.parentNode.removeChild(d)}),0)}}}(),S.b("utils",S.a),S.b("utils.arrayForEach",S.a.D),S.b("utils.arrayFirst",S.a.Lb),S.b("utils.arrayFilter",S.a.jb),S.b("utils.arrayGetDistinctValues",S.a.wc),S.b("utils.arrayIndexOf",S.a.A),S.b("utils.arrayMap",S.a.Mb),S.b("utils.arrayPushAll",S.a.Nb),S.b("utils.arrayRemoveItem",S.a.Pa),S.b("utils.cloneNodes",S.a.Ca),S.b("utils.createSymbolOrString",S.a.Da),S.b("utils.extend",S.a.extend),S.b("utils.fieldsIncludedWithJsonPost",S.a.Jc),S.b("utils.getFormFields",S.a.Lc),S.b("utils.objectMap",S.a.Ga),S.b("utils.peekObservable",S.a.bc),S.b("utils.postJson",S.a.Od),S.b("utils.parseJson",S.a.Nd),S.b("utils.registerEventHandler",S.a.B),S.b("utils.stringifyJson",S.a.hc),S.b("utils.range",S.a.Pd),S.b("utils.toggleDomNodeCssClass",S.a.Eb),S.b("utils.triggerEvent",S.a.Fb),S.b("utils.unwrapObservable",S.a.f),S.b("utils.objectForEach",S.a.P),S.b("utils.addOrRemoveItem",S.a.Na),S.b("utils.setTextContent",S.a.Bb),S.b("unwrap",S.a.f),Function.prototype.bind||(Function.prototype.bind=function(e){var t=this;if(1===arguments.length)return function(){return t.apply(e,arguments)};var n=Array.prototype.slice.call(arguments,1);return function(){var r=n.slice(0);return r.push.apply(r,arguments),t.apply(e,r)}}),S.a.g=new function(){var e,t,n=0,r="__ko__"+(new Date).getTime(),o={};return S.a.W?(e=function(e,t){var i=e[r];if(!i||"null"===i||!o[i]){if(!t)return a;i=e[r]="ko"+n++,o[i]={}}return o[i]},t=function(e){var t=e[r];return!!t&&(delete o[t],e[r]=null,!0)}):(e=function(e,t){var n=e[r];return!n&&t&&(n=e[r]={}),n},t=function(e){return!!e[r]&&(delete e[r],!0)}),{get:function(t,n){var r=e(t,!1);return r&&r[n]},set:function(t,n,r){(t=e(t,r!==a))&&(t[n]=r)},Ub:function(t,n,r){return(t=e(t,!0))[n]||(t[n]=r)},clear:t,Z:function(){return n+++r}}},S.b("utils.domData",S.a.g),S.b("utils.domData.clear",S.a.g.clear),S.a.K=new function(){function e(e,t){var n=S.a.g.get(e,r);return n===a&&t&&(n=[],S.a.g.set(e,r,n)),n}function t(t){if(r=e(t,!1))for(var r=r.slice(0),o=0;o<r.length;o++)r[o](t);S.a.g.clear(t),S.a.K.cleanExternalData(t),i[t.nodeType]&&n(t.childNodes,!0)}function n(e,n){for(var r,o=[],i=0;i<e.length;i++)if((!n||8===e[i].nodeType)&&(t(o[o.length]=r=e[i]),e[i]!==r))for(;i--&&-1==S.a.A(o,e[i]););}var r=S.a.g.Z(),o={1:!0,8:!0,9:!0},i={1:!0,9:!0};return{za:function(t,n){if("function"!=typeof n)throw Error("Callback must be a function");e(t,!0).push(n)},yb:function(t,n){var o=e(t,!1);o&&(S.a.Pa(o,n),0==o.length&&S.a.g.set(t,r,a))},oa:function(e){return S.u.G((function(){o[e.nodeType]&&(t(e),i[e.nodeType]&&n(e.getElementsByTagName("*")))})),e},removeNode:function(e){S.oa(e),e.parentNode&&e.parentNode.removeChild(e)},cleanExternalData:function(e){l&&"function"==typeof l.cleanData&&l.cleanData([e])}}},S.oa=S.a.K.oa,S.removeNode=S.a.K.removeNode,S.b("cleanNode",S.oa),S.b("removeNode",S.removeNode),S.b("utils.domNodeDisposal",S.a.K),S.b("utils.domNodeDisposal.addDisposeCallback",S.a.K.za),S.b("utils.domNodeDisposal.removeDisposeCallback",S.a.K.yb),h=[0,"",""],b={thead:g=[1,"<table>","</table>"],tbody:g,tfoot:g,tr:[2,"<table><tbody>","</tbody></table>"],td:m=[3,"<table><tbody><tr>","</tr></tbody></table>"],th:m,option:v=[1,"<select multiple='multiple'>","</select>"],optgroup:v},y=8>=S.a.W,S.a.ua=function(e,t){var n;if(l){if(l.parseHTML)n=l.parseHTML(e,t)||[];else if((n=l.clean([e],t))&&n[0]){for(var r=n[0];r.parentNode&&11!==r.parentNode.nodeType;)r=r.parentNode;r.parentNode&&r.parentNode.removeChild(r)}}else{(n=t)||(n=u),r=n.parentWindow||n.defaultView||s;var o,i=S.a.Db(e).toLowerCase(),a=n.createElement("div");for(o=(i=i.match(/^(?:\x3c!--.*?--\x3e\s*?)*?<([a-z]+)[\s>]/))&&b[i[1]]||h,i=o[0],o="ignored<div>"+o[1]+e+o[2]+"</div>","function"==typeof r.innerShiv?a.appendChild(r.innerShiv(o)):(y&&n.body.appendChild(a),a.innerHTML=o,y&&a.parentNode.removeChild(a));i--;)a=a.lastChild;n=S.a.la(a.lastChild.childNodes)}return n},S.a.Md=function(e,t){var n=S.a.ua(e,t);return n.length&&n[0].parentElement||S.a.Yb(n)},S.a.fc=function(e,t){if(S.a.Tb(e),null!==(t=S.a.f(t))&&t!==a)if("string"!=typeof t&&(t=t.toString()),l)l(e).html(t);else for(var n=S.a.ua(t,e.ownerDocument),r=0;r<n.length;r++)e.appendChild(n[r])},S.b("utils.parseHtmlFragment",S.a.ua),S.b("utils.setHtml",S.a.fc),S.aa=function(){var e={};return{Xb:function(t){if("function"!=typeof t)throw Error("You can only pass a function to ko.memoization.memoize()");var n=(4294967296*(1+Math.random())|0).toString(16).substring(1)+(4294967296*(1+Math.random())|0).toString(16).substring(1);return e[n]=t,"\x3c!--[ko_memo:"+n+"]--\x3e"},bd:function(t,n){var r=e[t];if(r===a)throw Error("Couldn't find any memo with ID "+t+". Perhaps it's already been unmemoized.");try{return r.apply(null,n||[]),!0}finally{delete e[t]}},cd:function(e,t){var n=[];!function s(e,t){if(e)if(8==e.nodeType)null!=(n=S.aa.Uc(e.nodeValue))&&t.push({ud:e,Kd:n});else if(1==e.nodeType)for(var n=0,r=e.childNodes,o=r.length;n<o;n++)s(r[n],t)}(e,n);for(var r=0,o=n.length;r<o;r++){var i=n[r].ud,a=[i];t&&S.a.Nb(a,t),S.aa.bd(n[r].Kd,a),i.nodeValue="",i.parentNode&&i.parentNode.removeChild(i)}},Uc:function(e){return(e=e.match(/^\[ko_memo\:(.*?)\]$/))?e[1]:null}}}(),S.b("memoization",S.aa),S.b("memoization.memoize",S.aa.Xb),S.b("memoization.unmemoize",S.aa.bd),S.b("memoization.parseMemoText",S.aa.Uc),S.b("memoization.unmemoizeDomNodeAndDescendants",S.aa.cd),S.na=function(){function e(){if(r)for(var e,t=r,o=0;i<r;)if(e=n[i++]){if(i>t){if(5e3<=++o){i=r,S.a.Gc(Error("'Too much recursion' after processing "+o+" task groups."));break}t=r}try{e()}catch(a){S.a.Gc(a)}}}function t(){e(),i=r=n.length=0}var n=[],r=0,o=1,i=0;return{scheduler:s.MutationObserver?function(e){var t=u.createElement("div");return new MutationObserver(e).observe(t,{attributes:!0}),function(){t.classList.toggle("foo")}}(t):u&&"onreadystatechange"in u.createElement("script")?function(e){var t=u.createElement("script");t.onreadystatechange=function(){t.onreadystatechange=null,u.documentElement.removeChild(t),t=null,e()},u.documentElement.appendChild(t)}:function(e){setTimeout(e,0)},zb:function(e){return r||S.na.scheduler(t),n[r++]=e,o++},cancel:function(e){(e-=o-r)>=i&&e<r&&(n[e]=null)},resetForTesting:function(){var e=r-i;return i=r=n.length=0,e},Sd:e}}(),S.b("tasks",S.na),S.b("tasks.schedule",S.na.zb),S.b("tasks.runEarly",S.na.Sd),S.Ta={throttle:function(e,t){e.throttleEvaluation=t;var n=null;return S.$({read:e,write:function(r){clearTimeout(n),n=S.a.setTimeout((function(){e(r)}),t)}})},rateLimit:function(e,t){var n,i,a;"number"==typeof t?n=t:(n=t.timeout,i=t.method),e.Hb=!1,a="function"==typeof i?i:"notifyWhenChangesStop"==i?o:r,e.ub((function(e){return a(e,n,t)}))},deferred:function(e,t){if(!0!==t)throw Error("The 'deferred' extender only accepts the value 'true', because it is not supported to turn deferral off once enabled.");e.Hb||(e.Hb=!0,e.ub((function(t){var n,r=!1;return function(){if(!r){S.na.cancel(n),n=S.na.zb(t);try{r=!0,e.notifySubscribers(a,"dirty")}finally{r=!1}}}})))},notify:function(e,t){e.equalityComparer="always"==t?null:n}};var C={undefined:1,boolean:1,number:1,string:1};S.b("extenders",S.Ta),S.ic=function(e,t,n){this.da=e,this.lc=t,this.mc=n,this.Ib=!1,this.fb=this.Jb=null,S.L(this,"dispose",this.s),S.L(this,"disposeWhenNodeIsRemoved",this.l)},S.ic.prototype.s=function(){this.Ib||(this.fb&&S.a.K.yb(this.Jb,this.fb),this.Ib=!0,this.mc(),this.da=this.lc=this.mc=this.Jb=this.fb=null)},S.ic.prototype.l=function(e){this.Jb=e,S.a.K.za(e,this.fb=this.s.bind(this))},S.T=function(){S.a.Ab(this,E),E.qb(this)};var E={qb:function(e){e.U={change:[]},e.sc=1},subscribe:function(e,t,n){var r=this;n=n||"change";var o=new S.ic(r,t?e.bind(t):e,(function(){S.a.Pa(r.U[n],o),r.hb&&r.hb(n)}));return r.Qa&&r.Qa(n),r.U[n]||(r.U[n]=[]),r.U[n].push(o),o},notifySubscribers:function(e,t){if("change"===(t=t||"change")&&this.Gb(),this.Wa(t)){var n="change"===t&&this.ed||this.U[t].slice(0);try{S.u.xc();for(var r,o=0;r=n[o];++o)r.Ib||r.lc(e)}finally{S.u.end()}}},ob:function(){return this.sc},Dd:function(e){return this.ob()!==e},Gb:function(){++this.sc},ub:function(e){var t,n,r,o,a,s=this,u=S.O(s);s.gb||(s.gb=s.notifySubscribers,s.notifySubscribers=i);var c=e((function(){s.Ja=!1,u&&o===s&&(o=s.nc?s.nc():s());var e=n||a&&s.sb(r,o);a=n=t=!1,e&&s.gb(r=o)}));s.qc=function(e,n){n&&s.Ja||(a=!n),s.ed=s.U.change.slice(0),s.Ja=t=!0,o=e,c()},s.pc=function(e){t||(r=e,s.gb(e,"beforeChange"))},s.rc=function(){a=!0},s.gd=function(){s.sb(r,s.v(!0))&&(n=!0)}},Wa:function(e){return this.U[e]&&this.U[e].length},Bd:function(e){if(e)return this.U[e]&&this.U[e].length||0;var t=0;return S.a.P(this.U,(function(e,n){"dirty"!==e&&(t+=n.length)})),t},sb:function(e,t){return!this.equalityComparer||!this.equalityComparer(e,t)},toString:function(){return"[object Object]"},extend:function(e){var t=this;return e&&S.a.P(e,(function(e,n){var r=S.Ta[e];"function"==typeof r&&(t=r(t,n)||t)})),t}};S.L(E,"init",E.qb),S.L(E,"subscribe",E.subscribe),S.L(E,"extend",E.extend),S.L(E,"getSubscriptionsCount",E.Bd),S.a.Ba&&S.a.setPrototypeOf(E,Function.prototype),S.T.fn=E,S.Qc=function(e){return null!=e&&"function"==typeof e.subscribe&&"function"==typeof e.notifySubscribers},S.b("subscribable",S.T),S.b("isSubscribable",S.Qc),S.S=S.u=function(){function e(e){r.push(n),n=e}function t(){n=r.pop()}var n,r=[],o=0;return{xc:e,end:t,cc:function(e){if(n){if(!S.Qc(e))throw Error("Only subscribable things can act as dependencies");n.od.call(n.pd,e,e.fd||(e.fd=++o))}},G:function(n,r,o){try{return e(),n.apply(r,o||[])}finally{t()}},qa:function(){if(n)return n.o.qa()},Va:function(){if(n)return n.o.Va()},Ya:function(){if(n)return n.Ya},o:function(){if(n)return n.o}}}(),S.b("computedContext",S.S),S.b("computedContext.getDependenciesCount",S.S.qa),S.b("computedContext.getDependencies",S.S.Va),S.b("computedContext.isInitial",S.S.Ya),S.b("computedContext.registerDependency",S.S.cc),S.b("ignoreDependencies",S.Yd=S.u.G);var w=S.a.Da("_latestValue");S.ta=function(e){function t(){return 0<arguments.length?(t.sb(t[w],arguments[0])&&(t.ya(),t[w]=arguments[0],t.xa()),this):(S.u.cc(t),t[w])}return t[w]=e,S.a.Ba||S.a.extend(t,S.T.fn),S.T.fn.qb(t),S.a.Ab(t,P),S.options.deferUpdates&&S.Ta.deferred(t,!0),t};var P={equalityComparer:n,v:function(){return this[w]},xa:function(){this.notifySubscribers(this[w],"spectate"),this.notifySubscribers(this[w])},ya:function(){this.notifySubscribers(this[w],"beforeChange")}};S.a.Ba&&S.a.setPrototypeOf(P,S.T.fn);var T=S.ta.Ma="__ko_proto__";P[T]=S.ta,S.O=function(e){if((e="function"==typeof e&&e[T])&&e!==P[T]&&e!==S.o.fn[T])throw Error("Invalid object that looks like an observable; possibly from another Knockout instance");return!!e},S.Za=function(e){return"function"==typeof e&&(e[T]===P[T]||e[T]===S.o.fn[T]&&e.Nc)},S.b("observable",S.ta),S.b("isObservable",S.O),S.b("isWriteableObservable",S.Za),S.b("isWritableObservable",S.Za),S.b("observable.fn",P),S.L(P,"peek",P.v),S.L(P,"valueHasMutated",P.xa),S.L(P,"valueWillMutate",P.ya),S.Ha=function(e){if("object"!=typeof(e=e||[])||!("length"in e))throw Error("The argument passed when initializing an observable array must be an array, or null, or undefined.");return e=S.ta(e),S.a.Ab(e,S.Ha.fn),e.extend({trackArrayChanges:!0})},S.Ha.fn={remove:function(e){for(var t=this.v(),n=[],r="function"!=typeof e||S.O(e)?function(t){return t===e}:e,o=0;o<t.length;o++){var i=t[o];if(r(i)){if(0===n.length&&this.ya(),t[o]!==i)throw Error("Array modified during remove; cannot remove item");n.push(i),t.splice(o,1),o--}}return n.length&&this.xa(),n},removeAll:function(e){if(e===a){var t=this.v(),n=t.slice(0);return this.ya(),t.splice(0,t.length),this.xa(),n}return e?this.remove((function(t){return 0<=S.a.A(e,t)})):[]},destroy:function(e){var t=this.v(),n="function"!=typeof e||S.O(e)?function(t){return t===e}:e;this.ya();for(var r=t.length-1;0<=r;r--){var o=t[r];n(o)&&(o._destroy=!0)}this.xa()},destroyAll:function(e){return e===a?this.destroy((function(){return!0})):e?this.destroy((function(t){return 0<=S.a.A(e,t)})):[]},indexOf:function(e){var t=this();return S.a.A(t,e)},replace:function(e,t){var n=this.indexOf(e);0<=n&&(this.ya(),this.v()[n]=t,this.xa())},sorted:function(e){var t=this().slice(0);return e?t.sort(e):t.sort()},reversed:function(){return this().slice(0).reverse()}},S.a.Ba&&S.a.setPrototypeOf(S.Ha.fn,S.ta.fn),S.a.D("pop push reverse shift sort splice unshift".split(" "),(function(e){S.Ha.fn[e]=function(){var t=this.v();this.ya(),this.zc(t,e,arguments);var n=t[e].apply(t,arguments);return this.xa(),n===t?this:n}})),S.a.D(["slice"],(function(e){S.Ha.fn[e]=function(){var t=this();return t[e].apply(t,arguments)}})),S.Pc=function(e){return S.O(e)&&"function"==typeof e.remove&&"function"==typeof e.push},S.b("observableArray",S.Ha),S.b("isObservableArray",S.Pc),S.Ta.trackArrayChanges=function(e,t){function n(){function t(){if(c){var t,n=[].concat(e.v()||[]);e.Wa("arrayChange")&&((!u||1<c)&&(u=S.a.Pb(i,n,e.Ob)),t=u),i=n,u=null,c=0,t&&t.length&&e.notifySubscribers(t,"arrayChange")}}s?t():(s=!0,o=e.subscribe((function(){++c}),null,"spectate"),i=[].concat(e.v()||[]),u=null,r=e.subscribe(t))}if(e.Ob={},t&&"object"==typeof t&&S.a.extend(e.Ob,t),e.Ob.sparse=!0,!e.zc){var r,o,i,s=!1,u=null,c=0,l=e.Qa,d=e.hb;e.Qa=function(t){l&&l.call(e,t),"arrayChange"===t&&n()},e.hb=function(t){d&&d.call(e,t),"arrayChange"!==t||e.Wa("arrayChange")||(r&&r.s(),o&&o.s(),o=r=null,s=!1,i=a)},e.zc=function(e,t,n){function r(e,t,n){return o[o.length]={status:e,value:t,index:n}}if(s&&!c){var o=[],i=e.length,a=n.length,l=0;switch(t){case"push":l=i;case"unshift":for(t=0;t<a;t++)r("added",n[t],l+t);break;case"pop":l=i-1;case"shift":i&&r("deleted",e[l],l);break;case"splice":t=Math.min(Math.max(0,0>n[0]?i+n[0]:n[0]),i),i=1===a?i:Math.min(t+(n[1]||0),i),a=t+a-2,l=Math.max(i,a);for(var d=[],f=[],p=2;t<l;++t,++p)t<i&&f.push(r("deleted",e[t],t)),t<a&&d.push(r("added",n[p],t));S.a.Kc(f,d);break;default:return}u=o}}}};var A=S.a.Da("_state");S.o=S.$=function(e,t,n){function r(){if(0<arguments.length){if("function"!=typeof o)throw Error("Cannot write a value to a ko.computed unless you specify a 'write' option. If you wish to read the current value, don't pass any parameters.");return o.apply(i.nb,arguments),this}return i.ra||S.u.cc(r),(i.ka||i.J&&r.Xa())&&r.ha(),i.X}if("object"==typeof e?n=e:(n=n||{},e&&(n.read=e)),"function"!=typeof n.read)throw Error("Pass a function that returns the value of the ko.computed");var o=n.write,i={X:a,sa:!0,ka:!0,rb:!1,jc:!1,ra:!1,wb:!1,J:!1,Wc:n.read,nb:t||n.owner,l:n.disposeWhenNodeIsRemoved||n.l||null,Sa:n.disposeWhen||n.Sa,Rb:null,I:{},V:0,Ic:null};return r[A]=i,r.Nc="function"==typeof o,S.a.Ba||S.a.extend(r,S.T.fn),S.T.fn.qb(r),S.a.Ab(r,_),n.pure?(i.wb=!0,i.J=!0,S.a.extend(r,x)):n.deferEvaluation&&S.a.extend(r,O),S.options.deferUpdates&&S.Ta.deferred(r,!0),i.l&&(i.jc=!0,i.l.nodeType||(i.l=null)),i.J||n.deferEvaluation||r.ha(),i.l&&r.ja()&&S.a.K.za(i.l,i.Rb=function(){r.s()}),r};var _={equalityComparer:n,qa:function(){return this[A].V},Va:function(){var e=[];return S.a.P(this[A].I,(function(t,n){e[n.Ka]=n.da})),e},Vb:function(e){if(!this[A].V)return!1;var t=this.Va();return-1!==S.a.A(t,e)||!!S.a.Lb(t,(function(t){return t.Vb&&t.Vb(e)}))},uc:function(e,t,n){if(this[A].wb&&t===this)throw Error("A 'pure' computed must not be called recursively");this[A].I[e]=n,n.Ka=this[A].V++,n.La=t.ob()},Xa:function(){var e,t,n=this[A].I;for(e in n)if(Object.prototype.hasOwnProperty.call(n,e)&&(t=n[e],this.Ia&&t.da.Ja||t.da.Dd(t.La)))return!0},Jd:function(){this.Ia&&!this[A].rb&&this.Ia(!1)},ja:function(){var e=this[A];return e.ka||0<e.V},Rd:function(){this.Ja?this[A].ka&&(this[A].sa=!0):this.Hc()},$c:function(e){if(e.Hb){var t=e.subscribe(this.Jd,this,"dirty"),n=e.subscribe(this.Rd,this);return{da:e,s:function(){t.s(),n.s()}}}return e.subscribe(this.Hc,this)},Hc:function(){var e=this,t=e.throttleEvaluation;t&&0<=t?(clearTimeout(this[A].Ic),this[A].Ic=S.a.setTimeout((function(){e.ha(!0)}),t)):e.Ia?e.Ia(!0):e.ha(!0)},ha:function(e){var t=this[A],n=t.Sa,r=!1;if(!t.rb&&!t.ra){if(t.l&&!S.a.Sb(t.l)||n&&n()){if(!t.jc)return void this.s()}else t.jc=!1;t.rb=!0;try{r=this.zd(e)}finally{t.rb=!1}return r}},zd:function(e){var t=this[A],n=!1,r=t.wb?a:!t.V;n={qd:this,mb:t.I,Qb:t.V},S.u.xc({pd:n,od:p,o:this,Ya:r}),t.I={},t.V=0;var o=this.yd(t,n);return t.V?n=this.sb(t.X,o):(this.s(),n=!0),n&&(t.J?this.Gb():this.notifySubscribers(t.X,"beforeChange"),t.X=o,this.notifySubscribers(t.X,"spectate"),!t.J&&e&&this.notifySubscribers(t.X),this.rc&&this.rc()),r&&this.notifySubscribers(t.X,"awake"),n},yd:function(e,t){try{var n=e.Wc;return e.nb?n.call(e.nb):n()}finally{S.u.end(),t.Qb&&!e.J&&S.a.P(t.mb,f),e.sa=e.ka=!1}},v:function(e){var t=this[A];return(t.ka&&(e||!t.V)||t.J&&this.Xa())&&this.ha(),t.X},ub:function(e){S.T.fn.ub.call(this,e),this.nc=function(){return this[A].J||(this[A].sa?this.ha():this[A].ka=!1),this[A].X},this.Ia=function(e){this.pc(this[A].X),this[A].ka=!0,e&&(this[A].sa=!0),this.qc(this,!e)}},s:function(){var e=this[A];!e.J&&e.I&&S.a.P(e.I,(function(e,t){t.s&&t.s()})),e.l&&e.Rb&&S.a.K.yb(e.l,e.Rb),e.I=a,e.V=0,e.ra=!0,e.sa=!1,e.ka=!1,e.J=!1,e.l=a,e.Sa=a,e.Wc=a,this.Nc||(e.nb=a)}},x={Qa:function(e){var t=this,n=t[A];if(!n.ra&&n.J&&"change"==e){if(n.J=!1,n.sa||t.Xa())n.I=null,n.V=0,t.ha()&&t.Gb();else{var r=[];S.a.P(n.I,(function(e,t){r[t.Ka]=e})),S.a.D(r,(function(e,r){var o=n.I[e],i=t.$c(o.da);i.Ka=r,i.La=o.La,n.I[e]=i})),t.Xa()&&t.ha()&&t.Gb()}n.ra||t.notifySubscribers(n.X,"awake")}},hb:function(e){var t=this[A];t.ra||"change"!=e||this.Wa("change")||(S.a.P(t.I,(function(e,n){n.s&&(t.I[e]={da:n.da,Ka:n.Ka,La:n.La},n.s())})),t.J=!0,this.notifySubscribers(a,"asleep"))},ob:function(){var e=this[A];return e.J&&(e.sa||this.Xa())&&this.ha(),S.T.fn.ob.call(this)}},O={Qa:function(e){"change"!=e&&"beforeChange"!=e||this.v()}};S.a.Ba&&S.a.setPrototypeOf(_,S.T.fn);var I=S.ta.Ma;_[I]=S.o,S.Oc=function(e){return"function"==typeof e&&e[I]===_[I]},S.Fd=function(e){return S.Oc(e)&&e[A]&&e[A].wb},S.b("computed",S.o),S.b("dependentObservable",S.o),S.b("isComputed",S.Oc),S.b("isPureComputed",S.Fd),S.b("computed.fn",_),S.L(_,"peek",_.v),S.L(_,"dispose",_.s),S.L(_,"isActive",_.ja),S.L(_,"getDependenciesCount",_.qa),S.L(_,"getDependencies",_.Va),S.xb=function(e,t){return"function"==typeof e?S.o(e,t,{pure:!0}):((e=S.a.extend({},e)).pure=!0,S.o(e,t))},S.b("pureComputed",S.xb),function(){function e(n,r,o){if(o=o||new t,"object"!=typeof(n=r(n))||null===n||n===a||n instanceof RegExp||n instanceof Date||n instanceof String||n instanceof Number||n instanceof Boolean)return n;var i=n instanceof Array?[]:{};return o.save(n,i),function(e,t){if(e instanceof Array){for(var n=0;n<e.length;n++)t(n);"function"==typeof e.toJSON&&t("toJSON")}else for(n in e)t(n)}(n,(function(t){var s=r(n[t]);switch(typeof s){case"boolean":case"number":case"string":case"function":i[t]=s;break;case"object":case"undefined":var u=o.get(s);i[t]=u!==a?u:e(s,r,o)}})),i}function t(){this.keys=[],this.values=[]}S.ad=function(t){if(0==arguments.length)throw Error("When calling ko.toJS, pass the object you want to convert.");return e(t,(function(e){for(var t=0;S.O(e)&&10>t;t++)e=e();return e}))},S.toJSON=function(e,t,n){return e=S.ad(e),S.a.hc(e,t,n)},t.prototype={constructor:t,save:function(e,t){var n=S.a.A(this.keys,e);0<=n?this.values[n]=t:(this.keys.push(e),this.values.push(t))},get:function(e){return 0<=(e=S.a.A(this.keys,e))?this.values[e]:a}}}(),S.b("toJS",S.ad),S.b("toJSON",S.toJSON),S.Wd=function(e,t,n){function r(t){var r=S.xb(e,n).extend({ma:"always"}),o=r.subscribe((function(e){e&&(o.s(),t(e))}));return r.notifySubscribers(r.v()),o}return"function"!=typeof Promise||t?r(t.bind(n)):new Promise(r)},S.b("when",S.Wd),S.w={M:function(e){switch(S.a.R(e)){case"option":return!0===e.__ko__hasDomDataOptionValue__?S.a.g.get(e,S.c.options.$b):7>=S.a.W?e.getAttributeNode("value")&&e.getAttributeNode("value").specified?e.value:e.text:e.value;case"select":return 0<=e.selectedIndex?S.w.M(e.options[e.selectedIndex]):a;default:return e.value}},cb:function(e,t,n){switch(S.a.R(e)){case"option":"string"==typeof t?(S.a.g.set(e,S.c.options.$b,a),"__ko__hasDomDataOptionValue__"in e&&delete e.__ko__hasDomDataOptionValue__,e.value=t):(S.a.g.set(e,S.c.options.$b,t),e.__ko__hasDomDataOptionValue__=!0,e.value="number"==typeof t?t:"");break;case"select":""!==t&&null!==t||(t=a);for(var r,o=-1,i=0,s=e.options.length;i<s;++i)if((r=S.w.M(e.options[i]))==t||""===r&&t===a){o=i;break}(n||0<=o||t===a&&1<e.size)&&(e.selectedIndex=o,6===S.a.W&&S.a.setTimeout((function(){e.selectedIndex=o}),0));break;default:null!==t&&t!==a||(t=""),e.value=t}}},S.b("selectExtensions",S.w),S.b("selectExtensions.readValue",S.w.M),S.b("selectExtensions.writeValue",S.w.cb),S.m=function(){function e(e){123===(e=S.a.Db(e)).charCodeAt(0)&&(e=e.slice(1,-1));var t,n=[],a=(e+="\n,").match(r),s=[],u=0;if(1<a.length){for(var c,l=0;c=a[l];++l){var d=c.charCodeAt(0);if(44===d){if(0>=u){n.push(t&&s.length?{key:t,value:s.join("")}:{unknown:t||s.join("")}),t=u=0,s=[];continue}}else if(58===d){if(!u&&!t&&1===s.length){t=s.pop();continue}}else{if(47===d&&1<c.length&&(47===c.charCodeAt(1)||42===c.charCodeAt(1)))continue;47===d&&l&&1<c.length?(d=a[l-1].match(o))&&!i[d[0]]&&(a=(e=e.substr(e.indexOf(c)+1)).match(r),l=-1,c="/"):40===d||123===d||91===d?++u:41===d||125===d||93===d?--u:t||s.length||34!==d&&39!==d||(c=c.slice(1,-1))}s.push(c)}if(0<u)throw Error("Unbalanced parentheses, braces, or brackets")}return n}var t=["true","false","null","undefined"],n=/^(?:[$_a-z][$\w]*|(.+)(\.\s*[$_a-z][$\w]*|\[.+\]))$/i,r=RegExp("\"(?:\\\\.|[^\"])*\"|'(?:\\\\.|[^'])*'|`(?:\\\\.|[^`])*`|/\\*(?:[^*]|\\*+[^*/])*\\*+/|//.*\n|/(?:\\\\.|[^/])+/w*|[^\\s:,/][^,\"'`{}()/:[\\]]*[^\\s,\"'`{}()/:[\\]]|[^\\s]","g"),o=/[\])"'A-Za-z0-9_$]+$/,i={"in":1,"return":1,"typeof":1},a={};return{Ra:[],wa:a,ac:e,vb:function(r,o){function i(e,r){var o;if(!l){var d=S.getBindingHandler(e);if(d&&d.preprocess&&!(r=d.preprocess(r,e,i)))return;(d=a[e])&&(o=r,0<=S.a.A(t,o)?o=!1:(d=o.match(n),o=null!==d&&(d[1]?"Object("+d[1]+")"+d[2]:o)),d=o),d&&u.push("'"+("string"==typeof a[e]?a[e]:e)+"':function(_z){"+o+"=_z}")}c&&(r="function(){return "+r+" }"),s.push("'"+e+"':"+r)}var s=[],u=[],c=(o=o||{}).valueAccessors,l=o.bindingParams,d="string"==typeof r?e(r):r;return S.a.D(d,(function(e){i(e.key||e.unknown,e.value)})),u.length&&i("_ko_property_writers","{"+u.join(",")+" }"),s.join(",")},Id:function(e,t){for(var n=0;n<e.length;n++)if(e[n].key==t)return!0;return!1},eb:function(e,t,n,r,o){e&&S.O(e)?!S.Za(e)||o&&e.v()===r||e(r):(e=t.get("_ko_property_writers"))&&e[n]&&e[n](r)}}}(),S.b("expressionRewriting",S.m),S.b("expressionRewriting.bindingRewriteValidators",S.m.Ra),S.b("expressionRewriting.parseObjectLiteral",S.m.ac),S.b("expressionRewriting.preProcessBindings",S.m.vb),S.b("expressionRewriting._twoWayBindings",S.m.wa),S.b("jsonExpressionRewriting",S.m),S.b("jsonExpressionRewriting.insertPropertyAccessorsIntoJson",S.m.vb),function(){function e(e){return 8==e.nodeType&&i.test(o?e.text:e.nodeValue)}function t(e){return 8==e.nodeType&&a.test(o?e.text:e.nodeValue)}function n(n,r){for(var o=n,i=1,a=[];o=o.nextSibling;){if(t(o)&&(S.a.g.set(o,c,!0),0==--i))return a;a.push(o),e(o)&&i++}if(!r)throw Error("Cannot find closing comment tag to match: "+n.nodeValue);return null}function r(e,t){var r=n(e,t);return r?0<r.length?r[r.length-1].nextSibling:e.nextSibling:null}var o=u&&"\x3c!--test--\x3e"===u.createComment("test").text,i=o?/^\x3c!--\s*ko(?:\s+([\s\S]+))?\s*--\x3e$/:/^\s*ko(?:\s+([\s\S]+))?\s*$/,a=o?/^\x3c!--\s*\/ko\s*--\x3e$/:/^\s*\/ko\s*$/,s={ul:!0,ol:!0},c="__ko_matchedEndComment__";S.h={ea:{},childNodes:function(t){return e(t)?n(t):t.childNodes},Ea:function(t){if(e(t))for(var n=0,r=(t=S.h.childNodes(t)).length;n<r;n++)S.removeNode(t[n]);else S.a.Tb(t)},va:function(t,n){if(e(t)){S.h.Ea(t);for(var r=t.nextSibling,o=0,i=n.length;o<i;o++)r.parentNode.insertBefore(n[o],r)}else S.a.va(t,n)},Vc:function(t,n){var r;e(t)?(r=t.nextSibling,t=t.parentNode):r=t.firstChild,r?n!==r&&t.insertBefore(n,r):t.appendChild(n)},Wb:function(t,n,r){r?(r=r.nextSibling,e(t)&&(t=t.parentNode),r?n!==r&&t.insertBefore(n,r):t.appendChild(n)):S.h.Vc(t,n)},firstChild:function(n){if(e(n))return!n.nextSibling||t(n.nextSibling)?null:n.nextSibling;if(n.firstChild&&t(n.firstChild))throw Error("Found invalid end comment, as the first child of "+n);return n.firstChild},nextSibling:function(n){if(e(n)&&(n=r(n)),n.nextSibling&&t(n.nextSibling)){var o=n.nextSibling;if(t(o)&&!S.a.g.get(o,c))throw Error("Found end comment without a matching opening comment, as child of "+n);return null}return n.nextSibling},Cd:e,Vd:function(e){return(e=(o?e.text:e.nodeValue).match(i))?e[1]:null},Sc:function(n){if(s[S.a.R(n)]){var o=n.firstChild;if(o)do{if(1===o.nodeType){var i,a=null;if(i=o.firstChild)do{if(a)a.push(i);else if(e(i)){var u=r(i,!0);u?i=u:a=[i]}else t(i)&&(a=[i])}while(i=i.nextSibling);if(i=a)for(a=o.nextSibling,u=0;u<i.length;u++)a?n.insertBefore(i[u],a):n.appendChild(i[u])}}while(o=o.nextSibling)}}}}(),S.b("virtualElements",S.h),S.b("virtualElements.allowedBindings",S.h.ea),S.b("virtualElements.emptyNode",S.h.Ea),S.b("virtualElements.insertAfter",S.h.Wb),S.b("virtualElements.prepend",S.h.Vc),S.b("virtualElements.setDomNodeChildren",S.h.va),S.ga=function(){this.nd={}},S.a.extend(S.ga.prototype,{nodeHasBindings:function(e){switch(e.nodeType){case 1:return null!=e.getAttribute("data-bind")||S.j.getComponentNameForNode(e);case 8:return S.h.Cd(e);default:return!1}},getBindings:function(e,t){var n=(n=this.getBindingsString(e,t))?this.parseBindingsString(n,t,e):null;return S.j.tc(n,e,t,!1)},getBindingAccessors:function(e,t){var n=(n=this.getBindingsString(e,t))?this.parseBindingsString(n,t,e,{valueAccessors:!0}):null;return S.j.tc(n,e,t,!0)},getBindingsString:function(e){switch(e.nodeType){case 1:return e.getAttribute("data-bind");case 8:return S.h.Vd(e);default:return null}},parseBindingsString:function(e,t,n,r){try{var o,i=this.nd,a=e+(r&&r.valueAccessors||"");if(!(o=i[a])){var s,u="with($context){with($data||{}){return{"+S.m.vb(e,r)+"}}}";s=new Function("$context","$element",u),o=i[a]=s}return o(t,n)}catch(c){throw c.message="Unable to parse bindings.\nBindings value: "+e+"\nMessage: "+c.message,c}}}),S.ga.instance=new S.ga,S.b("bindingProvider",S.ga),function(){function e(e){var t=(e=S.a.g.get(e,C))&&e.N;t&&(e.N=null,t.Tc())}function t(t,n,r){this.node=t,this.yc=n,this.kb=[],this.H=!1,n.N||S.a.K.za(t,e),r&&r.N&&(r.N.kb.push(t),this.Kb=r)}function n(e){return function(){return e}}function r(e){return e()}function o(e){return S.a.Ga(S.u.G(e),(function(t,n){return function(){return e()[n]}}))}function i(e,t,r){return"function"==typeof e?o(e.bind(null,t,r)):S.a.Ga(e,n)}function c(e,t){return o(this.getBindings.bind(this,e,t))}function d(e,t){var n=S.h.firstChild(t);if(n){var r,o=S.ga.instance,i=o.preprocessNode;if(i){for(;r=n;)n=S.h.nextSibling(r),i.call(o,r);n=S.h.firstChild(t)}for(;r=n;)n=S.h.nextSibling(r),f(e,r)}S.i.ma(t,S.i.H)}function f(e,t){var n=e,r=1===t.nodeType;r&&S.h.Sc(t),(r||S.ga.instance.nodeHasBindings(t))&&(n=p(t,null,e).bindingContextForDescendants),n&&!b[S.a.R(t)]&&d(n,t)}function p(e,t,n){var o,i=S.a.g.Ub(e,C,{}),s=i.hd;if(!t){if(s)throw Error("You cannot apply bindings multiple times to the same element.");i.hd=!0}if(s||(i.context=n),i.Zb||(i.Zb={}),t&&"function"!=typeof t)o=t;else{var u=S.ga.instance,l=u.getBindingAccessors||c,d=S.$((function(){return(o=t?t(n,e):l.call(u,e,n))&&(n[g]&&n[g](),n[v]&&n[v]()),o}),null,{l:e});o&&d.ja()||(d=null)}var f,p=n;if(o){var h=function(){return S.a.Ga(d?d():o,r)},m=d?function(e){return function(){return r(d()[e])}}:function(e){return o[e]};h.get=function(e){return o[e]&&r(m(e))},h.has=function(e){return e in o},S.i.H in o&&S.i.subscribe(e,S.i.H,(function(){var t=(0,o[S.i.H])();if(t){var n=S.h.childNodes(e);n.length&&t(n,S.Ec(n[0]))}})),S.i.pa in o&&(p=S.i.Cb(e,n),S.i.subscribe(e,S.i.pa,(function(){var t=(0,o[S.i.pa])();t&&S.h.firstChild(e)&&t(e)}))),i=function(e){var t=[],n={},r=[];return S.a.P(e,(function o(i){if(!n[i]){var a=S.getBindingHandler(i);a&&(a.after&&(r.push(i),S.a.D(a.after,(function(t){if(e[t]){if(-1!==S.a.A(r,t))throw Error("Cannot combine the following bindings, because they have a cyclic dependency: "+r.join(", "));o(t)}})),r.length--),t.push({key:i,Mc:a})),n[i]=!0}})),t}(o),S.a.D(i,(function(t){var n=t.Mc.init,r=t.Mc.update,i=t.key;if(8===e.nodeType&&!S.h.ea[i])throw Error("The binding '"+i+"' cannot be used with virtual elements");try{"function"==typeof n&&S.u.G((function(){var t=n(e,m(i),h,p.$data,p);if(t&&t.controlsDescendantBindings){if(f!==a)throw Error("Multiple bindings ("+f+" and "+i+") are trying to control descendant bindings of the same element. You cannot use these bindings together on the same element.");f=i}})),"function"==typeof r&&S.$((function(){r(e,m(i),h,p.$data,p)}),null,{l:e})}catch(s){throw s.message='Unable to process binding "'+i+": "+o[i]+'"\nMessage: '+s.message,s}}))}return{shouldBindDescendants:i=f===a,bindingContextForDescendants:i&&p}}function h(e,t){return e&&e instanceof S.fa?e:new S.fa(e,a,a,t)}var g=S.a.Da("_subscribable"),m=S.a.Da("_ancestorBindingInfo"),v=S.a.Da("_dataDependency");S.c={};var b={script:!0,textarea:!0,template:!0};S.getBindingHandler=function(e){return S.c[e]};var y={};S.fa=function(e,t,n,r,o){function i(){var e=d?l():l,o=S.a.f(e);return t?(S.a.extend(u,t),m in t&&(u[m]=t[m])):(u.$parents=[],u.$root=o,u.ko=S),u[g]=s,c?o=u.$data:(u.$rawData=e,u.$data=o),n&&(u[n]=o),r&&r(u,t,o),t&&t[g]&&!S.S.o().Vb(t[g])&&t[g](),f&&(u[v]=f),u.$data}var s,u=this,c=e===y,l=c?a:e,d="function"==typeof l&&!S.O(l),f=o&&o.dataDependency;o&&o.exportDependencies?i():((s=S.xb(i)).v(),s.ja()?s.equalityComparer=null:u[g]=a)},S.fa.prototype.createChildContext=function(e,t,n,r){if(!r&&t&&"object"==typeof t&&(t=(r=t).as,n=r.extend),t&&r&&r.noChildContext){var o="function"==typeof e&&!S.O(e);return new S.fa(y,this,null,(function(r){n&&n(r),r[t]=o?e():e}),r)}return new S.fa(e,this,t,(function(e,t){e.$parentContext=t,e.$parent=t.$data,e.$parents=(t.$parents||[]).slice(0),e.$parents.unshift(e.$parent),n&&n(e)}),r)},S.fa.prototype.extend=function(e,t){return new S.fa(y,this,null,(function(t){S.a.extend(t,"function"==typeof e?e(t):e)}),t)};var C=S.a.g.Z();t.prototype.Tc=function(){this.Kb&&this.Kb.N&&this.Kb.N.sd(this.node)},t.prototype.sd=function(e){S.a.Pa(this.kb,e),!this.kb.length&&this.H&&this.Cc()},t.prototype.Cc=function(){this.H=!0,this.yc.N&&!this.kb.length&&(this.yc.N=null,S.a.K.yb(this.node,e),S.i.ma(this.node,S.i.pa),this.Tc())},S.i={H:"childrenComplete",pa:"descendantsComplete",subscribe:function(e,t,n,r,o){var i=S.a.g.Ub(e,C,{});return i.Fa||(i.Fa=new S.T),o&&o.notifyImmediately&&i.Zb[t]&&S.u.G(n,r,[e]),i.Fa.subscribe(n,r,t)},ma:function(e,t){var n=S.a.g.get(e,C);if(n&&(n.Zb[t]=!0,n.Fa&&n.Fa.notifySubscribers(e,t),t==S.i.H))if(n.N)n.N.Cc();else if(n.N===a&&n.Fa&&n.Fa.Wa(S.i.pa))throw Error("descendantsComplete event not supported for bindings on this node")},Cb:function(e,n){var r=S.a.g.Ub(e,C,{});return r.N||(r.N=new t(e,r,n[m])),n[m]==r?n:n.extend((function(e){e[m]=r}))}},S.Td=function(e){return(e=S.a.g.get(e,C))&&e.context},S.ib=function(e,t,n){return 1===e.nodeType&&S.h.Sc(e),p(e,t,h(n))},S.ld=function(e,t,n){return n=h(n),S.ib(e,i(t,n,e),n)},S.Oa=function(e,t){1!==t.nodeType&&8!==t.nodeType||d(h(e),t)},S.vc=function(e,t,n){if(!l&&s.jQuery&&(l=s.jQuery),2>arguments.length){if(!(t=u.body))throw Error("ko.applyBindings: could not find document.body; has the document been loaded?")}else if(!t||1!==t.nodeType&&8!==t.nodeType)throw Error("ko.applyBindings: first parameter should be your view model; second parameter should be a DOM node");f(h(e,n),t)},S.Dc=function(e){return!e||1!==e.nodeType&&8!==e.nodeType?a:S.Td(e)},S.Ec=function(e){return(e=S.Dc(e))?e.$data:a},S.b("bindingHandlers",S.c),S.b("bindingEvent",S.i),S.b("bindingEvent.subscribe",S.i.subscribe),S.b("bindingEvent.startPossiblyAsyncContentBinding",S.i.Cb),S.b("applyBindings",S.vc),S.b("applyBindingsToDescendants",S.Oa),S.b("applyBindingAccessorsToNode",S.ib),S.b("applyBindingsToNode",S.ld),S.b("contextFor",S.Dc),S.b("dataFor",S.Ec)}(),function(e){function t(t,r){var a,s=Object.prototype.hasOwnProperty.call(o,t)?o[t]:e;s?s.subscribe(r):((s=o[t]=new S.T).subscribe(r),n(t,(function(e,n){var r=!(!n||!n.synchronous);i[t]={definition:e,Gd:r},delete o[t],a||r?s.notifySubscribers(e):S.na.zb((function(){s.notifySubscribers(e)}))})),a=!0)}function n(e,t){r("getConfig",[e],(function(n){n?r("loadComponent",[e,n],(function(e){t(e,n)})):t(null,null)}))}function r(t,n,o,i){i||(i=S.j.loaders.slice(0));var a=i.shift();if(a){var s=a[t];if(s){var u=!1;if(s.apply(a,n.concat((function(e){u?o(null):null!==e?o(e):r(t,n,o,i)})))!==e&&(u=!0,!a.suppressLoaderExceptions))throw Error("Component loaders must supply values by invoking the callback, not by returning values synchronously.")}else r(t,n,o,i)}else o(null)}var o={},i={};S.j={get:function(n,r){var o=Object.prototype.hasOwnProperty.call(i,n)?i[n]:e;o?o.Gd?S.u.G((function(){r(o.definition)})):S.na.zb((function(){r(o.definition)})):t(n,r)},Bc:function(e){delete i[e]},oc:r},S.j.loaders=[],S.b("components",S.j),S.b("components.get",S.j.get),S.b("components.clearCachedDefinition",S.j.Bc)}(),function(){function e(e,t,n,r){function i(){0==--s&&r(a)}var a={},s=2,u=n.template;n=n.viewModel,u?o(t,u,(function(t){S.j.oc("loadTemplate",[e,t],(function(e){a.template=e,i()}))})):i(),n?o(t,n,(function(t){S.j.oc("loadViewModel",[e,t],(function(e){a[c]=e,i()}))})):i()}function n(e){switch(S.a.R(e)){case"script":return S.a.ua(e.text);case"textarea":return S.a.ua(e.value);case"template":if(r(e.content))return S.a.Ca(e.content.childNodes)}return S.a.Ca(e.childNodes)}function r(e){return s.DocumentFragment?e instanceof DocumentFragment:e&&11===e.nodeType}function o(e,n,r){"string"==typeof n.require?t||s.require?(t||s.require)([n.require],(function(e){e&&"object"==typeof e&&e.Xd&&e["default"]&&(e=e["default"]),r(e)})):e("Uses require, but no AMD loader is present"):r(n)}function i(e){return function(t){throw Error("Component '"+e+"': "+t)}}var a={};S.j.register=function(e,t){if(!t)throw Error("Invalid configuration for "+e);if(S.j.tb(e))throw Error("Component "+e+" is already registered");a[e]=t},S.j.tb=function(e){return Object.prototype.hasOwnProperty.call(a,e)},S.j.unregister=function(e){delete a[e],S.j.Bc(e)},S.j.Fc={getConfig:function(e,t){t(S.j.tb(e)?a[e]:null)},loadComponent:function(t,n,r){var a=i(t);o(a,n,(function(n){e(t,a,n,r)}))},loadTemplate:function(e,t,o){if(e=i(e),"string"==typeof t)o(S.a.ua(t));else if(t instanceof Array)o(t);else if(r(t))o(S.a.la(t.childNodes));else if(t.element)if(t=t.element,s.HTMLElement?t instanceof HTMLElement:t&&t.tagName&&1===t.nodeType)o(n(t));else if("string"==typeof t){var a=u.getElementById(t);a?o(n(a)):e("Cannot find element with ID "+t)}else e("Unknown element type: "+t);else e("Unknown template value: "+t)},loadViewModel:function(e,t,n){!function r(e,t,n){if("function"==typeof t)n((function(e){return new t(e)}));else if("function"==typeof t[c])n(t[c]);else if("instance"in t){var o=t.instance;n((function(){return o}))}else"viewModel"in t?r(e,t.viewModel,n):e("Unknown viewModel value: "+t)}(i(e),t,n)}};var c="createViewModel";S.b("components.register",S.j.register),S.b("components.isRegistered",S.j.tb),S.b("components.unregister",S.j.unregister),S.b("components.defaultLoader",S.j.Fc),S.j.loaders.push(S.j.Fc),S.j.dd=a}(),function(){function e(e,n){if(r=e.getAttribute("params")){var r=t.parseBindingsString(r,n,e,{valueAccessors:!0,bindingParams:!0}),o=(r=S.a.Ga(r,(function(t){return S.o(t,null,{l:e})})),S.a.Ga(r,(function(t){var n=t.v();return t.ja()?S.o({read:function(){return S.a.f(t())},write:S.Za(n)&&function(e){t()(e)},l:e}):n})));return Object.prototype.hasOwnProperty.call(o,"$raw")||(o.$raw=r),o}return{$raw:{}}}S.j.getComponentNameForNode=function(e){var t=S.a.R(e);if(S.j.tb(t)&&(-1!=t.indexOf("-")||"[object HTMLUnknownElement]"==""+e||8>=S.a.W&&e.tagName===t))return t},S.j.tc=function(t,n,r,o){if(1===n.nodeType){var i=S.j.getComponentNameForNode(n);if(i){if((t=t||{}).component)throw Error('Cannot use the "component" binding on a custom element matching a component');var a={name:i,params:e(n,r)};t.component=o?function(){return a}:a}}return t};var t=new S.ga;9>S.a.W&&(S.j.register=function(e){return function(t){return e.apply(this,arguments)}}(S.j.register),u.createDocumentFragment=function(e){return function(){var t,n=e(),r=S.j.dd;for(t in r);return n}}(u.createDocumentFragment))}(),function(){var e=0;S.c.component={init:function(t,n,r,o,i){function a(){var e=s&&s.dispose;"function"==typeof e&&e.call(s),c&&c.s(),u=s=c=null}var s,u,c,l=S.a.la(S.h.childNodes(t));return S.h.Ea(t),S.a.K.za(t,a),S.o((function(){var r,o,d=S.a.f(n());if("string"==typeof d?r=d:(r=S.a.f(d.name),o=S.a.f(d.params)),!r)throw Error("No component name specified");var f=S.i.Cb(t,i),p=u=++e;S.j.get(r,(function(e){if(u===p){if(a(),!e)throw Error("Unknown component '"+r+"'");!function(e,t,n){if(!(t=t.template))throw Error("Component '"+e+"' has no template");e=S.a.Ca(t),S.h.va(n,e)}(r,e,t);var n=function(e,t,n){var r=e.createViewModel;return r?r.call(e,t,n):t}(e,o,{element:t,templateNodes:l});e=f.createChildContext(n,{extend:function(e){e.$component=n,e.$componentTemplateNodes=l}}),n&&n.koDescendantsComplete&&(c=S.i.subscribe(t,S.i.pa,n.koDescendantsComplete,n)),s=n,S.Oa(e,t)}}))}),null,{l:t}),{controlsDescendantBindings:!0}}},S.h.ea.component=!0}();var N={"class":"className","for":"htmlFor"};S.c.attr={update:function(e,t){var n=S.a.f(t())||{};S.a.P(n,(function(t,n){n=S.a.f(n);var r=t.indexOf(":"),o=(r="lookupNamespaceURI"in e&&0<r&&e.lookupNamespaceURI(t.substr(0,r)),!1===n||null===n||n===a);o?r?e.removeAttributeNS(r,t):e.removeAttribute(t):n=n.toString(),8>=S.a.W&&t in N?(t=N[t],o?e.removeAttribute(t):e[t]=n):o||(r?e.setAttributeNS(r,t,n):e.setAttribute(t,n)),"name"===t&&S.a.Yc(e,o?"":n)}))}},S.c.checked={after:["value","attr"],init:function(e,t,n){function r(){var r=e.checked,u=o();if(!S.S.Ya()&&(r||!s&&!S.S.qa())){var d=S.u.G(t);if(c){var p=l?d.v():d,h=f;f=u,h!==u?r&&(S.a.Na(p,u,!0),S.a.Na(p,h,!1)):S.a.Na(p,u,r),l&&S.Za(d)&&d(p)}else i&&(u===a?u=r:r||(u=a)),S.m.eb(d,n,"checked",u,!0)}}var o=S.xb((function(){return n.has("checkedValue")?S.a.f(n.get("checkedValue")):d?n.has("value")?S.a.f(n.get("value")):e.value:void 0})),i="checkbox"==e.type,s="radio"==e.type;if(i||s){var u=t(),c=i&&S.a.f(u)instanceof Array,l=!(c&&u.push&&u.splice),d=s||c,f=c?o():a;s&&!e.name&&S.c.uniqueName.init(e,(function(){return!0})),S.o(r,null,{l:e}),S.a.B(e,"click",r),S.o((function(){var n=S.a.f(t()),r=o();c?(e.checked=0<=S.a.A(n,r),f=r):e.checked=i&&r===a?!!n:o()===n}),null,{l:e}),u=a}}},S.m.wa.checked=!0,S.c.checkedValue={update:function(e,t){e.value=S.a.f(t())}},S.c["class"]={update:function(e,t){var n=S.a.Db(S.a.f(t()));S.a.Eb(e,e.__ko__cssValue,!1),e.__ko__cssValue=n,S.a.Eb(e,n,!0)}},S.c.css={update:function(e,t){var n=S.a.f(t());null!==n&&"object"==typeof n?S.a.P(n,(function(t,n){n=S.a.f(n),S.a.Eb(e,t,n)})):S.c["class"].update(e,t)}},S.c.enable={update:function(e,t){var n=S.a.f(t());n&&e.disabled?e.removeAttribute("disabled"):n||e.disabled||(e.disabled=!0)}},S.c.disable={update:function(e,t){S.c.enable.update(e,(function(){return!S.a.f(t())}))}},S.c.event={init:function(e,t,n,r,o){var i=t()||{};S.a.P(i,(function(i){"string"==typeof i&&S.a.B(e,i,(function(e){var a,s=t()[i];if(s){try{var u=S.a.la(arguments);r=o.$data,u.unshift(r),a=s.apply(r,u)}finally{!0!==a&&(e.preventDefault?e.preventDefault():e.returnValue=!1)}!1===n.get(i+"Bubble")&&(e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation())}}))}))}},S.c.foreach={Rc:function(e){return function(){var t=e(),n=S.a.bc(t);return n&&"number"!=typeof n.length?(S.a.f(t),{foreach:n.data,as:n.as,noChildContext:n.noChildContext,includeDestroyed:n.includeDestroyed,afterAdd:n.afterAdd,beforeRemove:n.beforeRemove,afterRender:n.afterRender,beforeMove:n.beforeMove,afterMove:n.afterMove,templateEngine:S.ba.Ma}):{foreach:t,templateEngine:S.ba.Ma}}},init:function(e,t){return S.c.template.init(e,S.c.foreach.Rc(t))},update:function(e,t,n,r,o){return S.c.template.update(e,S.c.foreach.Rc(t),n,r,o)}},S.m.Ra.foreach=!1,S.h.ea.foreach=!0,S.c.hasfocus={init:function(e,t,n){function r(r){e.__ko_hasfocusUpdating=!0;var o=e.ownerDocument;if("activeElement"in o){var i;try{i=o.activeElement}catch(a){i=o.body}r=i===e}o=t(),S.m.eb(o,n,"hasfocus",r,!0),e.__ko_hasfocusLastValue=r,e.__ko_hasfocusUpdating=!1}var o=r.bind(null,!0),i=r.bind(null,!1);S.a.B(e,"focus",o),S.a.B(e,"focusin",o),S.a.B(e,"blur",i),S.a.B(e,"focusout",i),e.__ko_hasfocusLastValue=!1},update:function(e,t){var n=!!S.a.f(t());e.__ko_hasfocusUpdating||e.__ko_hasfocusLastValue===n||(n?e.focus():e.blur(),!n&&e.__ko_hasfocusLastValue&&e.ownerDocument.body.focus(),S.u.G(S.a.Fb,null,[e,n?"focusin":"focusout"]))}},S.m.wa.hasfocus=!0,S.c.hasFocus=S.c.hasfocus,S.m.wa.hasFocus="hasfocus",S.c.html={init:function(){return{controlsDescendantBindings:!0}},update:function(e,t){S.a.fc(e,t())}},function(){function e(e,t,n){S.c[e]={init:function(e,r,o,i,a){var s,u,c,l,d,f={};if(t){i=o.get("as");var p=o.get("noChildContext");f={as:i,noChildContext:p,exportDependencies:d=!(i&&p)}}return l=(c="render"==o.get("completeOn"))||o.has(S.i.pa),S.o((function(){var o,i=S.a.f(r()),p=!n!=!i,h=!u;(d||p!==s)&&(l&&(a=S.i.Cb(e,a)),p&&(t&&!d||(f.dataDependency=S.S.o()),o=t?a.createChildContext("function"==typeof i?i:r,f):S.S.qa()?a.extend(null,f):a),h&&S.S.qa()&&(u=S.a.Ca(S.h.childNodes(e),!0)),p?(h||S.h.va(e,S.a.Ca(u)),S.Oa(o,e)):(S.h.Ea(e),c||S.i.ma(e,S.i.H)),s=p)}),null,{l:e}),{controlsDescendantBindings:!0}}},S.m.Ra[e]=!1,S.h.ea[e]=!0}e("if"),e("ifnot",!1,!0),e("with",!0)}(),S.c["let"]={init:function(e,t,n,r,o){return t=o.extend(t),S.Oa(t,e),{controlsDescendantBindings:!0}}},S.h.ea["let"]=!0;var D={};S.c.options={init:function(e){if("select"!==S.a.R(e))throw Error("options binding applies only to SELECT elements");for(;0<e.length;)e.remove(0);return{controlsDescendantBindings:!0}},update:function(e,t,n){function r(){return S.a.jb(e.options,(function(e){return e.selected}))}function o(e,t,n){var r=typeof t;return"function"==r?t(e):"string"==r?e[t]:n}function i(t,n){if(h&&l)S.i.ma(e,S.i.H);else if(p.length){var r=0<=S.a.A(p,S.w.M(n[0]));S.a.Zc(n[0],r),h&&!r&&S.u.G(S.a.Fb,null,[e,"change"])}}var s=e.multiple,u=0!=e.length&&s?e.scrollTop:null,c=S.a.f(t()),l=n.get("valueAllowUnset")&&n.has("value"),d=n.get("optionsIncludeDestroyed");t={};var f,p=[];l||(s?p=S.a.Mb(r(),S.w.M):0<=e.selectedIndex&&p.push(S.w.M(e.options[e.selectedIndex]))),c&&("undefined"==typeof c.length&&(c=[c]),f=S.a.jb(c,(function(e){return d||e===a||null===e||!S.a.f(e._destroy)})),n.has("optionsCaption")&&null!==(c=S.a.f(n.get("optionsCaption")))&&c!==a&&f.unshift(D));var h=!1;t.beforeRemove=function(t){e.removeChild(t)},c=i,n.has("optionsAfterRender")&&"function"==typeof n.get("optionsAfterRender")&&(c=function(e,t){i(0,t),S.u.G(n.get("optionsAfterRender"),null,[t[0],e!==D?e:a])}),S.a.ec(e,f,(function(t,r,i){return i.length&&(p=!l&&i[0].selected?[S.w.M(i[0])]:[],h=!0),r=e.ownerDocument.createElement("option"),t===D?(S.a.Bb(r,n.get("optionsCaption")),S.w.cb(r,a)):(i=o(t,n.get("optionsValue"),t),S.w.cb(r,S.a.f(i)),t=o(t,n.get("optionsText"),i),S.a.Bb(r,t)),[r]}),t,c),l||(s?p.length&&r().length<p.length:p.length&&0<=e.selectedIndex?S.w.M(e.options[e.selectedIndex])!==p[0]:p.length||0<=e.selectedIndex)&&S.u.G(S.a.Fb,null,[e,"change"]),(l||S.S.Ya())&&S.i.ma(e,S.i.H),S.a.wd(e),u&&20<Math.abs(u-e.scrollTop)&&(e.scrollTop=u)}},S.c.options.$b=S.a.g.Z(),S.c.selectedOptions={init:function(e,t,n){function r(){var r=t(),o=[];S.a.D(e.getElementsByTagName("option"),(function(e){e.selected&&o.push(S.w.M(e))})),S.m.eb(r,n,"selectedOptions",o)}function o(){var n=S.a.f(t()),r=e.scrollTop;n&&"number"==typeof n.length&&S.a.D(e.getElementsByTagName("option"),(function(e){var t=0<=S.a.A(n,S.w.M(e));e.selected!=t&&S.a.Zc(e,t)})),e.scrollTop=r}if("select"!=S.a.R(e))throw Error("selectedOptions binding applies only to SELECT elements");var i;S.i.subscribe(e,S.i.H,(function(){i?r():(S.a.B(e,"change",r),i=S.o(o,null,{l:e}))}),null,{notifyImmediately:!0})},update:function(){}},S.m.wa.selectedOptions=!0,S.c.style={update:function(e,t){var n=S.a.f(t()||{});S.a.P(n,(function(t,n){if(null!==(n=S.a.f(n))&&n!==a&&!1!==n||(n=""),l)l(e).css(t,n);else if(/^--/.test(t))e.style.setProperty(t,n);else{t=t.replace(/-(\w)/g,(function(e,t){return t.toUpperCase()}));var r=e.style[t];e.style[t]=n,n===r||e.style[t]!=r||isNaN(n)||(e.style[t]=n+"px")}}))}},S.c.submit={init:function(e,t,n,r,o){if("function"!=typeof t())throw Error("The value for a submit binding must be a function");S.a.B(e,"submit",(function(n){var r,i=t();try{r=i.call(o.$data,e)}finally{!0!==r&&(n.preventDefault?n.preventDefault():n.returnValue=!1)}}))}},S.c.text={init:function(){return{controlsDescendantBindings:!0}},update:function(e,t){S.a.Bb(e,t())}},S.h.ea.text=!0,function(){if(s&&s.navigator){var e,t,n,r,o,i=function(e){if(e)return parseFloat(e[1])},u=s.navigator.userAgent;(e=s.opera&&s.opera.version&&parseInt(s.opera.version()))||(o=i(u.match(/Edge\/([^ ]+)$/)))||i(u.match(/Chrome\/([^ ]+)/))||(t=i(u.match(/Version\/([^ ]+) Safari/)))||(n=i(u.match(/Firefox\/([^ ]+)/)))||(r=S.a.W||i(u.match(/MSIE ([^ ]+)/)))||(r=i(u.match(/rv:([^ )]+)/)))}if(8<=r&&10>r)var c=S.a.g.Z(),l=S.a.g.Z(),d=function(e){var t=this.activeElement;(t=t&&S.a.g.get(t,l))&&t(e)},f=function(e,t){var n=e.ownerDocument;S.a.g.get(n,c)||(S.a.g.set(n,c,!0),S.a.B(n,"selectionchange",d)),S.a.g.set(e,l,t)};S.c.textInput={init:function(i,s,u){function c(e,t){S.a.B(i,e,t)}function l(){p||(h=i.value,p=S.a.setTimeout(d,4))}function d(){clearTimeout(p),h=p=a;var e=i.value;g!==e&&(g=e,S.m.eb(s(),u,"textInput",e))}var p,h,g=i.value,m=9==S.a.W?l:d,v=!1;r&&c("keypress",d),11>r&&c("propertychange",(function(e){v||"value"!==e.propertyName||m(e)})),8==r&&(c("keyup",d),c("keydown",d)),f&&(f(i,m),c("dragend",l)),(!r||9<=r)&&c("input",m),5>t&&"textarea"===S.a.R(i)?(c("keydown",l),c("paste",l),c("cut",l)):11>e?c("keydown",l):4>n?(c("DOMAutoComplete",d),c("dragdrop",d),c("drop",d)):o&&"number"===i.type&&c("keydown",l),c("change",d),c("blur",d),S.o((function b(){var e=S.a.f(s());null!==e&&e!==a||(e=""),h!==a&&e===h?S.a.setTimeout(b,4):i.value!==e&&(v=!0,i.value=e,v=!1,g=i.value)}),null,{l:i})}},S.m.wa.textInput=!0,S.c.textinput={preprocess:function(e,t,n){n("textInput",e)}}}(),S.c.uniqueName={init:function(e,t){if(t()){var n="ko_unique_"+ ++S.c.uniqueName.rd;S.a.Yc(e,n)}}},S.c.uniqueName.rd=0,S.c.using={init:function(e,t,n,r,o){var i;return n.has("as")&&(i={as:n.get("as"),noChildContext:n.get("noChildContext")}),t=o.createChildContext(t,i),S.Oa(t,e),{controlsDescendantBindings:!0}}},S.h.ea.using=!0,S.c.value={init:function(e,t,n){var r=S.a.R(e),o="input"==r;if(!o||"checkbox"!=e.type&&"radio"!=e.type){var i=[],s=n.get("valueUpdate"),u=!1,c=null;s&&(i="string"==typeof s?[s]:S.a.wc(s),S.a.Pa(i,"change"));var l,d,f=function(){c=null,u=!1;var r=t(),o=S.w.M(e);S.m.eb(r,n,"value",o)};!S.a.W||!o||"text"!=e.type||"off"==e.autocomplete||e.form&&"off"==e.form.autocomplete||-1!=S.a.A(i,"propertychange")||(S.a.B(e,"propertychange",(function(){u=!0})),S.a.B(e,"focus",(function(){u=!1})),S.a.B(e,"blur",(function(){u&&f()}))),S.a.D(i,(function(t){var n=f;S.a.Ud(t,"after")&&(n=function(){c=S.w.M(e),S.a.setTimeout(f,0)},t=t.substring(5)),S.a.B(e,t,n)})),l=o&&"file"==e.type?function(){var n=S.a.f(t());null===n||n===a||""===n?e.value="":S.u.G(f)}:function(){var o=S.a.f(t()),i=S.w.M(e);null!==c&&o===c?S.a.setTimeout(l,0):o===i&&i!==a||("select"===r?(i=n.get("valueAllowUnset"),S.w.cb(e,o,i),i||o===S.w.M(e)||S.u.G(f)):S.w.cb(e,o))},"select"===r?S.i.subscribe(e,S.i.H,(function(){d?n.get("valueAllowUnset")?l():f():(S.a.B(e,"change",f),d=S.o(l,null,{l:e}))}),null,{notifyImmediately:!0}):(S.a.B(e,"change",f),S.o(l,null,{l:e}))}else S.ib(e,{checkedValue:t})},update:function(){}},S.m.wa.value=!0,S.c.visible={update:function(e,t){var n=S.a.f(t()),r="none"!=e.style.display;n&&!r?e.style.display="":!n&&r&&(e.style.display="none")}},S.c.hidden={update:function(e,t){S.c.visible.update(e,(function(){return!S.a.f(t())}))}},function(e){S.c[e]={init:function(t,n,r,o,i){return S.c.event.init.call(this,t,(function(){var t={};return t[e]=n(),t}),r,o,i)}}}("click"),S.ca=function(){},S.ca.prototype.renderTemplateSource=function(){throw Error("Override renderTemplateSource")},S.ca.prototype.createJavaScriptEvaluatorBlock=function(){throw Error("Override createJavaScriptEvaluatorBlock")},S.ca.prototype.makeTemplateSource=function(e,t){if("string"==typeof e){var n=(t=t||u).getElementById(e);if(!n)throw Error("Cannot find template with ID "+e);return new S.C.F(n)}if(1==e.nodeType||8==e.nodeType)return new S.C.ia(e);throw Error("Unknown template type: "+e)},S.ca.prototype.renderTemplate=function(e,t,n,r){return e=this.makeTemplateSource(e,r),this.renderTemplateSource(e,t,n,r)},S.ca.prototype.isTemplateRewritten=function(e,t){return!1===this.allowTemplateRewriting||this.makeTemplateSource(e,t).data("isRewritten")},S.ca.prototype.rewriteTemplate=function(e,t,n){t=t((e=this.makeTemplateSource(e,n)).text()),e.text(t),e.data("isRewritten",!0)},S.b("templateEngine",S.ca),S.kc=function(){function e(e,t,n,r){e=S.m.ac(e);for(var o=S.m.Ra,i=0;i<e.length;i++){var a=e[i].key;if(Object.prototype.hasOwnProperty.call(o,a)){var s=o[a];if("function"==typeof s){if(a=s(e[i].value))throw Error(a)}else if(!s)throw Error("This template engine does not support the '"+a+"' binding within its templates")}}return n="ko.__tr_ambtns(function($context,$element){return(function(){return{ "+S.m.vb(e,{valueAccessors:!0})+" } })()},'"+n.toLowerCase()+"')",r.createJavaScriptEvaluatorBlock(n)+t}var t=/(<([a-z]+\d*)(?:\s+(?!data-bind\s*=\s*)[a-z0-9\-]+(?:=(?:\"[^\"]*\"|\'[^\']*\'|[^>]*))?)*\s+)data-bind\s*=\s*(["'])([\s\S]*?)\3/gi,n=/\x3c!--\s*ko\b\s*([\s\S]*?)\s*--\x3e/g;return{xd:function(e,t,n){t.isTemplateRewritten(e,n)||t.rewriteTemplate(e,(function(e){return S.kc.Ld(e,t)}),n)},Ld:function(r,o){return r.replace(t,(function(t,n,r,i,a){return e(a,n,r,o)})).replace(n,(function(t,n){return e(n,"\x3c!-- ko --\x3e","#comment",o)}))},md:function(e,t){return S.aa.Xb((function(n,r){var o=n.nextSibling;o&&o.nodeName.toLowerCase()===t&&S.ib(o,e,r)}))}}}(),S.b("__tr_ambtns",S.kc.md),function(){S.C={},S.C.F=function(e){if(this.F=e){var t=S.a.R(e);this.ab="script"===t?1:"textarea"===t?2:"template"==t&&e.content&&11===e.content.nodeType?3:4}},S.C.F.prototype.text=function(){var e=1===this.ab?"text":2===this.ab?"value":"innerHTML";if(0==arguments.length)return this.F[e];var t=arguments[0];"innerHTML"===e?S.a.fc(this.F,t):this.F[e]=t};var e=S.a.g.Z()+"_";S.C.F.prototype.data=function(t){if(1===arguments.length)return S.a.g.get(this.F,e+t);S.a.g.set(this.F,e+t,arguments[1])};var t=S.a.g.Z();S.C.F.prototype.nodes=function(){var e=this.F;if(0==arguments.length){var n=S.a.g.get(e,t)||{},r=n.lb||(3===this.ab?e.content:4===this.ab?e:a);if(!r||n.jd){var o=this.text();o&&o!==n.bb&&(r=S.a.Md(o,e.ownerDocument),S.a.g.set(e,t,{lb:r,bb:o,jd:!0}))}return r}n=arguments[0],this.ab!==a&&this.text(""),S.a.g.set(e,t,{lb:n})},S.C.ia=function(e){this.F=e},S.C.ia.prototype=new S.C.F,S.C.ia.prototype.constructor=S.C.ia,S.C.ia.prototype.text=function(){if(0==arguments.length){var e=S.a.g.get(this.F,t)||{};return e.bb===a&&e.lb&&(e.bb=e.lb.innerHTML),e.bb}S.a.g.set(this.F,t,{bb:arguments[0]})},S.b("templateSources",S.C),S.b("templateSources.domElement",S.C.F),S.b("templateSources.anonymousTemplate",S.C.ia)}(),function(){function e(e,t,n){var r;for(t=S.h.nextSibling(t);e&&(r=e)!==t;)n(r,e=S.h.nextSibling(r))}function t(t,n){if(t.length){var r=t[0],o=t[t.length-1],i=r.parentNode,a=S.ga.instance,s=a.preprocessNode;if(s){if(e(r,o,(function(e,t){var n=e.previousSibling,i=s.call(a,e);i&&(e===r&&(r=i[0]||t),e===o&&(o=i[i.length-1]||n))})),t.length=0,!r)return;r===o?t.push(r):(t.push(r,o),S.a.Ua(t,i))}e(r,o,(function(e){1!==e.nodeType&&8!==e.nodeType||S.vc(n,e)})),e(r,o,(function(e){1!==e.nodeType&&8!==e.nodeType||S.aa.cd(e,[n])})),S.a.Ua(t,i)}}function n(e){return e.nodeType?e:0<e.length?e[0]:null}function r(e,r,o,a,s){s=s||{};var u=(e&&n(e)||o||{}).ownerDocument,c=s.templateEngine||i;if(S.kc.xd(o,c,u),"number"!=typeof(o=c.renderTemplate(o,a,s,u)).length||0<o.length&&"number"!=typeof o[0].nodeType)throw Error("Template engine must return an array of DOM nodes");switch(u=!1,r){case"replaceChildren":S.h.va(e,o),u=!0;break;case"replaceNode":S.a.Xc(e,o),u=!0;break;case"ignoreTargetNode":break;default:throw Error("Unknown renderMode: "+r)}return u&&(t(o,a),s.afterRender&&S.u.G(s.afterRender,null,[o,a[s.as||"$data"]]),"replaceChildren"==r&&S.i.ma(e,S.i.H)),o}function o(e,t,n){return S.O(e)?e():"function"==typeof e?e(t,n):e}var i;S.gc=function(e){if(e!=a&&!(e instanceof S.ca))throw Error("templateEngine must inherit from ko.templateEngine");i=e},S.dc=function(e,t,s,u,c){if(((s=s||{}).templateEngine||i)==a)throw Error("Set a template engine before calling renderTemplate");if(c=c||"replaceChildren",u){var l=n(u);return S.$((function(){var i=t&&t instanceof S.fa?t:new S.fa(t,null,null,null,{exportDependencies:!0}),a=o(e,i.$data,i);i=r(u,c,a,i,s),"replaceNode"==c&&(l=n(u=i))}),null,{Sa:function(){return!l||!S.a.Sb(l)},l:l&&"replaceNode"==c?l.parentNode:l})}return S.aa.Xb((function(n){S.dc(e,t,s,n,"replaceNode")}))},S.Qd=function(e,n,i,s,u){function c(e,t){S.u.G(S.a.ec,null,[s,e,d,i,l,t]),S.i.ma(s,S.i.H)}function l(e,n){t(n,f),i.afterRender&&i.afterRender(n,e),f=null}function d(t,n){f=u.createChildContext(t,{as:p,noChildContext:i.noChildContext,extend:function(e){e.$index=n,p&&(e[p+"Index"]=n)}});var a=o(e,t,f);return r(s,"ignoreTargetNode",a,f,i)}var f,p=i.as,h=!1===i.includeDestroyed||S.options.foreachHidesDestroyed&&!i.includeDestroyed;if(h||i.beforeRemove||!S.Pc(n))return S.$((function(){var e=S.a.f(n)||[];"undefined"==typeof e.length&&(e=[e]),h&&(e=S.a.jb(e,(function(e){return e===a||null===e||!S.a.f(e._destroy)}))),c(e)}),null,{l:s});c(n.v());var g=n.subscribe((function(e){c(n(),e)}),null,"arrayChange");return g.l(s),g};var s=S.a.g.Z(),u=S.a.g.Z();S.c.template={init:function(e,t){var n=S.a.f(t());if("string"==typeof n||"name"in n)S.h.Ea(e);else if("nodes"in n){if(n=n.nodes||[],S.O(n))throw Error('The "nodes" option must be a plain, non-observable array.');var r=n[0]&&n[0].parentNode;r&&S.a.g.get(r,u)||(r=S.a.Yb(n),S.a.g.set(r,u,!0)),new S.C.ia(e).nodes(r)}else{if(!(0<(n=S.h.childNodes(e)).length))throw Error("Anonymous template defined, but no template content was provided");r=S.a.Yb(n),new S.C.ia(e).nodes(r)}return{controlsDescendantBindings:!0}},update:function(e,t,n,r,o){var i=t();n=!0,r=null,"string"==typeof(t=S.a.f(i))?t={}:(i="name"in t?t.name:e,"if"in t&&(n=S.a.f(t["if"])),n&&"ifnot"in t&&(n=!S.a.f(t.ifnot)),n&&!i&&(n=!1)),"foreach"in t?r=S.Qd(i,n&&t.foreach||[],t,e,o):n?(n=o,"data"in t&&(n=o.createChildContext(t.data,{as:t.as,noChildContext:t.noChildContext,exportDependencies:!0})),r=S.dc(i,n,t,e)):S.h.Ea(e),o=r,(t=S.a.g.get(e,s))&&"function"==typeof t.s&&t.s(),S.a.g.set(e,s,!o||o.ja&&!o.ja()?a:o)}},S.m.Ra.template=function(e){return 1==(e=S.m.ac(e)).length&&e[0].unknown||S.m.Id(e,"name")?null:"This template engine does not support anonymous templates nested within its templates"},S.h.ea.template=!0}(),S.b("setTemplateEngine",S.gc),S.b("renderTemplate",S.dc),S.a.Kc=function(e,t,n){var r,o,i,a,s;if(e.length&&t.length)for(r=o=0;(!n||r<n)&&(a=e[o]);++o){for(i=0;s=t[i];++i)if(a.value===s.value){a.moved=s.index,s.moved=a.index,t.splice(i,1),r=i=0;break}r+=i}},S.a.Pb=function(){function e(e,t,n,r,o){var i,a,s,u,c,l=Math.min,d=Math.max,f=[],p=e.length,h=t.length,g=h-p||1,m=p+h+1;for(i=0;i<=p;i++)for(u=s,f.push(s=[]),c=l(h,i+g),a=d(0,i-1);a<=c;a++)s[a]=a?i?e[i-1]===t[a-1]?u[a-1]:l(u[a]||m,s[a-1]||m)+1:a+1:i+1;for(l=[],d=[],g=[],i=p,a=h;i||a;)h=f[i][a]-1,a&&h===f[i][a-1]?d.push(l[l.length]={status:n,value:t[--a],index:a}):i&&h===f[i-1][a]?g.push(l[l.length]={status:r,value:e[--i],index:i}):(--a,--i,o.sparse||l.push({status:"retained",value:t[a]}));return S.a.Kc(g,d,!o.dontLimitMoves&&10*p),l.reverse()}return function(t,n,r){return r="boolean"==typeof r?{dontLimitMoves:r}:r||{},n=n||[],(t=t||[]).length<n.length?e(t,n,"added","deleted",r):e(n,t,"deleted","added",r)}}(),S.b("utils.compareArrays",S.a.Pb),function(){function e(e,t,n,r,o){var i=[],s=S.$((function(){var a=t(n,o,S.a.Ua(i,e))||[];0<i.length&&(S.a.Xc(i,a),r&&S.u.G(r,null,[n,a,o])),i.length=0,S.a.Nb(i,a)}),null,{l:e,Sa:function(){return!S.a.kd(i)}});return{Y:i,$:s.ja()?s:a}}var t=S.a.g.Z(),n=S.a.g.Z();S.a.ec=function(r,o,i,s,u,c){function l(e){p={Aa:e,pb:S.ta(E++)},y.push(p),b||_.push(p)}function d(e){p=v[e],E!==p.pb.v()&&A.push(p),p.pb(E++),S.a.Ua(p.Y,r),y.push(p)}function f(e,t){if(e)for(var n=0,r=t.length;n<r;n++)S.a.D(t[n].Y,(function(r){e(r,n,t[n].Aa)}))}"undefined"==typeof(o=o||[]).length&&(o=[o]),s=s||{};var p,h,g,m,v=S.a.g.get(r,t),b=!v,y=[],C=0,E=0,w=[],P=[],T=[],A=[],_=[],x=0;if(b)S.a.D(o,l);else{if(!c||v&&v._countWaitingForRemove){var O=S.a.Mb(v,(function(e){return e.Aa}));c=S.a.Pb(O,o,{dontLimitMoves:s.dontLimitMoves,sparse:!0})}var N,D,k;for(O=0;N=c[O];O++)switch(D=N.moved,k=N.index,N.status){case"deleted":for(;C<k;)d(C++);D===a&&((p=v[C]).$&&(p.$.s(),p.$=a),S.a.Ua(p.Y,r).length&&(s.beforeRemove&&(y.push(p),x++,p.Aa===n?p=null:T.push(p)),p&&w.push.apply(w,p.Y))),C++;break;case"added":for(;E<k;)d(C++);D!==a?(P.push(y.length),d(D)):l(N.value)}for(;E<o.length;)d(C++);y._countWaitingForRemove=x}S.a.g.set(r,t,y),f(s.beforeMove,A),S.a.D(w,s.beforeRemove?S.oa:S.removeNode);try{m=r.ownerDocument.activeElement}catch(I){}if(P.length)for(;(O=P.shift())!=a;){for(p=y[O],h=a;O;)if((g=y[--O].Y)&&g.length){h=g[g.length-1];break}for(o=0;C=p.Y[o];h=C,o++)S.h.Wb(r,C,h)}for(O=0;p=y[O];O++){for(p.Y||S.a.extend(p,e(r,i,p.Aa,u,p.pb)),o=0;C=p.Y[o];h=C,o++)S.h.Wb(r,C,h);!p.Ed&&u&&(u(p.Aa,p.Y,p.pb),p.Ed=!0,h=p.Y[p.Y.length-1])}for(m&&r.ownerDocument.activeElement!=m&&m.focus(),f(s.beforeRemove,T),O=0;O<T.length;++O)T[O].Aa=n;f(s.afterMove,A),f(s.afterAdd,_)}}(),S.b("utils.setDomNodeChildrenFromArrayMapping",S.a.ec),S.ba=function(){this.allowTemplateRewriting=!1},S.ba.prototype=new S.ca,S.ba.prototype.constructor=S.ba,S.ba.prototype.renderTemplateSource=function(e,t,n,r){return(t=9>S.a.W||!e.nodes?null:e.nodes())?S.a.la(t.cloneNode(!0).childNodes):(e=e.text(),S.a.ua(e,r))},S.ba.Ma=new S.ba,S.gc(S.ba.Ma),S.b("nativeTemplateEngine",S.ba),function(){S.$a=function(){var e=this.Hd=function(){if(!l||!l.tmpl)return 0;try{if(0<=l.tmpl.tag.tmpl.open.toString().indexOf("__"))return 2}catch(e){}return 1}();this.renderTemplateSource=function(t,n,r,o){if(o=o||u,r=r||{},2>e)throw Error("Your version of jQuery.tmpl is too old. Please upgrade to jQuery.tmpl 1.0.0pre or later.");var i=t.data("precompiled");return i||(i=t.text()||"",i=l.template(null,"{{ko_with $item.koBindingContext}}"+i+"{{/ko_with}}"),t.data("precompiled",i)),t=[n.$data],n=l.extend({koBindingContext:n},r.templateOptions),(n=l.tmpl(i,t,n)).appendTo(o.createElement("div")),l.fragments={},n},this.createJavaScriptEvaluatorBlock=function(e){return"{{ko_code ((function() { return "+e+" })()) }}"},this.addTemplate=function(e,t){u.write("<script type='text/html' id='"+e+"'>"+t+"<\/script>")},0<e&&(l.tmpl.tag.ko_code={open:"__.push($1 || '');"},l.tmpl.tag.ko_with={open:"with($1) {",close:"} "})},S.$a.prototype=new S.ca,S.$a.prototype.constructor=S.$a;var e=new S.$a;0<e.Hd&&S.gc(e),S.b("jqueryTmplTemplateEngine",S.$a)}()}))}()},function(e,t,n){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=n(7),i=n(0),a=n(1),s=n(17),u=n(9).getInstance(window.ServerData),c=i.String,l=a.Helper,d=o.KeyCode;function f(e){e.preventDefault?e.preventDefault():e.returnValue=!1}t.applyExtensions=function(e){var t,n=1,o={};e.components.loaders.unshift({loadComponent:function(t,n,r){e.components.defaultLoader.loadComponent(t,n,(function(t){var i;n.enableExtensions&&(i=t.createViewModel,t.createViewModel=function(t,n){var r=i(t,n);return function(t,n){var r=n.componentId;if(r&&o[r]){var i=o[r],a=i.parentViewModel,c=i.alias,l=e.unwrap(i.events)||{};c&&("string"==typeof c&&(c=a[c]),e.isWritableObservable(c)&&(c(t),e.utils.domNodeDisposal.addDisposeCallback(n,(function(){c(null)})))),e.utils.objectForEach(l,(function(e,n){e&&n&&("load"===e?n.call(a,t):(e="on"+e.charAt(0).toUpperCase()+e.substr(1),s.isComponentEvent(t[e])&&t[e].subscribe((function(r){return t[e].tracingOptions&&u.logComponentEvent(t,t[e].tracingOptions,e,r),n.apply(a,r)}))))}))}}(r,n.element),r}),r(t)}))}}),t=e.bindingHandlers.component.init,e.bindingHandlers.component.init=function(r,i,a,s,u){var c=e.unwrap(i());if("string"!=typeof c){var l=c.publicMethods,d=c.event;if(c.disabled)return;if(l||d){var f=r.componentId=n++;o[f]={parentViewModel:s,alias:l,events:d},e.utils.domNodeDisposal.addDisposeCallback(r,(function(){delete o[f]}))}}return t(r,i,a,s,u)},e.bindingHandlers.pageViewComponent={init:function(t,n,r,o,i){var a=e.unwrap(n());a.publicMethods=o.viewInterfaces[i.$index()],a.event=a.event||{},a.event.load=o.view_onLoad,a.event.switchView=o.view_onSwitchView;return e.bindingHandlers.component.init(t,(function(){return a}),r,o,i)}},e.bindingHandlers.component.preprocess=function(e){return!e||'"'!==e.charAt(0)&&"'"!==e.charAt(0)?e:c.format("{ name: {0}, params: { } }",e)},e.bindingHandlers.defineGlobals={init:function(t,n,r,o,i){function a(e){var t="";try{var n=document.createElement("div");n.innerHTML=e,n.childNodes.length>0&&n.childNodes[0].value&&(t=n.childNodes[0].value)}catch(r){}return t}var s=e.unwrap(n());s.sFT=a(s.sFT)||s.sFT||a(s.sFTTag);var u=i.extend({svr:s,str:s.str,html:s.html,$location:e.observable()});if(u.$location.subscribe((function(e){e&&document.location.replace(e)})),r.has("bodyCssClass")){var c=l.getIEVersion();if(c){var d={css:{}};d.css["IE_M"+c]=!0,e.applyBindingsToNode(t,d)}if(l.isHighContrast()){var f=l.getHighContrastTheme(),p="black"===f;if(p||"white"===f){var h=p?"theme-dark":"theme-light",g={css:{}};g.css[h]=!0,e.applyBindingsToNode(t,g)}}}return e.applyBindingsToDescendants(u,t),{controlsDescendantBindings:!0}}},e.bindingHandlers.autoSubmit={update:function(t,n){var r=n();e.unwrap(r)&&(e.isWritableObservable(r)&&r(!1),t.submit())}},e.bindingHandlers.postRedirectForm={init:function(e){e.setAttribute("method","POST"),e.setAttribute("aria-hidden","true"),e.setAttribute("target","_top")},update:function(t,n){var r=e.unwrap(n());r&&r.url&&(t.setAttribute("action",r.url),r.target&&t.setAttribute("target",r.target),r.postParams&&e.utils.objectForEach(r.postParams,(function(e,n){"unsafe_"===e.substr(0,7)&&(e=e.substr(7)),null!==n&&n!==undefined||(n="");var r=document.createElement("input");r.setAttribute("type","hidden"),r.setAttribute("name",e),r.setAttribute("value",n),t.appendChild(r)})),t.submit())}},e.bindingHandlers.href={update:function(t,n){e.bindingHandlers.attr.update(t,(function(){return{href:n()}}))}},e.bindingHandlers.placeholder={update:function(t,n){e.bindingHandlers.attr.update(t,(function(){return{placeholder:n()}}))}},e.bindingHandlers.ariaLabel={update:function(t,n){e.bindingHandlers.attr.update(t,(function(){return{"aria-label":n()}}))}},e.bindingHandlers.ariaDescribedBy={update:function(t,n){e.bindingHandlers.attr.update(t,(function(){return{"aria-describedby":n()}}))}},e.bindingHandlers.htmlWithBindings={init:function(){return{controlsDescendantBindings:!0}},update:function(t,n,r,o,i){e.utils.setHtml(t,n());var a=r.get("childBindings");if(a)for(var s in a)if(a.hasOwnProperty(s)){var u=document.getElementById(s);u&&e.applyBindingsToNode(u,a[s],i)}e.applyBindingsToDescendants(i,t)}},e.bindingHandlers.backgroundImage={update:function(e,t){var n=t();function r(t){e.style.backgroundImage=t?c.format("url('{0}')",t):""}var o=window.$Loader,i=new Image;i.onerror=function(){o&&o.On&&o.On(i,!0,r)},i.src=n,r(n)}},e.bindingHandlers.addEventHandlers={init:function(e){var t=window.$Loader;e.onerror=function(){if(t&&t.OnError)return t.OnError(e,(function(t){e.src=t}))}}},e.bindingHandlers.wizardCssCheck={update:function(e,t,n,r,o){if(CSSLoadFail()){var i=document.getElementById("mainDiv");i&&(i.style.display="none")}}},e.bindingHandlers.withProperties={init:function(t,n,r,o,i){var a=i.extend(n);return e.applyBindingsToDescendants(a,t),{controlsDescendantBindings:!0}}},e.bindingHandlers.clickExpr={preprocess:function(e){return"function ($data, $event) { "+e+" }"},init:function(t,n,r,o,i){return e.bindingHandlers.click.init.call(this,t,n,r,o,i)}},e.bindingHandlers.imgSrc={init:function(e){var t=window.$Loader;e.onerror=function(){if(t&&t.On)return t.On(e,!0,(function(t){e.src=t}))},l.isSvgImgSupported()?e.src=e.getAttribute("svgSrc"):e.src=e.getAttribute("pngSrc")}},e.bindingHandlers.svgSrc={update:function(t,n,r){var o=e.unwrap(n());e.bindingHandlers.attr.update(t,(function(){o&&l.isSvgImgSupported()&&(o=o.replace(new RegExp(".png$"),".svg"));var e=r.get("format");if(e)for(var t in e)e.hasOwnProperty(t)&&!e[t]&&(o=o.replace(t,""));return{src:o}}))}},e.bindingHandlers.injectScript={init:function(t,n){var r=e.unwrap(n()),o=document.createElement("script");return o.type="text/javascript",o.src=r,t.appendChild(o),{controlsDescendantBindings:!0}}},e.bindingHandlers.injectIframe={init:function(t,n){var r=e.unwrap(n());if(r&&r.url){var o=document.createElement("iframe");o.height="0",o.width="0",o.style.display="none",o.src=e.unwrap(r.url),r.onload&&(o.onload=function(){r.onload(o)}),t.appendChild(o)}return{controlsDescendantBindings:!0}}},e.bindingHandlers.injectDfpIframe={init:function(t,n){var r=e.unwrap(n());if(r&&r.url){var o=document.createElement("iframe");o.id="iDeviceFingerPrinting",o.setAttribute("style","color:#000000;float:left;visibility:hidden;position:absolute;width:1px;height:1px;left:-10000px;top:-10000px;border:0px"),o.src=e.unwrap(r.url),r.onload&&(o.onload=function(){r.onload(o)}),t.appendChild(o)}return{controlsDescendantBindings:!0}}},e.bindingHandlers.hasFocusEx={init:e.bindingHandlers.hasFocus.init,update:function(t,n,r,o,i){if(e.bindingHandlers.hasFocus.update(t,n,r,o,i),e.unwrap(n())){if(t.value){var a=t.value.length;if("selectionStart"in t)setTimeout((function(){try{t.selectionStart=a,t.selectionEnd=a}catch(e){}}),0);else if("createTextRange"in t){var s=t.createTextRange();s.moveStart("character",a),s.collapse(),s.moveEnd("character",a),s.select()}}t.focus()}else t.blur()}},e.bindingHandlers.preventTabbing={init:function(t,n){var r=e.unwrap(n())||{};"none"!==r.direction&&e.utils.registerEventHandler(t,"keydown",(function(e){return"Tab"!==(e=e||window.event).code&&e.keyCode!==d.Tab||!(!r.direction||"both"===r.direction||"up"===r.direction&&e.shiftKey||"down"===r.direction&&!e.shiftKey)||(f(e),!1)}))}},e.bindingHandlers.ariaHidden={update:function(t,n){e.bindingHandlers.attr.update(t,(function(){return{"aria-hidden":e.unwrap(n())}}))}},e.bindingHandlers.moveOffScreen={update:function(t,n){var o=e.unwrap(n());if("object"!==r(o)){var i=!1!==o;o={setClass:i,setTabIndex:i,setAriaHidden:i}}e.bindingHandlers.css.update(t,(function(){return{moveOffScreen:!1!==o.setClass}})),e.bindingHandlers.attr.update(t,(function(){return{tabindex:!1!==o.setTabIndex?-1:0}})),e.bindingHandlers.ariaHidden.update(t,(function(){return!1!==o.setAriaHidden}))}},e.bindingHandlers.pressEnter={init:function(t,n,r,o,i){var a=e.unwrap(n()),s=i.$data;e.utils.registerEventHandler(t,"keydown",(function(e){return"Enter"!==(e=e||window.event).code&&e.keyCode!==d.Enter||(f(e),a(s,e),!1)}))}},e.bindingHandlers.isScrolledToBottom={init:function(t,n){var r=e.unwrap(n()),o=r.disabled,i=r.value,a=r.sticky;function s(){var e=t.scrollTop+t.offsetHeight>=t.scrollHeight;return i(e),a&&e&&u(),e}function u(){l.removeEventListener(t,"scroll",s),l.removeEventListener(window,"resize",s)}!o&&e.isWritableObservable(i)&&(a&&s()||(l.addEventListener(t,"scroll",s),l.addEventListener(window,"resize",s),e.utils.domNodeDisposal.addDisposeCallback(t,u)))},update:function(t,n){e.unwrap(n()).value()&&(t.scrollTop=t.scrollHeight)}},e.bindingHandlers.animationEnd={init:function(t,n,r,o,i){var a=l.getAnimationEndEventName();a&&e.bindingHandlers.event.init(t,(function(){var t={};return t[a]=e.unwrap(n()),t}),r,o,i)}},e.bindingHandlers.htmlWithMods={init:function(t,n,r){var o=e.unwrap(n());if(o){var i=r.get("htmlMods");if(i&&i.filterLinks){var a=document.createElement("div");a.innerHTML=o;for(var s=a.getElementsByTagName("a"),u=s.length-1;u>=0;u--){var c=s[u],l=c.innerText,d=c.protocol;if("mailto:"===d||"tel:"===d){if(!1!==i.allowContactProtocols)continue;l=p(l,c.pathname)}else l=p(l,c.getAttribute("href"));var f=document.createElement("span");f.innerText=l,c.parentNode.replaceChild(f,c)}o=a.innerHTML}e.utils.setHtml(t,o)}function p(e,t){return e!==t?e+" ("+t+")":e}}},e.bindingHandlers.externalCss={update:function(t,n){e.utils.objectForEach(e.unwrap(n()),(function(n,r){var o=e.unwrap(r);e.utils.toggleDomNodeCssClass(t,n,o),e.utils.toggleDomNodeCssClass(t,"ext-"+n,o)}))}},e.virtualElements.allowedBindings.withProperties=!0,(e.options=e.options||{}).createChildContextWithAs=!0}},function(e,t){e.exports={format:function(e){if(e)for(var t=1;t<arguments.length;t++)e=e.replace(new RegExp("\\{"+(t-1)+"\\}","g"),arguments[t]);return e}}},function(e,t){t.PlatformTimeout=0,t.Timeout=6e5,t.PromiseTimeout=250,t.SupportedKeyAlgorithms=[-7,-257],t.Error={Internal:"InternalError",FidoCreateCallUnexpectedResponse:"FidoCreateCallUnexpectedResponse"}},function(e,t,n){var r=n(4);t.create=function(e){var t,n=!1;function o(){return n=!0,o.eventArgs(Array.prototype.slice.call(arguments)),t}return o.eventArgs=r.observable().extend({notify:"always"}),o.tracingOptions=e,o.subscribe=function(e){o.eventArgs.subscribe((function(n){t=e(n)})),n&&(t=e(o.eventArgs.peek()))},o},t.isComponentEvent=function(e){return e&&r.isObservable(e.eventArgs)}},function(e,t,n){var r=n(4),o=n(1),i=n(0),a=n(19),s=n(2),u=n(8),c=o.Helper,l=i.Object;e.exports=function(e){var t=this,n=e,o=n.bsso,i=n.urlPost,d=n.oPostParams;function f(){d?t.forceSubmit(!0):document.location.replace(t.postUrl())}t.postParams=[],t.forceSubmit=r.observable(!1),t.postUrl=r.observable(),function(){t.postUrl(i),l.forEach(d,(function(e,n){t.postParams.push({unsafe_name:c.htmlUnescape(e),unsafe_value:c.htmlUnescape(n)})}));var e=new a(n);if(e.isEnabled()){var r=new s((function(e){setTimeout(e,o.overallTimeoutMs)}));u.throwUnhandledExceptionOnRejection(s.race([e.pullBrowserSsoCookieAsync(),r]).then((function(e){e&&e.redirectUrl&&t.postUrl(e.redirectUrl)})).then(f,f))}else setTimeout(f,0)}()}},function(e,t,n){var r=n(3),o=n(0),i=n(1),a=n(20),s=n(2),u=n(22),c=n(23),l=window,d=i.QueryString,f=i.Cookies,p=o.Array;e.exports=function(e){var t=e,n=t.bsso||{enabled:!1},o=t.fIsCloudBuild,i=t.fTrimChromeBssoUrl,h=!1!==t.checkApiCanary,g=n.cookieNames,m=null;function v(){var e=new Date;e.setSeconds(e.getSeconds()+60),f.writeWithExpiration(g.ssoPulled,"1",!o,e.toUTCString());var t=l.location.href;return t=d.appendOrReplace(t,"sso_reload","true"),n.reloadOnFailure||"select_account"!==d.extract("prompt").toLowerCase()||(t=d.appendOrReplace(t,"prompt","")),t}function b(e){m.traces.push(e)}function y(e,t){m.data[e]=t}function S(e){return m.result=e.newSessions?"UserList":"Reload",E().then((function(){return e}))}function C(e){return m.result="Error",e instanceof u.Error?"OSError"===e.code&&e.externalData&&e.externalData.error?m.error=e.externalData.error:m.error=e.code:m.error=e,E().then((function(){return s.reject(e)}))}function E(){return new s((function(e){try{l.console&&l.console.info("BSSO Telemetry: "+r.stringify(m))}catch(t){}n.telemetry.url?new a({checkApiCanary:h}).Beacon({url:n.telemetry.url},m,e,e,500):e()}))}this.loginWindowsUserAsync=function(e){return n.enabled?function(e){if(!l.navigator||"function"!=typeof l.navigator.msLaunchUri)return y("BSSO.info","not-supported"),b("window.navigator.msLaunchUri is not available for _loginWindowsUser"),s.reject("bssoNotSupported");var t="abort"===n.initiatePullTimeoutAction;return new c({logMessage:b,logDataPoint:y},n.initiatePullTimeoutMs,n.overallTimeoutMs,t).loginWindowsUserAsync(e).then((function(e){return e.reload?(b("SSO cookie detected. Refreshing page."),v()):s.reject("error")}))}(e).then(S,C):s.reject("bssoDisabled")},this.pullBrowserSsoCookieAsync=function(){var e,t=n.failureRedirectUrl||n.reloadOnFailure,a=n.type;return n.enabled?("windows"===a?e=function(){if(!l.navigator||"function"!=typeof l.navigator.msLaunchUri)return y("BSSO.info","not-supported"),b("window.navigator.msLaunchUri is not available for _pullBrowserSsoCookie"),s.reject("bssoNotSupported");var e=f.getCookie(g.ssoTiles)||n.forceTiles;if(!e&&f.getCookie(g.ssoPulled))return y("BSSO.info","throttled"),b("Cookie pull throttled"),s.reject("throttled");var t="tbauth://login.windows.net?context="+encodeURIComponent(l.location.href.split("/",3).join("/"))+(n.nonce?"&request_nonce="+encodeURIComponent(n.nonce):"")+(n.rid?"&rid="+encodeURIComponent(n.rid):""),o=t;e&&(o=d.appendOrReplace(o,"user_id","*"),f.remove(g.ssoTiles));var i="abort"===n.initiatePullTimeoutAction;return new c({logMessage:b,logDataPoint:y},n.initiatePullTimeoutMs,n.overallTimeoutMs,i).pullBrowserSsoCookieAsync(o).then((function(e){if(e.reload)return b("SSO cookie detected. Refreshing page."),{redirectUrl:v()};if(e.userList){var n=function(e,t){var n=[],o=r.parse(e).users;o&&o.length>0?(p.forEach(o,(function(e){var r={ssoUniqueId:e.unique_id,displayName:e.display_name||"",name:e.upn,isWindowsSso:!0,isSignedIn:!0,url:t};n.push(r)})),b("User list processed. List: "+r.stringify(n))):b("User list is empty.");return n}(e.userList,t);return n.length>0?{newSessions:n}:s.reject("noUsers")}}))}():"chrome"===a&&(e=new u({logMessage:b,logDataPoint:y},n.nonce,"login.microsoftonline.com",o,i).getCookiesAsync().then((function(e){if(!e||!e.length)return s.reject(new u.Error("PageException","Extension returned no cookies"));for(var t=0,n=e.length;t<n;++t){var r=e[t].data;-1!==r.indexOf(";")&&(r=r.substr(0,r.indexOf(";"))),f.write(e[t].name,r,!o)}return b("SSO cookie detected. Refreshing page."),{redirectUrl:v()}})).then(null,(function(e){return f.write(g.aadSso,e.toCookieString(),!o),b("Error: "+e.toString()),s.reject(e)}))),e.then((function(e){return t&&!e.redirectUrl?s.reject("silentPullFailed"):e})).then(S,C).then(null,(function(e){return t?n.reloadOnFailure?{redirectUrl:v()}:{redirectUrl:n.failureRedirectUrl}:s.reject(e)}))):s.reject("bssoDisabled")},this.isEnabled=function(){return!!n.enabled},n.enabled&&(m={result:null,error:null,type:n.telemetry.type||null,data:{},traces:[]},n.initiatePullTimeoutMs=n.initiatePullTimeoutMs||n.overallTimeoutMs,n.initiatePullTimeoutAction=n.initiatePullTimeoutAction||"abort",b("BrowserSSO Initialized"))}},function(e,t,n){var r=n(3),o=n(21),i=n(7),a=n(0),s=n(1),u=n(9).getInstance(window.ServerData),c=n(5),l=window,d=l.$Config||l.ServerData||{},f=a.Object,p=s.QueryString;e.exports=function(e){var t=this,n=!1!==(e=e||{}).checkApiCanary,a=e.withCredentials||!1,s=e.breakCache||!1,h=e.responseType||"",g=e.notifyOnClientAbort||!1,m=l.ServerData.fSasEndAuthPostToGetSwitch,v=l.ServerData.fFixUICrashForApiRequestHandler;function b(e){var t={hpgid:d.hpgid||0,hpgact:d.hpgact||0};return e||(t.Accept="application/json",n&&d.apiCanary&&(t.canary=d.apiCanary)),d.correlationId&&(t["client-request-id"]=d.correlationId),d.sessionId&&(t.hpgrequestid=d.sessionId),t}function y(e){var t=e;if(e&&"string"!=typeof e){var n={};f.forEach(e,(function(e,t){"unsafe_"===e.substr(0,7)&&(e=e.substr(7)),n[e]=t})),t=r.stringify(n)}return t&&(t=t.replace(/\?/g,"\\u003F")),t}function S(e){e.headers=b(),e.withCredentials=a,e.breakCache=s,e.responseType=h}function C(e,t,n,r,o,i){var a=null;if(t){var s=t.eventOptions||{};if(s.eventId=t.eventId||s.eventId,s.hasOwnProperty("hidingMode")||(s.hidingMode=c.HidingMode.None),s.eventId){(a={}).eventType=n,a.eventId=s.eventId,a.eventLevel=s.eventLevel||c.EventLevel.ApiRequest;var l={};l.requestTimeout=r,o&&(l.contentType=o),l.requestType=n,i&&(l.noCallback=!0),a.eventArgs=l,a.eventOptions=s,u.traceBeginRequest(e,a)}}e.eventData=a}function E(e,t,n,r,o){u.traceEndRequest(e,t,n,r,o)}function w(e,t){var n={};return e&&(n.xhr_status=e.status),n.textStatus=t,n}t.Errors=[],t.Json=function(e,o,a,s,u,c,l){var f=!(!a&&!s),p=(new Date).getTime(),h=e.url;function b(e,t){var n={};if(500===e.status)try{n=r.parse(e.responseText)||{}}catch(s){}if(!n.error){var o=!1,i=8e3,a="Request Failed -- No Response from Server";switch(t){case"timeout":i=8001,a="Timeout Error",o=!0;break;case"abort":i=8002,a="Aborted";break;case"error":e.status>=400&&(o=!0);break;case"parsererror":a="Unable to parse response",o=!0}n.error={code:i,message:a,debugMessage:"(xhr status "+e.status+") xhr.responseText: "+e.responseText,stackTrace:"",isFatal:o}}return n}function S(e){var n,o=(e=e||{}).error||null,i={startTime:p,endTime:(new Date).getTime()};if(e.apiCanary&&(d.apiCanary=e.apiCanary,delete e.apiCanary),o){n=(n=o.stackTrace)&&n.encodeJson?n.encodeJson():"";var u=r.stringify({code:o.code,message:o.message,debug:o.debugMessage,stacktrace:n,requestUrl:h});t.Errors.push(u),t.Errors.length>100&&t.Errors.shift(),(8002!==o.code||g)&&s&&s(e,i)}else a&&a(e,i)}function C(e){return setTimeout((function(){S({error:{code:e,message:"Request Failed!",isFatal:!0}})}),0),null}if(n&&!d.apiCanary)return v?C(8002):(setTimeout((function(){S({error:{code:8002,message:"Request Failed!",isFatal:!0}})}),0),null);if(m&&null===o&&null!==c)t.Get(e,i.ContentType.Json,(function(e,t){if(f)if(v)try{S(r.parse(t))}catch(n){C(8e3)}else S(r.parse(t))}),(function(e,t,n,r){f&&S(b(t,n))}),u,c,l);else{var E=y(o);t.Post(e,i.ContentType.Json,E,(function(e,t){if(f)if(v)try{S(r.parse(t))}catch(n){C(8e3)}else S(r.parse(t))}),(function(e,t,n,r){f&&S(b(t,n))}),u)}},t.Post=function(e,n,r,i,a,s){var u=e.url,c={},l=!1;i||a||(l=!0),C(c,e,o.RequestType.Post,s,n,l);var d={targetUrl:u,contentType:n,data:r,requestType:o.RequestType.Post,timeout:s||3e4,successCallback:function(e,t){E(c,"Success",t,!0,(function(){i&&i(e,t)}))},failureCallback:function(e,t,n){E(c,"Failed",w(t,n),!1,(function(){a&&a(e,t,n)}))},timeoutCallback:function(e,t,n){E(c,"Timeout",w(t,n),!1,(function(){a&&a(e,t,n)}))}};S(d),o.Handler.call(t,d),t.sendRequest()},t.Get=function(e,n,r,i,a,s,u){var c=e.url,l={},d=!1;r||i||(d=!0),C(l,e,o.RequestType.Get,a,n,d);var f={targetUrl:c,contentType:n,requestType:o.RequestType.Get,timeout:a||3e4,successCallback:function(e,t){E(l,"Success",t,!0,(function(){r&&r(e,t)}))},failureCallback:function(e,t,n){E(l,"Failed",w(t,n),!1,(function(){i&&i(e,t,n)}))},timeoutCallback:function(e,t,n){E(l,"Timeout",w(t,n),!1,(function(){i&&i(e,t,n)}))}};if(S(f),m){if(s)for(var h in s)s.hasOwnProperty(h)&&(f.headers[h]=s[h]);if(u){var g=p.add(c,u);f.targetUrl=g}}o.Handler.call(t,f),t.sendRequest()},t.Beacon=function(e,n,r,o,i){var a=[],s=b(!0);f.forEach(s,(function(e,t){a.push([e,t])}));var u=e.url;if(u=p.add(u,a),e.url=u,navigator.sendBeacon){var c={};C(c,e,"Beacon",i,null,!1);var l=y(n),d=navigator.sendBeacon(u,l);E(c,d?"Success":"Failed",null,d,(function(){d&&r?r():!d&&o&&o()}))}else t.Json(e,n,r,o,i)}}},function(e,t,n){var r=n(3),o=n(1),i=n(0),a=i.Object,s=o.HttpCode,u=window;t.RequestType={Post:"POST",Get:"GET"};var c=t.State={Unsupported:-1,Unsent:0,Done:4,Timeout:5};t.Event={OnSuccess:"ajaxsuccess",OnError:"ajaxerror",OnTimeout:"ajaxtimeout"},t.Helper={generateRequestString:function(e){var t="";return e&&a.forEach(e,(function(e,n){(n||""===n)&&(t.length>0&&(t+="&"),t+=e+"="+n)})),t}},t.Handler=function(e){var t=this,n="",l=[],d=null,f=null,p=!1,h=!0,g=null,m=!1,v=!!e.contentType,b=!!e.headers,y=!!e.headerValue,S=e.data||"",C=e.targetUrl||"",E=e.requestType||"",w=!1!==e.isAsync,P=e.timeout||0,T=e.username||"",A=e.password||"",_=e.contentType||"application/x-www-form-urlencoded",x=e.withCredentials||!1,O=e.breakCache||!1,I=e.responseType||"",N=e.headers||{},D=e.successCallback,k=e.failureCallback,R=e.timeoutCallback;function L(e,r){e||t.isSuccess()?D&&D(d,n):(r||!t.isSuccess()&&!p)&&k&&k(d,f,f.statusText)}function M(){if(g=null,p=!0,t.cancel(),R){var e={status:s.Timeout,statusText:"timeout"};R(d,e,e.statusText)}}function F(e){if(j(),!t.isComplete()&&!f.canceled&&k){var n={status:s.ClientClosedRequest,statusText:"abort"};k(e,n,n.statusText)}}function U(e){t.isComplete()||H(e)}function B(e){t.isComplete()&&!p&&H(e)}function H(e){j(),n=f.responseText,d=e,L()}function j(){g&&(clearTimeout(g),g=null)}function V(e){j(),n=f.responseText,L(e,!e)}t.sendRequest=function(e){d=e,function(){p=!1;var e="withCredentials"in new XMLHttpRequest;if(!m||e){var n=C;(f=new XMLHttpRequest).onreadystatechange=B,f.addEventListener&&(f.addEventListener("abort",F),f.addEventListener("error",U)),O&&(n=o.QueryString.appendOrReplace(n,"_",(new Date).getTime())),T.length>0?f.open(E,n,w,T,A):f.open(E,n,w),t.clearResponse(),a.forEach(N,(function(e,t){f.setRequestHeader(e,t)})),f.responseType=I,f.withCredentials=x}else u.XDomainRequest?!w||T||A||v||b||y||x?h=!1:((f=new u.XDomainRequest).onerror=function(){V(!1)},f.onload=function(){V(!0)},f.open(E,C),t.clearResponse()):h=!1}(),h&&(P>0&&(g=setTimeout((function(){M.call(t)}),P)),f.send(S))},t.getState=function(){return h?p?c.Timeout:f?f.readyState:c.Unsent:c.Unsupported},t.getStatus=function(){return p?s.Timeout:f?f.status:0},t.cancel=function(){f&&(f.canceled=!0,f.abort())},t.getResponseJson=function(){return n?r.parse(n):{}},t.isComplete=function(){return t.getState()===c.Done||t.getState()===c.Timeout},t.isSuccess=function(){return t.isComplete()&&l[t.getStatus()]},t.clearResponse=function(){n=""},function(){N["Content-type"]=_,l[s.Ok]=!0,l[s.NotModified]=!0,l[s.Timeout]=!1;var e=i.String.extractDomainFromUrl(C);e&&(m=i.String.extractDomainFromUrl(document.location.href)!==e)}()}},function(e,t,n){var r=n(3),o=n(1),i=n(2),a=window,s=a.document,u=o.QueryString,c=o.Cookies,l=-**********;function d(e,t,n,r,o){var c="53ee284d-920a-4b59-9d30-a60315b26836",f=e,p=t,h=n,g=r,m=o,v={},b=0,y=null,S=null,C=null,E=null;function w(e){if(e.source===a){var t=e.data,n=t&&t.channel,r=t&&t.responseId,o=t&&t.body,i=o&&o.method;if(n===c&&r&&("CreateProviderAsync"===i||"Response"===i)){f.logMessage("Received message for method "+i);var s=v[r];delete v[r],setTimeout((function(){s(o.response||{})}),0)}}}function P(e,t){return new i((function(n){var r={channel:c,responseId:++b,body:e};t&&(r.extensionId=t),v[r.responseId]=n,f.logMessage("Sending message for method "+(e||{}).method||!1),a.postMessage(r,"*")})).then((function(e){return"Success"===e.status?i.resolve(e.result||{}):i.reject(new d.Error(e.code,e.description,e.ext))}))}function T(){return E||(a.addEventListener&&a.addEventListener("message",w),f.logMessage("Creating ChromeBrowserCore provider"),E=P({method:"CreateProviderAsync",response:{status:"Success"}}).then(A)),E}function A(){for(var e=null,t=y.firstChild;t;)!t.id||null!==e&&"ppnbnpeolgkicgegkbkbjmhlideopiji"!==t.id||(e=t.id),y.removeChild(t),t=y.firstChild;if(!e)throw new d.Error("NoExtension","Extension is not installed.",null);return f.logDataPoint("extension.id",e),f.logMessage("Using Chrome extension with id "+e),e}function _(e){return"OSError"===e.code&&e.externalData&&e.externalData.error===l?(f.logMessage("GetCookies method not found, falling back to GetCookie"),T().then((function(e){return P({method:"GetCookie",uri:S},e)}))):i.reject(e)}function x(e){var t=e.response||[];if(t&&t.length)for(var n=0,r=t.length;n<r;++n){var o=o=t[n].data||"",i=o.indexOf(";");-1!==i&&(o=o.substr(0,i)),t[n].data=o+C}return t}this.getCookiesAsync=function(){return T().then((function(e){return f.logMessage("Pulling SSO cookies"),P({method:"GetCookies",uri:S},e).then(null,_).then(x)}))},function(){if(S=u.appendOrReplace(a.location.href,"sso_nonce",p),m){var e=u.parse(S);e.fragment&&(e.fragment=null,S=u.join(e))}C="; path=/; domain="+h+(g?"":"; secure");(y=s.getElementById("ch-53ee284d-920a-4b59-9d30-a60315b26836"))||((y=s.createElement("div")).id="ch-53ee284d-920a-4b59-9d30-a60315b26836",s.body.appendChild(y))}()}d.Error=function(e,t,n){var o=e,i=t,a=n;this.code=o,this.description=i,this.externalData=a,this.toString=function(){var e="ChromeBrowserCore error "+(o||"")+": "+(i||"");return a&&(e+=" (ext: "+r.stringify(a)+")"),e},this.toCookieString=function(){var e="NA";if(c.isCookieSafeValue(o)){e+="|"+o;var t=a?encodeURIComponent(r.stringify(a)):null;t&&c.isCookieSafeValue(t)&&(e+="|"+t)}return e}},e.exports=d},function(e,t,n){var r=n(1),o=n(2),i=window,a=r.Cookies;e.exports=function(e,t,n,r){var s=e,u=t,c=n,l=r;function d(e,t,n){var r=(new Date).getTime();return function(e,t,n){return new o((function(t,n){i.navigator.msLaunchUri(e,t,n),setTimeout((function(){n("timeout")}),u)})).then((function(){var e=(new Date).getTime()-n;s.logDataPoint("msLaunchUri.success.ms",e),s.logMessage(t+" initiated successfully (took "+e+" ms)")}),(function(e){if("timeout"===e)s.logDataPoint("msLaunchUri.response",l?"timeout":"timeout-continue"),s.logMessage("");else{var r=(new Date).getTime()-n;s.logDataPoint("msLaunchUri.failure.ms",r),s.logMessage(t+" was NOT initiated successfully (took "+r+" ms)")}if("timeout"!==e||l)return o.reject(e||"noHandler")}))}(e,t,r).then((function(){return function(e,t,n){return new o((function(r,o){var i=setInterval((function(){var e=n?null:a.getCookie("ESTSUSERLIST"),o=a.getCookie("ESTSSSO");(e||o)&&(clearInterval(i),s.logDataPoint((e?"ESTSUSERLIST":"ESTSSSO")+".cookie.ms",(new Date).getTime()-t),e?(s.logMessage("Users list cookie detected"),a.remove("ESTSUSERLIST"),r({userList:decodeURIComponent(e).replace(/\+/g," ")})):r({reload:!0}))}),250);setTimeout((function(){clearInterval(i),s.logDataPoint("TB.response.timeout.ms",(new Date).getTime()-t),s.logMessage(e+" timed out."),o("timeout")}),c)}))}(t,r,n)}))}this.pullBrowserSsoCookieAsync=function(e){return d(e,"cookie pull",!1)},this.loginWindowsUserAsync=function(e){return d(e,"Windows user login",!0)}}},function(e,t){e.exports='<form aria-hidden="true" name="f1" autocomplete="off" method="POST" data-bind="\n    autoSubmit: forceSubmit,\n    attr: { action: postUrl }">\n\n    \x3c!-- ko foreach: postParams --\x3e\n    <input type="hidden" data-bind="attr: { name: $data.unsafe_name }, value: $data.unsafe_value" />\n    \x3c!-- /ko --\x3e\n</form>'}]),window.__BssoInterrupt_Core=!0;
//# sourceMappingURL=9dfb4e9986e1138c3ac2.map