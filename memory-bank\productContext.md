# Product Context

## Why This Project Exists
This project exists to automate daily routine tasks, reducing manual effort and improving efficiency.

## Problems It Solves
- Automates the execution of daily tasks that would otherwise require manual intervention.
- Specifically handles data downloading, processing, and subsequent notification.

## How It Should Work
The general workflow involves:
1.  **Data Download**: Scripts will fetch necessary data from relevant sources.
2.  **Data Processing**: Downloaded data will be processed according to defined logic.
3.  **Email Notification**: Upon completion of data processing, an email notification will be sent, likely summarizing the results or status.

## User Experience Goals
- **Reliability**: The automation should run consistently without errors.
- **Timeliness**: Tasks should be completed within expected timeframes.
- **Clarity**: Notifications should be clear and provide all necessary information.
- **Efficiency**: Significantly reduce the time and effort spent on these routine tasks.
