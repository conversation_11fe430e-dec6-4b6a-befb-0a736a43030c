/*!
 * ------------------------------------------- START OF THIRD PARTY NOTICE -----------------------------------------
 * 
 * This file is based on or incorporates material from the projects listed below (Third Party IP). The original copyright notice and the license under which Microsoft received such Third Party IP, are set forth below. Such licenses and notices are provided for informational purposes only. Microsoft licenses the Third Party IP to you under the licensing terms for the Microsoft product. Microsoft reserves all other rights not expressly granted under this agreement, whether by implication, estoppel or otherwise.
 * 
 *   json2.js (2016-05-01)
 *   https://github.com/douglascrockford/JSON-js
 *   License: Public Domain
 * 
 * Provided for Informational Purposes Only
 * 
 * ----------------------------------------------- END OF THIRD PARTY NOTICE ------------------------------------------
*/!function(e){function n(n){for(var t,i,o=n[0],r=n[1],s=0,c=[];s<o.length;s++)i=o[s],Object.prototype.hasOwnProperty.call(a,i)&&a[i]&&c.push(a[i][0]),a[i]=0;for(t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);for(d&&d(n);c.length;)c.shift()()}var t,i={},a={24:0};function o(n){if(i[n])return i[n].exports;var t=i[n]={i:n,l:!1,exports:{}};return e[n].call(t.exports,t,t.exports,o),t.l=!0,t.exports}Function.prototype.bind||(t=Array.prototype.slice,Function.prototype.bind=function(e){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var n=t.call(arguments,1),i=n.length,a=this,o=function(){},r=function(){return n.length=i,n.push.apply(n,arguments),a.apply(o.prototype.isPrototypeOf(this)?this:e,n)};return this.prototype&&(o.prototype=this.prototype),r.prototype=new o,r}),document.head=document.head||document.getElementsByTagName("head")[0],function(){function e(n){var t=this,i=0,a=null,o=[];function r(){if(o.length>0){var e=o.slice();o=[],setTimeout((function(){for(var n=0,t=e.length;n<t;++n)e[n]()}),0)}}function s(e){0===i&&(a=e,i=1,r())}function c(e){0===i&&(a=e,i=2,r())}t.then=function(n,t){return new e((function(s,c){!function(n,t,s,c){o.push((function(){var o;try{o=1===i?"function"==typeof n?n(a):a:"function"==typeof t?t(a):a}catch(r){return void c(r)}o instanceof e?o.then(s,c):2===i&&"function"!=typeof t?c(o):s(o)})),0!==i&&r()}(n,t,s,c)}))},t["catch"]=function(e){return t.then(null,e)},function(){if("function"!=typeof n)throw new TypeError("Promise: argument is not a Function object");try{n(s,c)}catch(e){c(e)}}()}function n(e,n,t,i,a){return function(o){e[n]=i?o:a?{status:"fulfilled",value:o}:{status:"rejected",reason:o},t()}}function t(t,i){return t&&t.length?new e((function(a,o){for(var r=[],s=0,c=0,d=t.length;c<d;++c){var l=t[c];if(l instanceof e){s++;var u=function(){0==--s&&a(r)};i?l.then(n(r,c,u,i),o):l.then(n(r,c,u,i,!0),n(r,c,u,i,!1))}else r[c]=l}0===s&&setTimeout((function(){a(r)}),0)})):e.resolve([])}function i(e,n){return function(){e(n)}}e.all=function(e){return t(e,!0)},e.allSettled=function(e){return t(e,!1)},e.race=function(n){return new e((function(t,a){if(n&&n.length)for(var o=0,r=n.length;o<r;++o){var s=n[o];s instanceof e?s.then(t,a):setTimeout(i(t,s),0)}}))},e.reject=function(n){return new e((function(e,t){t(n)}))},e.resolve=function(n){return n instanceof e?n:n&&"function"==typeof n.then?new e((function(e,t){n.then(e,t)})):new e((function(e){e(n)}))},window.Promise||(window.Promise=e),window.Promise.all||(window.Promise.all=e.all),window.Promise.allSettled||(window.Promise.allSettled=e.allSettled),window.Promise.race||(window.Promise.race=e.race),window.Promise.reject||(window.Promise.reject=e.reject),window.Promise.resolve||(window.Promise.resolve=e.resolve)}(),o.e=function(e){var n=[],t=a[e];if(0!==t)if(t)n.push(t[2]);else{var i=new Promise((function(n,i){t=a[e]=[n,i]}));n.push(t[2]=i);var r=window.ServerData,s=r&&r.loader&&r.loader.cdnRoots||[],c=r&&r.slMaxRetry?r.slMaxRetry:s.length-1,d=new Error;var l=function u(n,t){var i,o=document.createElement("script");o.charset="utf-8",o.timeout=120;var r=document.querySelector("script[nonce]");if(r){var l=r.nonce||r.getAttribute("nonce");o.setAttribute("nonce",l)}o.src=n,i=function(i){o.onerror=o.onload=null,clearTimeout(p);var r=a[e];if(0!==r)if(r)if(c<=0||t===c){var l=i&&("load"===i.type?"missing":i.type),f=i&&i.target&&i.target.src;d.message="Loading chunk "+e+" failed after "+(c+1)+" tries.\n("+l+": "+f+")",d.name="ChunkLoadError",d.type=l,d.request=f,r[1](d),a[e]=undefined,SRSRetry&&(window.external.notify(JSON.stringify({type:"invoke",value:{name:"CloudExperienceHost.Telemetry.logEvent",args:["MSA.ResourceDownloadError","Javascript failed to download on IDUX: "+f],context:"criticalError1"}})),window.external.notify(JSON.stringify({type:"event",value:{name:"CloudExperienceHost.done",data:"fail"}})))}else{var g=u(function(e,n){if(!n)return e;for(var t=0;t<n.length;t++)if(0==e.indexOf(n[t]))return n[(t+1)%n.length]+e.substring(n[t].length);return e}(n,s),t+1);document.head.appendChild(g)}else a[e]=undefined};var p=setTimeout((function(){i({type:"timeout",target:o})}),12e4);return o.onerror=o.onload=i,o}(function(e){return o.p+"content/js/asyncchunk/convergedlogin_p"+({0:"aadfedconflict",1:"alt",2:"certificateinterstitial",3:"clienttracing",4:"confirmrecoveryusername",5:"confirmsend",6:"confirmsignup",7:"credentialpicker",8:"customizationloader",9:"estslogin_accesspass",10:"estslogin_remoteloginpoll",11:"estslogin_searchorganization",12:"estslogin_signupblocked",13:"estslogin_signupusername",14:"estslogin_tenantdisambiguation",15:"fedconflict",16:"fedlink",17:"fetchsessionsprogress",18:"fido",19:"idpdisambiguation",20:"idpredirect",21:"idpredirectspeedbump",22:"learnmore",23:"learnmoreofflineaccount",25:"onetimecode",26:"password",27:"phonedisambiguation",28:"pop",29:"proofconfirmation",30:"prooffedconflict",31:"qrcodepin",32:"qrcodescan",33:"remoteconnectcanaryvalidation",34:"remoteconnectlocation",35:"remotengc",36:"resetpasswordsplitter",37:"stringcustomizationhelper",38:"tiles",39:"vcpresentation",40:"verticalsplittemplate",41:"viewagreement",42:"wammessagehandler",43:"webnativebridge"}[e]||e)+"_"+{0:"9069d158af20f4813f8e",1:"ec91386f404c143a6657",2:"3a9ed34e171d96ba70ba",3:"ef3518251d1ecc65b2b2",4:"388fd3456e2683f38655",5:"907a758945ea5d443737",6:"98372c68e00f95dbce9b",7:"ccc6d492040d2c40f68f",8:"41f36656d3c0bb04c90c",9:"a76e4d86ffb446263c57",10:"3c24aaf63cf7328d633f",11:"e02b486ea38c74743e67",12:"9a5cb445c530ca20a980",13:"a75c81d009ad98d2f93b",14:"41f17377c42fa22b9961",15:"1fb1566998c022808f68",16:"2175ea13c209f457c4b6",17:"268b957e4bf672b8fe6b",18:"ea9f5d0a4d8dc6319dc4",19:"827cf6d373124a0add66",20:"de30c7ca106b3caea020",21:"e36f5b2dd12026dd1f9b",22:"7d93bbac6b7d7da122cb",23:"5db697b80dfc6717e7d5",25:"9269686e5e76734e916a",26:"aa031712c04fd9c63636",27:"b043815aa80ed64fc14c",28:"14f7ce88a5130f14a44d",29:"b6db49107f8551a4d4dd",30:"de3a7fc3e673fd333660",31:"c1f5492b1a40ac101ae3",32:"d8890a1f43f3d282cdad",33:"31320f6ac61845c283b7",34:"fec902b1f5d528842aa6",35:"01b6aa1e254e8b0ce9c7",36:"1c32614b25a0501e8bb6",37:"4f75990aeef30238698e",38:"4648c8b30e3bab4c59ab",39:"ead0b432f4cb257bd231",40:"b6361b4efbe96e8fb9c4",41:"b6a20a5145df8cba8138",42:"593350e7ce2d02e371de",43:"297f7f9b825d16fb96ed"}[e]+".js"}(e),0);document.head.appendChild(l)}return Promise.all(n)},o.m=e,o.c=i,o.d=function(e,n,t){o.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,n){if(1&n&&(e=o(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(o.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)o.d(t,i,function(n){return e[n]}.bind(null,i));return t},o.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(n,"a",n),n},o.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},o.p="",o.oe=function(e){throw console.error(e),e};var r=window.webpackJsonp=window.webpackJsonp||[],s=r.push.bind(r);r.push=n,r=r.slice();for(var c=0;c<r.length;c++)n(r[c]);var d=s;o(o.s=68)}([function(e,n){n.UsernameMaxLength=113,n.SATOTPV1Length=6,n.SATOTPLength=8,n.SAEOTPLength=8,n.PhoneNumberConfirmationLength=4,n.OneTimeCodeDefaultLength=16,n.OneTimeCodeMaxAcceptedLength=10,n.PCExperienceQS="pcexp",n.PCExperienceDisabled=n.PCExperienceQS+"=false",n.NotPreferredCredentialQs="npc",n.AnimationTimeout=700,n.PageSummaryVersion=1,n.GuidTemplate="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",n.proofUpCorrelationIdParamName="x-ms-correlation-id",n.Regex={PhoneNumberValidation:/^[0-9 ()[\].\-#*/+]+$/},n.ProofUpRedirectLandingView={AccountCompromised:1,RiskySession:2},n.LoginMode={None:0,Login:1,ForceCredType:3,LWAConsent:4,GenericError:5,ForceSignin:6,OTS:7,HIP_Login:8,HIP_Lockout:9,InviteBlocked:10,SwitchUser:11,LWADelegation:12,ServiceBlocked:13,IDPFailed:14,StrongAuthOTC:16,StrongAuthMobileOTC:25,Finish:27,LoginWizard_Login:28,StrongAuthWABOTC:30,LoginWizard_HIP_Login:32,LoginWizard_Finish:34,LoginMobile:36,ForceSigninMobile:37,GenericErrorMobile:38,LoginHost:39,ForceSigninHost:40,GenericErrorHost:42,StrongAuthHostOTC:43,HIP_LoginHost:45,HIP_LoginMobile:46,HIP_LockoutHost:47,HIP_LockoutMobile:48,SwitchUserHost:49,LoginXbox_Login:50,HIP_LoginXbox:51,FinishXbox:52,IfExistsXbox:53,StartIfExistsXbox:54,StrongAuthXboxOTC:55,LoginWPWiz_Login:56,LoginWPWiz_HIP_Login:57,LoginWPWiz_Finish:58,StrongAuthWizOTC:59,StrongAuthWPWizOTC:60,FinishWPWiz:61,SwitchUserMobile:62,LoginWPWiz_PhoneSignIn:63,LoginWPWiz_HIP_PhoneSignIn:64,Login_PhoneSignIn:65,Login_HIP_PhoneSignIn:66,LoginHost_PhoneSignIn:67,LoginHost_HIP_PhoneSignIn:68,LoginMobile_PhoneSignIn:69,LoginMobile_HIP_PhoneSignIn:70,LoginWizard_PhoneSignIn:71,LoginWizard_HIP_PhoneSignIn:72,LoginXbox_PhoneSignIn:73,LoginXbox_HIP_PhoneSignIn:74,LoginWin10:75,HIP_LoginWin10:76,FinishWin10:77,FinishBlockedWin10:78,LoginWin10_PhoneSignIn:79,HIP_LoginWin10_PhoneSignIn:80,FinishWin10_TokenBroker:81,SwitchUserWin10:82,ForceSignInXbox:88,LoginClientSDK_Login:92,LoginClientSDK_HIP_Login:93,LoginClientSDK_Finish:94,StrongAuthClientSDKOTC:95,FinishClientSDK:96,LoginClientSDK_PhoneSignIn:97,LoginClientSDK_HIP_PhoneSignIn:98,Win10InclusiveOOBE_Finish:99,Win10InclusiveOOBE_FinishBlocked:100,Tiles:102,RemoteConnect:103,FedConflict:105,Win10Host_Login:106,Win10Host_Login_PhoneSignin:107,Win10Host_Finish:108,Win10Host_StrongAuth:109,Win10Host_HIP_Login:110,Fido:111,Win10Host_HIP_Login_PhoneSignIn:112,FedLink:113,UserCredentialPolicyBlocked:114,BindFailed:115,Win10HostOOBE_HIP_Login:116,Win10HostOOBE_HIP_Login_PhoneSignIn:117,AadFedConflict:118,ProofFedConflict:119,FedBoundLink:120,FetchSessionsProgress:121,Win10Host_TransferLogin:122,TransferLogin:123,Signup:124,CredentialPicker:129,SignupBlocked:132,QrCodePin:133},n.LoginBody={Login_OTC:5},n.SessionPullFlags={Msa:1,Dsso:2},n.ResponseMode={Fragment:"Fragment",Query:"Query",FormPost:"FormPost",NotSpecified:"NotSpecified",Unsupported:"Unsupported"},n.ResponseType={code:"code",token:"token",id_token:"id_token",none:"none"},n.PaginatedState={Previous:-1,Unknown:0,Username:1,Password:2,OneTimeCode:3,RemoteNGC:4,PhoneDisambiguation:5,LwaConsent:6,IdpDisambiguation:7,IdpRedirect:8,ViewAgreement:10,LearnMore:11,Tiles:12,ConfirmSend:13,RemoteConnectCode:14,RemoteLoginPolling:15,BindRedirect:16,TermsOfUse:17,DesktopSsoProgress:18,ResetPasswordSplitter:19,Kmsi:20,CheckPasswordType:21,ChangePassword:22,Fido:23,CredentialPicker:24,Consent:25,Error:26,ConfirmSignup:27,ConfirmRecoverUsername:28,ConfirmConsentSelection:29,FedConflict:30,ProofUpRedirect:32,ProofUpRedirectLanding:33,ConditionalAccessInstallBroker:34,ConditionalAccessWorkplaceJoin:35,ConditionalAccessError:36,CreateFido:37,FedLink:38,FedLinkComplete:40,IdpRedirectSpeedbump:41,TransferLogin:42,Cmsi:43,ProofConfirmation:44,MessagePrompt:45,FinishError:46,Hip:48,LearnMoreOfflineAccount:49,TenantDisambiguation:50,AadFedConflict:51,RemoteConnectCanaryValidation:52,PartnerCanaryValidation:53,ProofFedConflict:54,FetchSessionsProgress:55,AccessPass:56,SignupUsername:57,ReportSuspiciousApp:58,MoreInfo:59,AuthenticatorAddAccountView:60,SignupCredentialPicker:61,LoginError:62,SearchOrganization:63,Ptca:64,GuestConsent:65,RemoteConnectLocation:66,AttributeCollection:67,RdpDevicePrompt:68,GuestConsentConnect:69,SeeHowDataIsManaged:70,SecurityDefaultsUpsell:71,SecurityDefaultsUpsellOptOut:72,SecurityDefaultsUpsellAutoEnabled:73,WebNativeBridge:74,TransferLoginChallengePin:75,RecoveryCredentialPicker:76,OneTimeCodeRecovery:77,PhoneLinkLearnMore:78,PhoneLinkSupport:79,CertificateInterstitialView:80,ConsentResourceApp:81,SignupBlocked:82,VCPresentation:83,QrCodeScan:84,QrCodePin:85,AttributeCollectionRedirect:86},n.PostType={Password:11,Federation:13,SHA1:15,StrongAuth:18,StrongAuthTOTP:19,LWAConsent:30,PasswordInline:20,RemoteNGC:21,SessionApproval:22,NGC:23,OtcNoPassword:24,RemoteConnect_NativePlatform:25,OTC:27,Kmsi:28,TransferTokenOTC:31,QrCodePin:32},n.UserProperty={USERNAME:"login",ERROR_CODE:"HR",ERR_MSG:"ErrorMessage",EXT_ERROR:"ExtErr",ERR_URL:"ErrUrl",DATOKEN:"DAToken",DA_SESKEY:"DASessionKey",DA_START:"DAStartTime",DA_EXPIRE:"DAExpires",STS_ILFT:"STSInlineFlowToken",SIGNINNAME:"SigninName",FIRST_NAME:"LastName",LAST_NAME:"FirstName",TILE_URL:"TileUrl",CID:"CID",PUID:"PUID"},n.DEFAULT_CHANNEL_ID="53ee284d-920a-4b59-9d30-a60315b26836",n.DEFAULT_PREFERRED_EXTENSION_ID="ppnbnpeolgkicgegkbkbjmhlideopiji",n.WebNativeBridgeSuccess="Success",n.DFPPrefix="dfp:",n.Error={S_OK:"0",InvalidRealmDiscLogin:10,UsernameInvalid:1e3,PasswordEmpty:1001,HIPEmpty:1002,AltEmailInvalid:1005,PhoneInvalid:1006,SAContainsName:1007,OTCEmpty:1009,OTCInvalid:1010,NotEnoughProofs:1013,PhoneEmpty:1015,FedUser:1016,FedUserConflict:1017,FedUserInviteBlocked:1018,EmptyFields:1020,PhoneHasSpecialChars:1021,AutoVerifyNoCodeSent:1022,ProofConfirmationEmpty:1023,ProofConfirmationInvalid:1024,TOTPInvalid:1025,SessionNotApproved:1026,PhoneNumberInvalid:1027,PhoneFormattingInvalid:1028,PollingTimedOut:1029,SendNotificationFailed:1030,Server_MessageOnly:9999,PP_E_DB_MEMBERDOESNOTEXIST:"CFFFFC15",PP_E_EXCLUDED:"80041010",PP_E_MEMBER_LOCKED:"80041011",PP_E_BAD_PASSWORD:"80041012",PP_E_MISSING_MEMBERNAME:"80041031",PP_E_MISSING_PASSWORD:"80041032",PP_E_FEDERATION_INLINELOGIN_DISALLOWED:"800478AC",PP_E_PE_RULEFALSE:"8004490C",PP_E_MOBILECREDS_PHONENUMBER_BLANK:"80045801",PP_E_MOBILECREDS_PHONENUMBER_TOOSHORT:"80045806",PP_E_MOBILECREDS_PHONENUMBER_TOOLONG:"80045807",PP_E_MOBILECREDS_PHONENUMBER_INVALID:"80045800",PP_E_NAME_BLANK:"80041100",PP_E_EMAIL_INCOMPLETE:"8004110D",PP_E_EMAIL_INVALID:"8004110B",PP_E_NAME_TOO_SHORT:"80041101",PP_E_NAME_INVALID:"80041103",PP_E_INVALIDARG:"80048388",PP_E_SA_TOOSHORT:"80041120",PP_E_SA_TOOLONG:"80041121",PP_E_INVALID_PHONENUMBER:"8004113F",PP_E_SECRETQ_CONTAINS_SECRETA:"80041165",PP_E_SECRETA_CONTAINS_SECRETQ:"8004117D",PP_E_SA_CONTAINS_MEMBERNAME:"8004116A",PP_E_STRONGPROCESS_ALTEMAILSAMEASMAILBOX:"80049C2D",PP_E_EMAIL_RIGHT_TOO_LONG:"8004110C",PP_E_NAME_TOO_LONG:"80041102",PP_E_ALIAS_AUTH_NOTPERMITTED:"8004788B",PP_E_TOTP_INVALID:"80049C34",PP_E_OLD_SKYPE_PASSWORD:"80043557",PP_E_OTT_DATA_INVALID:"8004348F",PP_E_OTT_ALREADY_CONSUMED:"80043490",PP_E_OTT_INVALID_PURPOSE:"80043496",PP_E_PPSA_RPT_NOTOADDRESS:"80048120",PP_E_STRONGPROCESS_BADDEVICENAME:"80049C22",PP_E_INLINELOGIN_INVALID_SMS:"800434E1",PP_E_INLINELOGIN_INVALID_ALT:"800434E2",PP_E_PREVIOUS_PASSWORD:"80041013",PP_E_HIP_VALIDATION_WRONG:"80045505",PP_E_HIP_VALIDATION_ERROR_FATAL:"80045537",PP_E_HIP_VALIDATION_ERROR_UNAUTHENTICATED:"********",PP_E_HIP_VALIDATION_ERROR_OTHER:"********",PP_E_SQ_CONTAINS_PASSWORD:"8004341E",PP_E_SA_CONTAINS_PASSWORD:"8004341C",PP_E_SA_CONTAINED_IN_PASSWORD:"8004341D",PP_E_LIBPHONENUMBERINTEROP_NUMBERPARSE_EXCEPTION:"********",PP_E_STRONGPROCESS_EMAIL_HAS_MOBILE_DOMAIN:"80049C33",PP_E_STRONGPROCESS_MXALIAS_NOTALLOWED:"80049C23",PP_E_INVALID_MEMBERNAME:"********",PP_E_SA_TOO_MANY_CACHE_SESSIONS:"8004A00C",PP_E_INTERFACE_DISABLED:"********",PP_E_ASSOCIATE_DUPLICATE_ACCOUNT:"********",PP_E_OAUTH_REMOTE_CONNECT_USER_CODE_MISSING_OR_INVALID:"800478C7",PP_E_LOGIN_NOPA_USER_PASSWORD_REQUIRED:"800478CE",PP_E_IDP_LINKEDIN_BINDING_NOT_ALLOWED:"800478D5",PP_E_IDP_GOOGLE_BINDING_NOT_ALLOWED:"800478D6",PP_E_IDP_GITHUB_BINDING_NOT_ALLOWED:"800478D7",PP_E_IDP_BINDING_EXISTS_SAMSUNG:"8004453E",PP_E_TRANSFER_TOKEN_INVALID_SESSION:"800435A0"},n.EstsError={UserAccountSelectionInvalid:"16001",UserUnauthorized:"50020",UserUnauthorizedApiVersionNotSupported:"500201",UserUnauthorizedMsaGuestUsersNotSupported:"500202",UserAccountNotFound:"50034",UserAccountDeleted:"500341",UserAlreadyExists:"1003037",UserAccountNotFoundNotConfiguredForRemoteNgc:"500342",UserAccountNotFoundFailedToCreateRemoteSignIn:"500343",UserAccountNotFoundForFidoSignIn:"500344",IdsLocked:"50053",InvalidPasswordLastPasswordUsed:"50054",InvalidPasswordExpiredPassword:"50055",InvalidPasswordNullPassword:"50056",UserDisabled:"50057",GuestUserDisabled:"500571",FlowTokenExpired:"50089",InvalidUserNameOrPassword:"50126",InvalidDomainName:"50128",ProtectedKeyMisuse:"50141",MissingCustomSigningKey:"50146",IdpLoopDetected:"50174",InvalidOneTimePasscode:"50181",ExpiredOneTimePasscode:"50182",OneTimePasscodeCacheError:"50183",OneTimePasscodeCacheErrorNoMoreOTPGenerated:"501831",OneTimePasscodeEntryNotExist:"50184",OneTimePasscodeMessageDeliveryFailed:"50185",InvalidPassword:"50193",InvalidOneTimePasscodeOTPNotGiven:"501811",InvalidGrantDeviceNotFound:"700003",SsoArtifactExpiredDueToConditionalAccess:"70044",SsoArtifactExpiredDueToConditionalAccessReAuth:"70046",InvalidTenantName:"90002",InvalidTenantNameEmptyGuidIdentifier:"900021",InvalidTenantNameEmptyIdentifier:"900022",InvalidTenantNameFormat:"900023",PhoneSignInBlockedByUserCredentialPolicy:"130500",AccessPassBlockedByPolicy:"130502",InvalidAccessPass:"130503",AccessPassExpired:"130504",AccessPassAlreadyUsed:"130505",PublicIdentifierSasBeginCallRetriableError:"131001",PublicIdentifierAuthUserNotAllowedByPolicy:"131010",PublicIdentifierSasBeginCallNonRetriableError:"131002",PublicIdentifierSasEndCallRetriableError:"131003",PublicIdentifierSasEndCallNonRetriableError:"131004",DeviceIsDisabled:"135011",FidoBlockedByPolicy:"135016",PasskeyBlockedByPolicyOtherAuthAppPasskeyAvailable:"1350161",PasskeyBlockedByPolicyOtherPasskeyAvailable:"1350162",PasskeyAuthInterrupted:"1350201",BlockedAdalVersion:"220300",BlockedClientId:"220400",InvalidCredentialDueToMfaClassification:"54009",ProofupBlockedDueToMfaClassification:"54010",NoEmailAddressCollectedFromExternalOidcIDP:"901011",EmailAddressCollectedFromExternalOidcIDPNotVerified:"901012",EmailAddressCollectedFromExternalOidcIDPNotPublic:"901013",NoExternalIdentifierCollectedFromExternalOidcIDP:"901014",UserVoiceAuthFailedCallWentToVoicemail:"UserVoiceAuthFailedCallWentToVoicemail",UserVoiceAuthFailedInvalidPhoneInput:"UserVoiceAuthFailedInvalidPhoneInput",UserVoiceAuthFailedPhoneHungUp:"UserVoiceAuthFailedPhoneHungUp",UserVoiceAuthFailedInvalidPhoneNumber:"UserVoiceAuthFailedInvalidPhoneNumber",UserVoiceAuthFailedInvalidExtension:"UserVoiceAuthFailedInvalidExtension",InvalidFormat:"InvalidFormat",UserAuthFailedDuplicateRequest:"UserAuthFailedDuplicateRequest",UserVoiceAuthFailedPhoneUnreachable:"UserVoiceAuthFailedPhoneUnreachable",UserVoiceAuthFailedProviderCouldntSendCall:"UserVoiceAuthFailedProviderCouldntSendCall",User2WaySMSAuthFailedProviderCouldntSendSMS:"User2WaySMSAuthFailedProviderCouldntSendSMS",SMSAuthFailedProviderCouldntSendSMS:"SMSAuthFailedProviderCouldntSendSMS",User2WaySMSAuthFailedNoResponseTimeout:"User2WaySMSAuthFailedNoResponseTimeout",SMSAuthFailedNoResponseTimeout:"SMSAuthFailedNoResponseTimeout",SMSAuthFailedWrongCodeEntered:"SMSAuthFailedWrongCodeEntered",IncorrectOTP:"IncorrectOTP",OathCodeIncorrect:"OathCodeIncorrect",OathCodeDuplicate:"OathCodeDuplicate",OathCodeOld:"OathCodeOld",ProofDataNotFound:"ProofDataNotFound",OathCodeCorrectButDeviceNotAllowed:"OathCodeCorrectButDeviceNotAllowed",OathCodeFailedMaxAllowedRetryReached:"OathCodeFailedMaxAllowedRetryReached",InvalidSession:"InvalidSession",PhoneAppNoResponse:"PhoneAppNoResponse",User2WaySMSAuthFailedWrongCodeEntered:"User2WaySMSAuthFailedWrongCodeEntered",PhoneAppInvalidResult:"PhoneAppInvalidResult",PhoneAppDenied:"PhoneAppDenied",PhoneAppTokenChanged:"PhoneAppTokenChanged",SMSAuthFailedMaxAllowedCodeRetryReached:"SMSAuthFailedMaxAllowedCodeRetryReached",PhoneAppFraudReported:"PhoneAppFraudReported",FraudCodeEntered:"FraudCodeEntered",UserIsBlocked:"UserIsBlocked",PhoneAppEntropyIncorrect:"PhoneAppEntropyIncorrect",VoiceOTPAuthFailedWrongCodeEntered:"VoiceOTPAuthFailedWrongCodeEntered",VoiceOTPAuthFailedMaxAllowedCodeRetryReached:"VoiceOTPAuthFailedMaxAllowedCodeRetryReached",AccessPassBlockedByPolicyTfa:"AccessPassBlockedByPolicy",InvalidAccessPassTfa:"InvalidAccessPass",AccessPassExpiredTfa:"AccessPassExpired",AccessPassAlreadyUsedTfa:"AccessPassAlreadyUsed",AppLockRequiredButNotUsed:"AppLockRequiredButNotUsed",IncompatibleAppVersion:"IncompatibleAppVersion",FlowTokenExpiredTfa:"FlowTokenExpired",ApplicationUsedIsNotAnApprovedAppRequiredByConditionalAccess:"530021",BlockedByConditionalAccess:"53003",BlockedByConditionalAccessForRemoteDeviceFlow:"530033",BrokerAppNotInstalled:"50127",BrokerAppNotInstalledDeviceAuthenticationFailed:"501271",DeviceIsNotWorkplaceJoined:"50129",DeviceIsNotWorkplaceJoinedForMamApp:"501291",DeviceNotCompliant:"53000",DeviceNotCompliantBrowserNotSupported:"530001",DeviceNotCompliantDeviceCompliantRequired:"530002",DeviceNotCompliantDeviceManagementRequired:"530003",DeviceNotDomainJoined:"53001",DeviceNotDomainJoinedBrowserNotSupported:"530011",ProofUpBlockedDueToRisk:"53004",ProofUpBlockedDueToUserRisk:"53011",RemediateCompliantApp:"53009",RemediateDeviceStateManagedBrowserRequired:"530081",RemediateDeviceStateWorkplaceJoinRequired:"530082",AuthenticatorAppRegistrationRequiredInterrupt:"50203",AuthenticatorAppRegistrationEnforcementInterrupt:"502031",UserStrongAuthEnrollmentRequiredInterrupt:"50072",UserStrongAuthClientAuthNRequiredInterrupt:"50074",RequiredDeviceStateNotSupported:"9001011",AdminConsentRequired:"90094",AdminConsentRequiredRequestAccess:"90095",CertificateValidationBlockedByPolicy:"500186",IssuerHintsPropagationDelay:"2205016",TenantDoesNotSupportNativeCredentialRecovery:"500207",UserDoesNotSupportNativeCredentialRecovery:"500208",CredentialDoesNotSupportNativeRecovery:"500209",QrCodeKeyInvalidKey:"130100",InvalidRequestNonce:"140000",QrPinInvalid:"1301021",InvalidGrantQrPinChanged:"1301024"},n.Fido={MaxUserPromptLength:99,FinishStates:{Success:0,Cancel:1,Error:2,NotSupported:3},UnexpectedErrorCode:9999,EdgeErrorCodes:{SyntaxError:3,NotFoundError:8,NotSupportedError:9,InvalidAccessError:15,AbortError:20}},n.IfExistsResult={Unknown:-1,Exists:0,NotExist:1,Throttled:2,Error:4,ExistsInOtherMicrosoftIDP:5,ExistsBothIDPs:6},n.ThrottleStatus={NotThrottled:0,AadThrottled:1,MsaThrottled:2},n.DomainType={Unknown:1,Consumer:2,Managed:3,Federated:4,CloudFederated:5},n.CredentialType={None:0,Password:1,RemoteNGC:2,OneTimeCode:3,Federation:4,CloudFederation:5,OtherMicrosoftIdpFederation:6,Fido:7,GitHub:8,PublicIdentifierCode:9,LinkedIn:10,RemoteLogin:11,Google:12,AccessPass:13,Facebook:14,Certificate:15,OfflineAccount:16,VerifiableCredential:17,QrCodePin:18,NoPreferredCredential:1e3},n.RemoteNgcType={PushNotification:1,ListSessions:3},n.SessionPollingType={Image:1,Json:2},n.AgreementType={Privacy:"privacy",Tou:"tou",Impressum:"impressum",A11yConforme:"a11yConforme"},n.ApiErrorCodes={GeneralError:6e3,AuthFailure:6001,InvalidArgs:6002,Generic:8e3,Timeout:8001,Aborted:8002},n.DefaultRequestTimeout=3e4,PROOF={Type:{Email:1,AltEmail:2,SMS:3,DeviceId:4,CSS:5,SQSA:6,Certificate:7,HIP:8,Birthday:9,TOTPAuthenticator:10,RecoveryCode:11,StrongTicket:13,TOTPAuthenticatorV2:14,TwoWayVoice:15,TwoWaySMS:16,FidoKey:17,AccessPass:18,TransferToken:19,CompanionApp:21,ExternalAuth:22,ConsolidatedTelephony:23,Voice:-3}},n.ContentType={Json:"application/json; charset=utf-8",FormUrlEncoded:"application/x-www-form-urlencoded"},n.BindProvider={LinkedIn:0,GitHub:1,Google:2,Samsung:3,Facebook:4},n.PromotedAltCredFlags={None:0,GitHub:1,LinkedIn:2},n.EnvironmentName={Internal:1,TestSlice:2,FirstSlice:3},n.AnimationState={Begin:0,End:-1,RenderNewView:1,AnimateNewView:2},n.AnimationName={None:0,SlideOutNext:1,SlideInNext:2,SlideOutBack:3,SlideInBack:4},n.DialogId={None:0,FidoHelp:1,GitHubHelp:2,ConsentAppInfo:3,QrCodePinHelp:4},n.KeyCode={Tab:9,Enter:13,Escape:27,Space:32,PageUp:33,PageDown:34,End:35,Home:36,ArrowUp:38,ArrowDown:40,WinKeyLeft:91,F6:117,GamePadB:196},n.ProofOfPossession={AuthenticatorKey:"cpa",CanaryTokenKey:"canary",MethodHint:"cpa_method_hint"},n.UpgradeMigrationUXId={Invalid:0,Mojang:1},n.TransferLoginStringsVariant={Default:0,Mmx:1,MmxPhoneFirst:2,AppNameOnly:3,AppNameAndUsername:4,MmxGe:5,OutlookMobileCustom:6,TeamsMobileCustom:7},n.LayoutTemplateType={Lightbox:0,VerticalSplit:1},n.StringCustomizationPageId={ConditionalAccess:0,AttributeCollection:1,MessagePage:2,ProofUpPage:3,ErrorPage:4,LoginPage:5},n.ProofUpRedirectViewType={DefaultProofUpRedirectView:0,AuthAppProofUpRedirectView:1},n.ConfirmationInputDisplayType={None:0,Retype:1,RetypeWithReveal:2},n.SecurityDefaultsUpsellAction={None:0,Upsell:1,AutoEnable:2,AutoEnableAfterPrompt:3,ReevaluateLegacy:4,AutoEnabledNotify:5},n.Branding={DefaultBackgroundColor:"#FAF9F8"},n.CredentialDeviceType={SingleDevice:"singleDevice",MultiDevice:"multiDevice"},n.AttestationParseError={Unknown:1,InvalidAuthDataSize:2,SingleDeviceBackedUp:3,CBORDataEmpty:4},n.ExternalFederatedIdpType={Google:50,Facebook:51,Apple:200},n.CameraMode={Environment:"environment",User:"user",Back:"back"},n.SignInIdentifierTypes={UPN:0,Email:1,Username:2,CustomUsername:3}},function(e,n,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=t(31),o=t(3),r=t(5),s=t(9),c=o.Object,d=o.String,l=o.Array,u=window,p=null,f={},g={},m={},b=null,v=null,h=null,_=null,C=null,S=null,x=null,w=null,y=!!u.ServerData.fUseSameSite,k=null,P=!!u.ServerData.fUseHighContrastDetectionMode;n.HttpCode={Ok:200,NotModified:304,Timeout:408,ClientClosedRequest:499};var T=n.Helper={isIEOlderThan:function(e){if(f[e]===undefined){var n=T.getIEVersion();f[e]=n&&n<e+1}return f[e]},isEdge:function(){if(null===p){p=!1;var e=T.getWindowsVersion();if(null!==e&&e>=10){var n=T.getIEVersion();p=null!==n&&n>=12}}return p},isChrome:function(){return null===b&&(b=navigator.userAgent.toLowerCase().indexOf("chrome")>-1),b},isFirefoxNewerThan:function(e){if(g[e]===undefined){var n=T.getFirefoxVersion();g[e]=n&&n>e}return g[e]},isChromeNewerThan:function(e){if(m[e]===undefined){var n=T.getChromeVersion();m[e]=n&&n>e}return m[e]},isIOSSafari:function(){if(null===v){var e=u.navigator.userAgent.toLowerCase();v=/safari/.test(e)&&/iphone|ipod|ipad/.test(e)&&!u.MSStream}return v},isIOSUIWebView:function(){if(null===h){var e=u.navigator.userAgent.toLowerCase();h=!1===/safari/.test(e)&&/iphone|ipod|ipad/.test(e)&&!u.MSStream}return h},isQtCarBrowser:function(){return null===_&&(_=navigator.userAgent.toLowerCase().indexOf("qtcarbrowser")>-1),_},isEdgeClientBrowser:function(){return null===C&&(C=navigator.userAgent.toLowerCase().indexOf("edgeclient/")>-1),C},isOnTouchStartEventSupported:function(){return"ontouchstart"in document.documentElement},getIEVersion:function(){var e=u.navigator.userAgent,n=e.indexOf("MSIE ");if(n>0)return parseInt(e.substring(n+5,e.indexOf(".",n)),10);if(e.indexOf("Trident/")>0){var t=e.indexOf("rv:");return parseInt(e.substring(t+3,e.indexOf(".",t)),10)}var i=e.indexOf("Edge/");return i>0?parseInt(e.substring(i+5,e.indexOf(".",i)),10):null},getFirefoxVersion:function(){var e=u.navigator.userAgent.match(/(firefox(?=\/))\/?\s*(\d+)/i);return e&&3===e.length&&"firefox"===e[1].toLowerCase()?parseInt(e[2]):null},getChromeVersion:function(){var e=u.navigator.userAgent.match(/(chrome(?=\/))\/?\s*(\d+)/i);return e&&3===e.length&&"chrome"===e[1].toLowerCase()?parseInt(e[2]):null},getWindowsVersion:function(){return null!==new RegExp("Windows NT ([0-9]{1,}[.0-9]{0,})").exec(navigator.userAgent)?parseFloat(RegExp.$1):null},htmlEscape:function(e){if(!e)return"";var n=document.createElement("textarea");return n.innerText=e,n.innerHTML},htmlUnescape:function(e){if(!e)return"";if(e.match(/<[^<>]+>/))return e;var n=document.createElement("textarea");return n.innerHTML=e,n.value},getStackSize:function(e){var n=0,t=null==e;try{!function i(){n++,(t||n<=e)&&i()}()}catch(i){}return n},getAnimationEndEventName:function(){var e=document.createElement("div"),n={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(var t in n)if(e.style[t]!==undefined)return n[t];return""},isStackSizeGreaterThan:function(e){return e=e||0,T.getStackSize(e)>e},isSvgImgSupported:function(){return null===k&&(k=document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Image","1.1")),k},isPlaceholderAttributeAllowed:function(e){return null===x&&(x=T.isChromeNewerThan(16)||T.isEdge()||T.isFirefoxNewerThan(14)||e&&T.isIOSUIWebView()||T.isIOSSafari()||T.isQtCarBrowser()),x},isCSSAnimationSupported:function(){var e=!1,n=document.createElement("div");(e=n.style.animationName!==undefined)||(e=!!l.first(["Webkit","Moz","O"],(function(e){return n.style[e+"AnimationName"]!==undefined})));return e},isStyleSupported:function(e){return e in document.documentElement.style},isCORSSupported:function(){return u.XDomainRequest||u.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest},isHistorySupported:function(){if(null===w){if(w=u.history&&u.history.pushState&&"undefined"!=typeof u.history.state&&"undefined"!=typeof u.onpopstate)try{u.history.replaceState("__history_test",""),("__history_test"!==u.history.state||T.isEdgeClientBrowser())&&(w=!1)}catch(e){w=!1}}return w},isFidoSupportedAsync:function(e,n){if(!n){if(!(u.navigator.credentials!==undefined&&u.navigator.credentials.create!==undefined&&u.navigator.credentials.get!==undefined&&u.PublicKeyCredential!==undefined&&u.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable!==undefined))return r.resolve(!1);if(u.PublicKeyCredential.isExternalCTAP2SecurityKeySupported)return s.newPromiseWithTimeout(u.PublicKeyCredential.isExternalCTAP2SecurityKeySupported,a.PromiseTimeout,!1)}return r.resolve(e)},isChangingInputTypeSupported:function(){return!T.isIEOlderThan(9)},getComputedSpan:function(){var e=document.createElement("span");e.style.borderLeftColor="red",e.style.borderRightColor="blue",e.style.backgroundColor="Window",e.style.position="absolute",e.style.top="-999px",document.body.appendChild(e);var n=T.getComputedStyle(e),t=n.borderLeftColor,i=n.borderRightColor,a=n.backgroundColor;return document.body.removeChild(e),{borderLeftColor:t,borderRightColor:i,backgroundColor:a}},isHighContrast:function(){if(null===S){var e=T.getComputedSpan();S=e.borderLeftColor===e.borderRightColor,P&&!S&&(S=T.getIsHighContrastUsingCssMediaQuery().isHighContrast)}return S},getIsHighContrastUsingCssMediaQuery:function(){var e=document.getElementsByTagName("head")[0],n=document.createElement("style");n.innerHTML='@media (-ms-high-contrast: active) {  .high-contrast-detection::before {    content: "active";    display: none;  }}@media (-ms-high-contrast: black-on-white) {  .high-contrast-detection::before {    content: "white";    display: none;  }}@media (-ms-high-contrast: white-on-black) {  .high-contrast-detection::before {    content: "black";    display: none;  }}',e.appendChild(n);var t=document.createElement("div");t.className="high-contrast-detection",document.body.appendChild(t);var i=window.getComputedStyle(t,"::before").content,a="";return'"black"'===i?a="black":'"white"'===i&&(a="white"),document.body.removeChild(t),e.removeChild(n),{isHighContrast:-1!==['"active"','"black"','"white"'].indexOf(i),theme:a}},getHighContrastTheme:function(){function e(e,n,t){function i(e,n,t,i){return new RegExp("^rgba?\\("+n+",\\s?"+t+",\\s?"+i+"(,\\s?\\d+\\.?\\d*)?\\)$","i").test(e.trim())}for(var a=0;a<n.length;a++){var o=n[a].split(",").map(Number),r=o[0],s=o[1],c=o[2];if(i(e,r,s,c))return!0}return!!l.first(t,(function(n){return n===e.trim()}))}if(T.isHighContrast()){var n=T.getComputedSpan();if(n.backgroundColor){var t=n.backgroundColor.toLowerCase().replace(new RegExp(" ","g"),""),i=e(t,["0,0,0"],["#000000","#000"]),a=e(t,["255,255,255"],["#ffffff","#fff"]),o=e(t,["32,32,32"],["#202020"]),r=e(t,["45,50,54"],["#2d3236"]),s=e(t,["255,250,239"],["#fffaef"]);if(i||o||r)return"black";if(a||s)return"white";if(P)return T.getIsHighContrastUsingCssMediaQuery().theme}}},getComputedStyle:function(e){return document.defaultView&&document.defaultView.getComputedStyle?document.defaultView.getComputedStyle(e,null):e.currentStyle?e.currentStyle:{}},history:{pushState:function(e,n){T.isHistorySupported()&&u.history.pushState(e,n)},replaceState:function(e,n){T.isHistorySupported()&&u.history.replaceState(e,n)}},addEventListener:function(e,n,t,i){e.addEventListener?e.addEventListener(n,t,i):e.attachEvent&&e.attachEvent("on"+n,t)},removeEventListener:function(e,n,t,i){e.removeEventListener?e.removeEventListener(n,t,i):e.detachEvent&&e.detachEvent("on"+n,t)},getEventTarget:function(e){return e?e.target?e.target:e.srcElement?e.srcElement:null:null}},D=n.QueryString={parse:function(e){var n=e,t=null,i=null;if(e){var a=e.indexOf("?"),o=e.indexOf("#");-1!==o&&(-1===a||o<a)?(n=e.substring(0,o),i=d.doubleSplit(e.substring(o+1),"&","=")):-1!==a&&-1===o?(n=e.substring(0,a),t=d.doubleSplit(e.substring(a+1),"&","=")):-1!==a&&-1!==o&&(n=e.substring(0,a),t=d.doubleSplit(e.substring(a+1,o),"&","="),i=d.doubleSplit(e.substring(o+1),"&","="))}return{originAndPath:n,query:t,fragment:i}},join:function(e){var n=e.originAndPath||"";return e.query&&(n+="?"+c.join(e.query,"&","=")),e.fragment&&(n+="#"+c.join(e.fragment,"&","=")),n},appendCurrentQueryParameterIfNotExist:function(e){var n=D.parse(window.location.href);return c.forEach(n.query,(function(n,t){e=D.addIfNotExist(e,n,t)})),e},append:function(e,n){var t=D.parse(e),i=d.doubleSplit(n,"&","=");return t.query=t.query||{},c.forEach(i,(function(e,n){t.query[e]=n||null})),D.join(t)},addIfNotExist:function(e,n,t){t=t||"";var i=D.parse(e);return null===c.findOwnProperty(i.query||{},n,!0)&&(i.query=i.query||{},i.query[n.toLowerCase()]=t),D.join(i)},add:function(e,n){var t=D.parse(e);return e&&n&&n.length&&(t.query=t.query||{},l.forEach(n,(function(e){t.query[e[0]]=e[1]}))),D.join(t)},addFragment:function(e,n){var t="";if(e&&n&&n.length){(t=D.parse(e)).fragment=t.fragment||{};var i=[];l.forEach(n,(function(e){i.includes(e[0])||(t.fragment[e[0]]=e[1],i.push(e[0]))}))}return t},appendOrReplace:function(e,n,t,i){var a=D.parse(e);a.query=a.query||{};var o=c.findOwnProperty(a.query,n,!0);o&&delete a.query[o],a.query[n.toLowerCase()]=t;var r=D.join(a);return i&&r.length>i?e:r},remove:function(e,n){var t=D.parse(e);t.query=t.query||{};var i=c.findOwnProperty(t.query,n,!0);return i&&delete t.query[i],D.join(t)},extract:function(e,n){n||""===n||(n=document.location.search);var t=D.parse(n);t.query=t.query||{};var i=c.findOwnProperty(t.query,e,!0);return i?t.query[i]:""},appendOrReplaceFromCurrentUrl:function(e,n){var t=D.extract(n);return t?D.appendOrReplace(e,n,t):e},stripQueryStringAndFragment:function(e){return D.parse(e).originAndPath}},E=n.Cookies={expireDate:"Thu, 30-Oct-1980 16:00:00 GMT",persistTTLDays:390,cookieSafeRegex:/^[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]+$/,enabled:function(){var e="G"+(new Date).getTime();E.write("CkTst",e);var n=!!E.getCookie("CkTst");return E.remove("CkTst"),n},getCookies:function(){return d.doubleSplit(document.cookie,";","=",!1,d.trim)},getCookie:function(e){var n=E.getCookies();return n[e]?n[e]:null},getObject:function(e){var n=E.getCookie(e)||"";return d.doubleSplit(n,"&","=")},remove:function(e,n,t){var i=n||document.location.hostname,a=i.split("."),o=a.length,r=a[o-2]+"."+a[o-1],s=t||"/",c="https:"===document.location.protocol,l=c?";secure":"",u=E.getDefaultSameSiteAttribute(c);document.cookie=d.format("{0}= ;domain=.{1};path={2};expires={3}{4}{5}",e,r,s,E.expireDate,l,u),document.cookie=d.format("{0}= ;domain=.{1};path={2};expires={3}{4}{5}",e,i,s,E.expireDate,l,u)},write:function(e,n,t,i,a,o,r,s,c){var d=o?".":"",l=document.domain.split(".");a&&l.splice(0,Math.max(0,l.length-2));var u=d+l.join(".");E.writeWithExpiration(e,n,t,i?E.getPersistDate():null,u,r,s,c)},writeWithExpiration:function(e,n,t,a,o,r,s,l){if(""===n)E.remove(e,o);else{"object"===i(n)&&(n=c.join(n,"&","="));var u,p=a?";expires="+a:"",f=o?";domain="+o:"",g=r||"/",m=t?";secure":"";u=s&&"none"!==s.toLowerCase()?";SameSite="+s:E.getDefaultSameSiteAttribute(t);var b=d.format("{0};path={1}{2}{3}{4}",f,g,p,m,u);if(l){for(var v=4e3-b.length-e.length-1,h=Math.ceil(n.length/v),_=E.getCookies(),C=0;C<h;C++){var S=0===C?"":C.toString(),x=n.substring(C*v,(C+1)*v),w=d.format("{0}{1}={2}{3}",e,S,x,b);document.cookie=w}for(;;C++){var y=e+C.toString();if(!_[y])break;E.remove(y,o,r)}}else{var k=d.format("{0}={1}{2}",e,n,b);document.cookie=k}}},isCookieSafeValue:function(e){return E.cookieSafeRegex.test(e)},getDefaultSameSiteAttribute:function(e){return e&&y?";SameSite=None":""},getPersistDate:function(){var e=new Date;return e.setDate(e.getDate()+E.persistTTLDays),e.toUTCString()}}},function(e,n,t){var i=window,a=i.document.documentMode,o=i.navigator;!function(){var n=null,r=o.userAgent,s=null,c=r.match(/MSIE ([^ ]+)/);if(c&&(n=parseInt(c[1])),"function"==typeof i.Symbol&&r.match(/AppleWebKit\/601/)&&(i.Symbol=null),n&&a&&n!==a&&Object.defineProperty)try{Object.defineProperty(o,"userAgent",{get:function(){return s}}),s=r.replace(/MSIE [^ ]+/,"MSIE "+a+".0"),e.exports=t(23),s=r}catch(d){e.exports=t(23)}else e.exports=t(23)}()},function(e,n,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=t(2),o=t(10),r=t(71),s=window,c=n.Object={assignRecursive:function(){return c.assignRecursiveWithCurrentDepth.apply(this,[1].concat(Array.prototype.slice.call(arguments)))},assignRecursiveWithCurrentDepth:function(e,n){if("number"==typeof e){for(var t=2,a=arguments.length;t<a;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&""!==o[r]&&("object"===i(o[r])&&e<=3?(n[r]=n[r]||{},c.assignRecursiveWithCurrentDepth(++e,n[r],o[r])):n[r]=o[r])}return n}},clone:function(e){var n={};return e&&(n=o.parse(o.stringify(e))),n},join:function(e,n,t){var i="";return e&&c.forEach(e,(function(e,a){i&&(i+=n),i+=e+t+(a||"")})),i},forEach:function(e,n){a.utils.objectForEach(e,n)},findOwnProperty:function(e,n,t){var i;for(var a in t&&(i=n.toLowerCase()),e)if(e.hasOwnProperty(a)&&(a===n||t&&a.toLowerCase()===i))return a;return null},extend:a.utils.extend},d=n.String={trim:function(e){return e.replace(/^\s+|\s+$/g,"")},find:function(e,n,t,i){return e?t?e.toLowerCase().indexOf(n.toLowerCase(),i):e.indexOf(n,i):-1},format:r.format,doubleSplit:function(e,n,t,i,a){var o={};return e&&l.forEach(e.split(n),(function(e){if(e){var n=e.split(t),r=n[0];a&&(r=a(r)),1===n.length?o[r]=null:o[r]=i?n.slice(1):n.slice(1).join(t)}})),o},isEmailAddress:function(e){if((e=d.trim(e)).charAt(0)>"~"||-1!==e.indexOf(" "))return!1;var n=e.indexOf("@");if(-1===n||-1===e.indexOf(".",n))return!1;var t=e.split("@");if(t.length>2||t[0].length<1||t[1].length<2)return!1;if(s.ServerData.fApplyAsciiRegexOnInput){var i=new RegExp(/^[\x21-\x7E]+$/);return!!e.match(i)}return!0},isPhoneNumber:function(e){var n=e.replace(/\D+/g,"");return n.length>=4&&n.length<=50},isSkypeName:function(e){e=d.trim(e);var n=new RegExp(/^[a-zA-Z][a-zA-Z0-9.,\-_:']{0,128}$/);return!!e.match(n)},extractDomain:function(e,n,t){if(!d.isEmailAddress(e))return e;var i=d.trim(e).split("@")[1];return e=t?"@":"",n?e+i.slice(0,i.lastIndexOf(".")+1):e+i},extractDomainFromUrl:function(e){if(e){var n=document.createElement("a");return n.href=e,n.hostname}return""},extractOriginFromUrl:function(e){if(e){var n=document.createElement("a");n.href=e;var t=n.origin;return t||(t=n.protocol+"//"+n.hostname+(n.port?":"+n.port:"")),t}return""},doOriginsMatch:function(e,n){var t=d.extractOriginFromUrl(e);return d.extractOriginFromUrl(n)===t},capFirst:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},cleanseUsername:function(e,n){if(!e)return"";if(e=d.trim(e).toLowerCase(),!d.isEmailAddress(e)&&!d.isSkypeName(e)&&d.isPhoneNumber(e)){var t="";return n&&"+"===e.charAt(0)&&(t="+"),t+e.replace(/\D+/g,"")}return e},maskString:function(e,n){if(!e)return"";if(e.length<=2*n)return e;var t=e.length-2*n,i=Array(t+1).join("*");return e.substring(0,n)+i+e.substring(n+t)},utf8Encode:function(e){e=e.replace(/\r\n/g,"\n");for(var n="",t=0;t<e.length;t++){var i=e.charCodeAt(t);i<128?n+=String.fromCharCode(i):i>127&&i<2048?(n+=String.fromCharCode(i>>6|192),n+=String.fromCharCode(63&i|128)):(n+=String.fromCharCode(i>>12|224),n+=String.fromCharCode(i>>6&63|128),n+=String.fromCharCode(63&i|128))}return n}},l=n.Array={first:a.utils.arrayFirst,forEach:a.utils.arrayForEach,map:a.utils.arrayMap,removeItem:a.utils.arrayRemoveItem,arrayFilter:a.utils.arrayFilter,findIndex:function(e,n){if(e&&"object"===i(e)&&e.length)for(var t=0;t<e.length;t++)if(n(e[t]))return t;return-1}};n.DateTime={getCurrentTime:function(){return(new Date).getTime()},getUTCString:function(){return Date.prototype.toISOString?(new Date).toISOString():(new Date).toUTCString()}},n.ErrorData=function(e,n){var t=this;t.errorText=e,t.remediationText=n,t.toString=function(){return t.errorText}}},function(e,n,t){var i=t(2);n.create=function(e){var n,t=!1;function a(){return t=!0,a.eventArgs(Array.prototype.slice.call(arguments)),n}return a.eventArgs=i.observable().extend({notify:"always"}),a.tracingOptions=e,a.subscribe=function(e){a.eventArgs.subscribe((function(t){n=e(t)})),t&&(n=e(a.eventArgs.peek()))},a},n.isComponentEvent=function(e){return e&&i.isObservable(e.eventArgs)}},function(e,n,t){var i=window;e.exports=i.Promise},function(e,n,t){var i=t(2),a=t(1),o=window,r=a.Helper;function s(e,n,t){var i=this,a=e.hasDarkBackground;i.isHighContrastBlackTheme=!1,i.isHighContrastWhiteTheme=!1,i.hasDarkBackground=a,i.lightImageNode=n,i.darkImageNode=t,function(){if(r.isHighContrast()){var e=r.getHighContrastTheme();i.isHighContrastBlackTheme="black"===e,i.isHighContrastWhiteTheme="white"===e}}()}i.components.register("accessible-image-control",{viewModel:{createViewModel:function(e,n){var t=i.utils.arrayFilter(n.templateNodes,(function(e){return 1===e.nodeType}));return new s(e,t[0],t[1])}},template:t(362),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents)}),e.exports=s},function(e,n){n.Tokens={Username:"#~#MemberName_LS#~#"},n.Fed={DomainToken:"#~#partnerdomain#~#",FedDomain:"#~#FederatedDomainName_LS#~#",Partner:"#~#FederatedPartnerName_LS#~#"},n.LoginOption={DoNotRemember:0,RememberPWD:1,NothingChecked:3},n.StringsVariantId={Default:0,SkypeMoveAlias:1,CombinedSigninSignup:2,CombinedSigninSignupDefaultTitle:3,RemoteConnectLogin:4,CombinedSigninSignupV2:5,CombinedSigninSignupV2WelcomeTitle:6},n.AllowedIdentitiesType={MsaOnly:0,AadOnly:1,Both:2},n.SessionIdp={Aad:0,Msa:1},n.ClientTracingEventIds={Event_LoginPaginatedUsernameView_onLoad:11e4,Event_LoginPaginatedPasswordView_onLoad:110001,ComponentEvent_LoginPaginatedUsernameView_onShowDialog:12e4,ComponentEvent_LoginPaginatedUsernameView_onAgreementClick:120001,ComponentEvent_LoginPaginatedPasswordView_onResetPassword:120100,PropertyValue_LoginPaginatedPageView_IsFidoSupported:14e4,PropertyValue_LoginPaginatedUsernameView_Username:140100,PropertyValue_LoginPaginatedUsernameView_ClientError:140101,PropertyValue_LoginPaginatedPasswordView_Password:140200,PropertyValue_LoginPaginatedPasswordView_ClientError:140201,PropertyValue_LoginPaginatedPasswordView_KMSI:140202}},function(e,n){n.EventIds={Unknown:0,Event_PaginationControl_ViewSwitch:1e4,Api_GetOneTimeCode:2e4,Api_GetOneTimeToken:20001,Api_CanaryValidation:20002,Api_GetCustomCss:20003,Api_GetCredentialType:20004,Api_CheckSessionState:20005,Api_GetIwaSsoToken:20006,Api_OtcAuthentication:20007,Api_DeviceAuthentication:20008,Api_BeginOtcAuthentication:20009,Api_ConfirmOneTimeCode:20010,Api_BeginSessionApproval:20011,Api_EndSessionApproval:20012,Api_Forget:20013,Api_GetRecoveryCredentialType:20014,Redirect_Unknown:4e4,Redirect_MSASignUpPage:40001,Redirect_AADSignUpPage:40002,Redirect_SkipZeroTouch:40003,Redirect_ResetPasswordPage:40004,Redirect_MSAUserRecoveryPage:40005,Redirect_OtherIdpRedirection:40006,Redriect_SwitchUser:40007},n.EventLevel={None:0,Critical:1,Info:2,ApiRequest:4,CXH:8,Debug:16,Verbose:32,All:65535},n.HidingMode={None:0,Hide:1,Mask:2},n.DataPointScope={ClientEvent:1,Global:2},n.EventStage={None:0,Begin:1,End:2}},function(e,n,t){var i=t(5);n.throwUnhandledExceptionOnRejection=function(e){e["catch"]((function(e){var n=e;e instanceof Error||(n=new Error("Unhandled Promise rejection: "+e)),setTimeout((function(){throw n}),0)}))},n.newPromiseWithTimeout=function(e,n,t){return new i((function(a,o){i.resolve(e()).then(a,o),setTimeout((function(){a(t)}),n)}))}},function(module,exports){var JSON;JSON||(JSON={}),function(){"use strict";var global=Function("return this")(),JSON=global.JSON;function f(e){return e<10?"0"+e:e}JSON||(JSON={}),"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(e){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(e){return this.valueOf()});var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,(function(e){var n=meta[e];return"string"==typeof n?n:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'}function str(e,n){var t,i,a,o,r,s=gap,c=n[e];switch(c&&"object"==typeof c&&"function"==typeof c.toJSON&&(c=c.toJSON(e)),"function"==typeof rep&&(c=rep.call(n,e,c)),typeof c){case"string":return quote(c);case"number":return isFinite(c)?String(c):"null";case"boolean":case"null":return String(c);case"object":if(!c)return"null";if(gap+=indent,r=[],"[object Array]"===Object.prototype.toString.apply(c)){for(o=c.length,t=0;t<o;t+=1)r[t]=str(t,c)||"null";return a=0===r.length?"[]":gap?"[\n"+gap+r.join(",\n"+gap)+"\n"+s+"]":"["+r.join(",")+"]",gap=s,a}if(rep&&"object"==typeof rep)for(o=rep.length,t=0;t<o;t+=1)"string"==typeof rep[t]&&(a=str(i=rep[t],c))&&r.push(quote(i)+(gap?": ":":")+a);else for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(a=str(i,c))&&r.push(quote(i)+(gap?": ":":")+a);return a=0===r.length?"{}":gap?"{\n"+gap+r.join(",\n"+gap)+"\n"+s+"}":"{"+r.join(",")+"}",gap=s,a}}"function"!=typeof JSON.stringify&&(JSON.stringify=function(e,n,t){var i;if(gap="",indent="","number"==typeof t)for(i=0;i<t;i+=1)indent+=" ";else"string"==typeof t&&(indent=t);if(rep=n,n&&"function"!=typeof n&&("object"!=typeof n||"number"!=typeof n.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(e,n){var t,i,a=e[n];if(a&&"object"==typeof a)for(t in a)Object.prototype.hasOwnProperty.call(a,t)&&((i=walk(a,t))!==undefined?a[t]=i:delete a[t]);return reviver.call(e,n,a)}if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,(function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")}),global.JSON=JSON,module.exports=JSON}()},function(e,n,t){var i=t(8),a=t(28),o=t(3),r=t(5);function s(e){var n,s=this,c=o.DateTime.getCurrentTime(),d=e&&e.fEnableClientTelemetry&&e.iClientLogLevel,l=null;function u(e){return function(){if(l)return l[e].apply(l,arguments)}}s.createLoadClientTracingPromise=function(){return new r((function(n){d&&!l?t.e(3).then(function(){var i=t(504).getInstance(e,c);l||(l=i),n()}.bind(null,t))["catch"](t.oe):n()}))},s.logRedirection=function(e,n){var t=e,i=null;return e&&"string"!=typeof e?(t=e.url,i=e.eventOptions,n=e.traceParameters?n:null,e.traceUrl&&(n?n.url=t:n=t)):n=null,i&&i.eventId&&s.logEvent({eventType:"onRedirect",eventId:i.eventId,eventLevel:i.eventLevel,eventArgs:n,eventOptions:i}),t},s.getPropertyLogOption=function(e,n){return(n=n||{}).hasOwnProperty("tracingPropertyChange")||(n.tracingPropertyChange=!0),n.eventLevel=n.eventLevel||i.EventLevel.Info,{viewModel:e,tracingOptions:n}},s.getDefaultTextBoxPropertyLogOption=function(e,n){return(n=n||{}).hasOwnProperty("hidingMode")||(n.hidingMode=i.HidingMode.None),n.rateLimit={method:"notifyWhenChangesStop"},s.getPropertyLogOption(e,n)},s.getPIITextBoxPropertyLogOption=function(e,n){return(n=n||{}).hidingMode=i.HidingMode.Mask,s.getDefaultTextBoxPropertyLogOption(e,n)},s.getPasswordTextBoxPropertyLogOption=function(e,n){return(n=n||{}).hidingMode=i.HidingMode.Hide,s.getDefaultTextBoxPropertyLogOption(e,n)},s.getDefaultEventTracingOptions=function(e,n,t){return{eventId:e,eventLevel:t||i.EventLevel.Info,hidingMode:n?i.HidingMode.None:i.HidingMode.Hide}},s.attachViewLoadClientTracingOptions=(n="attachViewLoadClientTracingOptions",function(){if(a)return a[n].apply(a,arguments)}),s.logEvent=u("logEvent"),s.logUserInteractionEvent=u("logUserInteractionEvent"),s.traceBeginRequest=u("traceBeginRequest"),s.traceEndRequest=function(e,n,t,i,a){l?l.traceEndRequest(e,n,t,i,a):a&&a()},s.setPageViewModel=u("setPageViewModel"),s.logComponentEvent=u("logComponentEvent"),s.logViewState=u("logViewState"),s.setViewViewModel=u("setViewViewModel"),s.switchView=u("switchView"),s.postEvent=u("postEvent")}var c=null;n.getInstance=function(e){return c=c||new s(e)}},function(e,n,t){var i=t(10),a=t(26),o=t(0),r=t(3),s=t(1),c=t(11).getInstance(window.ServerData),d=t(8),l=window,u=l.$Config||l.ServerData||{},p=r.Object,f=s.QueryString;e.exports=function(e){var n=this,t=!1!==(e=e||{}).checkApiCanary,r=e.withCredentials||!1,s=e.breakCache||!1,g=e.responseType||"",m=e.notifyOnClientAbort||!1,b=l.ServerData.fSasEndAuthPostToGetSwitch,v=l.ServerData.fFixUICrashForApiRequestHandler;function h(e){var n={hpgid:u.hpgid||0,hpgact:u.hpgact||0};return e||(n.Accept="application/json",t&&u.apiCanary&&(n.canary=u.apiCanary)),u.correlationId&&(n["client-request-id"]=u.correlationId),u.sessionId&&(n.hpgrequestid=u.sessionId),n}function _(e){var n=e;if(e&&"string"!=typeof e){var t={};p.forEach(e,(function(e,n){"unsafe_"===e.substr(0,7)&&(e=e.substr(7)),t[e]=n})),n=i.stringify(t)}return n&&(n=n.replace(/\?/g,"\\u003F")),n}function C(e){e.headers=h(),e.withCredentials=r,e.breakCache=s,e.responseType=g}function S(e,n,t,i,a,o){var r=null;if(n){var s=n.eventOptions||{};if(s.eventId=n.eventId||s.eventId,s.hasOwnProperty("hidingMode")||(s.hidingMode=d.HidingMode.None),s.eventId){(r={}).eventType=t,r.eventId=s.eventId,r.eventLevel=s.eventLevel||d.EventLevel.ApiRequest;var l={};l.requestTimeout=i,a&&(l.contentType=a),l.requestType=t,o&&(l.noCallback=!0),r.eventArgs=l,r.eventOptions=s,c.traceBeginRequest(e,r)}}e.eventData=r}function x(e,n,t,i,a){c.traceEndRequest(e,n,t,i,a)}function w(e,n){var t={};return e&&(t.xhr_status=e.status),t.textStatus=n,t}n.Errors=[],n.Json=function(e,a,r,s,c,d,l){var p=!(!r&&!s),f=(new Date).getTime(),g=e.url;function h(e,n){var t={};if(500===e.status)try{t=i.parse(e.responseText)||{}}catch(s){}if(!t.error){var a=!1,o=8e3,r="Request Failed -- No Response from Server";switch(n){case"timeout":o=8001,r="Timeout Error",a=!0;break;case"abort":o=8002,r="Aborted";break;case"error":e.status>=400&&(a=!0);break;case"parsererror":r="Unable to parse response",a=!0}t.error={code:o,message:r,debugMessage:"(xhr status "+e.status+") xhr.responseText: "+e.responseText,stackTrace:"",isFatal:a}}return t}function C(e){var t,a=(e=e||{}).error||null,o={startTime:f,endTime:(new Date).getTime()};if(e.apiCanary&&(u.apiCanary=e.apiCanary,delete e.apiCanary),a){t=(t=a.stackTrace)&&t.encodeJson?t.encodeJson():"";var c=i.stringify({code:a.code,message:a.message,debug:a.debugMessage,stacktrace:t,requestUrl:g});n.Errors.push(c),n.Errors.length>100&&n.Errors.shift(),(8002!==a.code||m)&&s&&s(e,o)}else r&&r(e,o)}function S(e){return setTimeout((function(){C({error:{code:e,message:"Request Failed!",isFatal:!0}})}),0),null}if(t&&!u.apiCanary)return v?S(8002):(setTimeout((function(){C({error:{code:8002,message:"Request Failed!",isFatal:!0}})}),0),null);if(b&&null===a&&null!==d)n.Get(e,o.ContentType.Json,(function(e,n){if(p)if(v)try{C(i.parse(n))}catch(t){S(8e3)}else C(i.parse(n))}),(function(e,n,t,i){p&&C(h(n,t))}),c,d,l);else{var x=_(a);n.Post(e,o.ContentType.Json,x,(function(e,n){if(p)if(v)try{C(i.parse(n))}catch(t){S(8e3)}else C(i.parse(n))}),(function(e,n,t,i){p&&C(h(n,t))}),c)}},n.Post=function(e,t,i,o,r,s){var c=e.url,d={},l=!1;o||r||(l=!0),S(d,e,a.RequestType.Post,s,t,l);var u={targetUrl:c,contentType:t,data:i,requestType:a.RequestType.Post,timeout:s||3e4,successCallback:function(e,n){x(d,"Success",n,!0,(function(){o&&o(e,n)}))},failureCallback:function(e,n,t){x(d,"Failed",w(n,t),!1,(function(){r&&r(e,n,t)}))},timeoutCallback:function(e,n,t){x(d,"Timeout",w(n,t),!1,(function(){r&&r(e,n,t)}))}};C(u),a.Handler.call(n,u),n.sendRequest()},n.Get=function(e,t,i,o,r,s,c){var d=e.url,l={},u=!1;i||o||(u=!0),S(l,e,a.RequestType.Get,r,t,u);var p={targetUrl:d,contentType:t,requestType:a.RequestType.Get,timeout:r||3e4,successCallback:function(e,n){x(l,"Success",n,!0,(function(){i&&i(e,n)}))},failureCallback:function(e,n,t){x(l,"Failed",w(n,t),!1,(function(){o&&o(e,n,t)}))},timeoutCallback:function(e,n,t){x(l,"Timeout",w(n,t),!1,(function(){o&&o(e,n,t)}))}};if(C(p),b){if(s)for(var g in s)s.hasOwnProperty(g)&&(p.headers[g]=s[g]);if(c){var m=f.add(d,c);p.targetUrl=m}}a.Handler.call(n,p),n.sendRequest()},n.Beacon=function(e,t,i,a,o){var r=[],s=h(!0);p.forEach(s,(function(e,n){r.push([e,n])}));var c=e.url;if(c=f.add(c,r),e.url=c,navigator.sendBeacon){var d={};S(d,e,"Beacon",o,null,!1);var l=_(t),u=navigator.sendBeacon(c,l);x(d,u?"Success":"Failed",null,u,(function(){u&&i?i():!u&&a&&a()}))}else n.Json(e,t,i,a,o)}}},function(e,n,t){var i=t(2),a=t(1),o=t(4);var r=window;function s(e){var n,t,r=this,s=(e=e||{}).serverData,c=e.primaryButtonId,d=s.fConsentButtonIdViaName,l=!(!d||!e.needsIdNameProtection);if(d){var u=c||"idSIButton9";n=l?undefined:u,t=l?u:undefined}else n=c,t=undefined;var p,f,g=e.secondaryButtonId,m=e.primaryButtonText,b=e.secondaryButtonText,v=!1!==e.isPrimaryButtonVisible,h=!1!==e.isSecondaryButtonVisible,_=!1!==e.isPrimaryButtonEnabled,C=!1!==e.isSecondaryButtonEnabled;d?(p=!l&&(e.focusOnPrimaryButton||!1),f=e.focusOnSecondaryButton||!!l&&e.focusOnPrimaryButton):(p=e.focusOnPrimaryButton||!1,f=e.focusOnSecondaryButton||!1);var S=e.primaryButtonDescribedBy,x=e.secondaryButtonDescribedBy,w=e.primaryButtonCss,y=e.secondaryButtonCss,k=e.primaryButtonType||"submit",P=e.removeBottomMargin,T=e.primaryButtonPreventTabbing||{direction:"none"};r.primaryButtonId=n,r.secondaryButtonId=g,r.primaryButtonCss=w,r.secondaryButtonCss=y,r.primaryButtonText=i.observable(m),r.secondaryButtonText=i.observable(b),r.isPrimaryButtonVisible=i.observable(v),r.isSecondaryButtonVisible=i.observable(h),r.isPrimaryButtonEnabled=i.observable(_),r.isSecondaryButtonEnabled=i.observable(C),r.focusOnPrimaryButton=i.observable(p),r.focusOnSecondaryButton=i.observable(f),r.hasOneButtonVisible=i.pureComputed((function(){var e=0;return r.isPrimaryButtonVisible()&&e++,r.isSecondaryButtonVisible()&&e++,1===e})),r.primaryButtonDescribedBy=S,r.secondaryButtonDescribedBy=x,r.removeBottomMargin=P,r.primaryButtonPreventTabbing=T,r.primaryButtonAttributes=i.pureComputed((function(){var e;return e=d?{id:n,name:t,"aria-describedby":S}:{id:r.primaryButtonId||"idSIButton9","aria-describedby":r.primaryButtonDescribedBy},a.Helper.isChangingInputTypeSupported()&&(e.type=k),e})),r.onPrimaryButtonClick=o.create(),r.onSecondaryButtonClick=o.create(),r.setTextPrimaryButton=function(e){r.primaryButtonText(e)},r.setTextSecondaryButton=function(e){r.secondaryButtonText(e)},r.setVisibilityPrimaryButton=function(e){r.isPrimaryButtonVisible(e)},r.setVisibilitySecondaryButton=function(e){r.isSecondaryButtonVisible(e)},r.setEnabledPrimaryButton=function(e){r.isPrimaryButtonEnabled(e)},r.setEnabledSecondaryButton=function(e){r.isSecondaryButtonEnabled(e)},r.primaryButton_onClick=function(){r.onPrimaryButtonClick()},r.secondaryButton_onClick=function(){r.onSecondaryButtonClick()}}i.components.register("footer-buttons-field",{viewModel:s,template:t(369),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){var i=t(2),a=t(3),o=t(1),r=t(0),s=t(9),c=t(75),d=t(85),l=t(244),u=window,p=o.Helper,f=a.String,g=a.Object,m=r.LayoutTemplateType,b=r.Branding,v=u.ServerData.fUseNonMicrosoftDefaultBrandingForCiam,h=u.ServerData.fIsCiamUserFlowUx,_=u.ServerData.fRemoveCustomCss,C=u.ServerData.fEnableLivePreview,S={loadTenantBranding:function(e){var n={};if(e){var t=e[0]||{},a=e[1]||{};i.utils.arrayForEach(["BoilerPlateText","UserIdLabel","TileLogo","TileDarkLogo","BannerLogo","BackgroundColor","Illustration","KeepMeSignedInDisabled","UseTransparentLightBox","LayoutTemplateConfig","CustomizationFiles","AccessRecoveryLink","CantAccessYourAccountText","ForgotPasswordText","FooterTOULink","FooterTOUText","FooterPrivacyLink","FooterPrivacyText","Favicon"],(function(e){if("LayoutTemplateConfig"===e||"CustomizationFiles"===e){var i=a[e],o=t[e];n[e]=g.assignRecursive({},o,i)}else n[e]=a[e]||t[e]||""})),n.TileDarkLogo||(n.TileDarkLogo=n.TileLogo)}return n},getPageBranding:function(e,n,t){var i={useDefaultBackground:!1};if(e&&(i.bannerLogoUrl=e.BannerLogo),e&&(e.BackgroundColor||e.Illustration))i.color=e.BackgroundColor,i.backgroundImageUrl=e.Illustration,i.useTransparentLightBox=e.UseTransparentLightBox,i.useImageMask=!v||!h;else if(n&&(n.backgroundImageIndex>=0||n.backgroundLogoIndex>=0||n.backgroundColor||n.friendlyAppName))n.backgroundImageIndex>=0&&(i.backgroundImageUrl=d(f.format("./{0}.jpg",n.backgroundImageIndex)),p.isStyleSupported("backgroundSize")&&(i.smallImageUrl=d(f.format("./{0}-small.jpg",n.backgroundImageIndex)))),n.backgroundLogoIndex>=0&&(i.backgroundLogoUrl=l(f.format("./{0}.png",n.backgroundLogoIndex))),i.color=n.backgroundColor,i.friendlyAppName=n.friendlyAppName;else if(n&&n.urlLegacyBackgroundLogo)i.backgroundLogoUrl=n.urlLegacyBackgroundLogo;else if(v&&h)i.color=b.DefaultBackgroundColor;else if(t>=0){var a=p.isSvgImgSupported();i.backgroundImageUrl=c(f.format("./{0}.{1}",t,a?"svg":"jpg")),!a&&p.isStyleSupported("backgroundSize")&&(i.smallImageUrl=c(f.format("./{0}-small.jpg",t))),i.useDefaultBackground=!0}return i},getMergedBranding:function(e,n,t,i){var a;if(t)a=n;else{a=e,n=n||{},i?(n.BannerLogo&&(a.BannerLogo=n.BannerLogo),n.BoilerPlateText&&(a.BoilerPlateText=n.BoilerPlateText),n.KeepMeSignedInDisabled&&(a.KeepMeSignedInDisabled=n.KeepMeSignedInDisabled),n.AccessRecoveryLink&&(a.AccessRecoveryLink=n.AccessRecoveryLink),n.CantAccessYourAccountText&&(a.CantAccessYourAccountText=n.CantAccessYourAccountText),n.ForgotPasswordText&&(a.ForgotPasswordText=n.ForgotPasswordText)):(a.BannerLogo=n.BannerLogo||"",a.BoilerPlateText=n.BoilerPlateText||"",a.KeepMeSignedInDisabled=n.KeepMeSignedInDisabled||!1,a.AccessRecoveryLink=n.AccessRecoveryLink||"",a.CantAccessYourAccountText=n.CantAccessYourAccountText||"",a.ForgotPasswordText=n.ForgotPasswordText||"");var o=!1;if(n.LayoutTemplateConfig&&(o=n.LayoutTemplateConfig.hideAccountResetCredentials),a.LayoutTemplateConfig=a.LayoutTemplateConfig||S.getLayoutTemplateConfig({}),a.LayoutTemplateConfig.hideAccountResetCredentials=o,a.CustomizationFiles=a.CustomizationFiles||{},n.CustomizationFiles){var r={strings:n.CustomizationFiles.strings,customCssUrl:n.CustomizationFiles.customCssUrl};a.CustomizationFiles.customCssUrl!==r.customCssUrl&&(r.customCssUrl=null),a.CustomizationFiles=r}}return a},getLayoutTemplateConfig:function(e){var n=e.LayoutTemplateConfig;return n&&n!=={}||(n={showHeader:!1,headerLogo:"",layoutType:m.Lightbox,showFooter:!0,hideTOU:!1,hidePrivacy:!1,hideAccountResetCredentials:!1},v&&h&&(n.showFooter=!1)),n},createMergedBrandingObservables:function(e){e.masterPageMethods=i.observable(),e.isVerticalSplitTemplate=i.observable(),e.showHeader=i.observable(!1),e.headerLogo=i.observable(),e.showFooter=i.observable(!0),e.hideTOU=i.observable(!1),e.hidePrivacy=i.observable(!1),e.termsText=i.observable(),e.termsLink=i.observable(),e.privacyText=i.observable(),e.privacyLink=i.observable(),e.userIdLabel=i.observable(),e.cantAccessYourAccountText=i.observable(),e.forgotPasswordText=i.observable(),e.accessRecoveryLink=i.observable(),e.boilerPlateText=i.observable(),e.hideAccountResetCredentials=i.observable(!1)},updateMergedBrandingObservables:function(e,n){if(n){var t=S.getLayoutTemplateConfig(n);e.masterPageMethods()&&e.masterPageMethods().updateBranding(n),e.isVerticalSplitTemplate(t.layoutType===m.VerticalSplit),e.showHeader(t.showHeader),e.headerLogo(t.headerLogo),e.showFooter(t.showFooter),e.hideTOU(t.hideTOU),e.hidePrivacy(t.hidePrivacy),e.termsText(n.FooterTOUText),e.termsLink(n.FooterTOULink),e.privacyText(n.FooterPrivacyText),e.privacyLink(n.FooterPrivacyLink),C&&(e.hideAccountResetCredentials(t.hideAccountResetCredentials),e.userIdLabel(n.UserIdLabel),e.cantAccessYourAccountText(n.CantAccessYourAccountText),e.forgotPasswordText(n.ForgotPasswordText),e.accessRecoveryLink(n.AccessRecoveryLink),e.boilerPlateText(n.BoilerPlateText))}},updateFavicon:function(e,n){var t=document.querySelector("link[rel~='icon']");t&&(e&&e.Favicon?t.href=e.Favicon:n&&(t.href=n))},removeCustomCss:function(){var e=document.head.querySelector("#customCssStyle");e&&document.head.removeChild(e)},loadCustomizationFiles:function(e,n){if(!_||n&&e&&e.CustomizationFiles&&e.CustomizationFiles.customCssUrl||S.removeCustomCss(),n){if(!e||!e.CustomizationFiles)return n.isLoadComplete(!0),void n.strings.isLoadComplete(!0);var t=e.CustomizationFiles,i=t.customCssUrl,a=t.strings;if(a||i){n.initialize();var o={customStringsFiles:a,customCss:i};s.throwUnhandledExceptionOnRejection(n.load(o))}else n.isLoadComplete(!0),n.strings.isLoadComplete(!0)}},createCustomizationLoader:function(e,n,i,a){t.e(8).then(function(){var o=new(t(542))({serverData:e,pageId:a});i?(i(o),S.loadCustomizationFiles(n,i())):S.loadCustomizationFiles(n,o)}.bind(null,t))["catch"](t.oe)}};e.exports=S},function(e,n,t){t(357);var i=t(358);function a(e,n){return e&&e[n]?e[n]:""}function o(e){if(!e||!e.ver)return"";var n=e.ver.v||e.ver||"";return Array.isArray(n)&&n.length>0?n.join("."):n}var r=null;n.getInstance=function(e){if(e.fEnableOneDSClientTelemetry)try{r=r||function(e){var n=e||{},t=e.browser||{},r=n.clientEvents||{},s=n.serverDetails||{},c=n.correlationId?n.correlationId:"";return r.correlationID=c,r.hostPageID=n.hpgid,r.pageName=n.pgid||n.sPageId,r.actorID=n.hpgact||n.sCID,r.appId=n.appId,r.autoCaptureJsErrors=n.fAutoCaptureJsErrors||!1,r.autoCaptureClicks=n.fAutoCaptureClicks||!1,r.autoCaptureEvents=n.fAutoCaptureEvents||!1,r.environment=n.environment,r.serverDetails={datacenter:a(s,"dc"),role:a(s,"r"),roleInstance:a(s,"ri"),version:o(s)},t.IE&&r.appInsightsConfig&&(r.appInsightsConfig.PostChannel?r.appInsightsConfig.PostChannel.disableXhrSync=!0:r.appInsightsConfig.PostChannel={disableXhrSync:!0}),new i.TelemetryHelper(r)}(e)}catch(n){}return r}},function(e,n,t){var i=t(2),a=t(1),o=window,r=a.Helper;function s(){var e=this;e.useCssAnimation=!1,r.isCSSAnimationSupported()&&!r.isHighContrast()&&(e.useCssAnimation=!0)}i.components.register("marching-ants-control",{viewModel:s,template:t(377),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents)}),e.exports=s},function(e,n,t){var i=t(26),a=t(1),o=i.Helper,r=a.QueryString,s=n.Properties={State:"State",SessionLookupKey:"SessionLookupKey",DisplaySignForUI:"DisplaySignForUI",FlowToken:"FlowToken"},c=n.Purpose={Password:"eOTT_OneTimePassword",RemoteNGC:"eOTT_RemoteNGC",NoPassword:"eOTT_NoPasswordAccountLoginCode",OtcLogin:"eOTT_OtcLogin",XboxRemoteConnect:"RemoteSignInWithUserCode"},d=n.Channel={Authenticator:"Authenticator",MobileSms:"SMS",EmailAddress:"Email",VoiceCall:"Voice",PushNotifications:"PushNotifications",VerifiableCredentials:"VerifiableCredentials"},l=n.Type={EmailAddress:"AltEmail",EmailAddressEncrypted:"AltEmailE",Mobile:"MobileNum",MobileEncrypted:"MobileNumE",SessionApprover:"SAPId"};n.Event={OnSend:"otcsend",OnSendFail:"otcsendfailed",OnFlowExpired:"otcflowexpired"};var u=n.RequestParam={Username:"login",Purpose:"purpose",FlowToken:"flowtoken",CanaryFlowToken:"canaryFlowToken",Channel:"channel",UIMode:"UIMode",PhoneCountry:"MobileCountry",PhoneCountryCode:"MobileCC",UnauthSessionId:"uaid",ProofConfirmation:"ProofConfirmation"},p=n.Status={None:0,Error:200,Success:201,HIPError:202,FTError:203,InputError:204,DestinationError:205,Timeout:300},f=n.ProofTypeToChannel=function(e){var n=null;switch(e){case PROOF.Type.SMS:n=d.MobileSms;break;case PROOF.Type.Voice:n=d.VoiceCall;break;case PROOF.Type.Email:case PROOF.Type.AltEmail:n=d.EmailAddress;break;case PROOF.Type.TOTPAuthenticatorV2:n=d.PushNotifications}return n},g=n.ProofTypeToOtcType=function(e,n){var t=null;switch(e){case PROOF.Type.Voice:case PROOF.Type.SMS:t=n?l.MobileEncrypted:l.Mobile;break;case PROOF.Type.Email:case PROOF.Type.AltEmail:t=n?l.EmailAddressEncrypted:l.EmailAddress;break;case PROOF.Type.TOTPAuthenticatorV2:t=l.SessionApprover}return t};n.Proof=function(e){var n=e.username||"",t=e.flowToken||"",i=e.purpose||c.Password,a=e.proofType,o=e.proofData||"",r=e.isEncrypted,s=e.uiMode,d=e.lcid,l=e.phoneCountry||"",p=e.phoneCountryCode||"",m=e.unauthSessionId,b=e.proofConfirmation,v=e.canaryFlowToken;this[u.Username]=n,this[u.FlowToken]=t,this[u.Purpose]=i,this[u.Channel]=f(a),this[g(a,r)]=o,s&&(this[u.UIMode]=s),d&&(this.lcid=d),r||a!==PROOF.Type.SMS&&a!==PROOF.Type.Voice||(this[u.PhoneCountry]=l,this[u.PhoneCountryCode]=p),m&&(this[u.UnauthSessionId]=m),b&&(this[u.ProofConfirmation]=b),v&&(this[u.CanaryFlowToken]=v)},n.Request=function(e){var n,t,a,c,d=this,l=p.None,u="",f="",g="",m=e.data,b=e.onSend,v=e.onSendFail,h=e.onFlowExpired,_=e.timeout||3e4,C=e.siteId,S=e.clientId,x=e.forwardedClientId,w=e.noPaBubbleVersion;function y(e){var n=!1,t=d.getResponseJson();g=t[s.FlowToken]||"",t[s.State]?(l=t[s.State],u=t[s.SessionLookupKey]||"",f=t[s.DisplaySignForUI]||"",n=l!==p.Success):(l=p.Error,u="",f="",n=!0),n?l===p.FTError?h(e,d):v(e,d):b(e)}function k(){g="",l=p.Error,u="",f="",v(d)}function P(){l=p.Timeout,u="",f="",g="",v(d)}d.getOtcStatus=function(){return d.isComplete()?l:p.None},d.getSessionKey=function(){return d.isComplete()?u:""},d.getDisplaySign=function(){return d.isComplete()?f:""},d.getFlowToken=function(){return d.isComplete()?g:""},n=r.extract("mkt"),t=r.extract("lc"),a=[].concat(n?[["mkt",n]]:[],t?[["lcid",t]]:[],C?[["id",C]]:[],S?[["client_id",S]]:[],x?[["fci",x]]:[],w?[["nopa",w]]:[]),c={targetUrl:r.add("GetOneTimeCode.srf",a),requestType:i.RequestType.Post,data:o.generateRequestString(m),isAsync:!0,timeout:_,successCallback:y,failureCallback:k,timeoutCallback:P},i.Handler.call(d,c)}},function(e,n,t){var i=t(31),a=t(9),o=t(27),r=t(5),s=t(0),c=t(7),d=window,l=d.navigator,u=d.PublicKeyCredential,p=d.ServerData.fShouldPlatformKeyBeSuppressed,f=s.PostType,g=c.AllowedIdentitiesType;n.makeCredential=function(e,n,t,a,r,s,c,d){var u=[];s&&(u=s.map((function(e){return{type:"public-key",id:o.base64UrlStringToArrayBuffer(e)}})));var p=i.SupportedKeyAlgorithms.map((function(e){return{type:"public-key",alg:e}})),f={challenge:o.stringToArrayBuffer(e),rp:{name:"Microsoft",id:d},user:{id:o.base64UrlStringToArrayBuffer(n),name:t,displayName:a,icon:r},pubKeyCredParams:p,timeout:i.Timeout,excludeCredentials:u,authenticatorSelection:{authenticatorAttachment:c,requireResidentKey:!0,userVerification:"required"},attestation:"direct",extensions:{hmacCreateSecret:!0,credentialProtectionPolicy:"userVerificationOptional"}};return l.credentials.create({publicKey:f})},n.getAssertion=function(e,n,t,a,r){var s=[];n&&(s=n.map((function(e){return{type:"public-key",id:o.base64UrlStringToArrayBuffer(e)}})));var c={challenge:o.stringToArrayBuffer(e),timeout:i.Timeout,rpId:t,allowCredentials:s,userVerification:"required"},d=a?"conditional":undefined;return r?l.credentials.get({publicKey:c,mediation:d}):l.credentials.get({publicKey:c})},n.isPlatformAuthenticatorAvailable=function(e){return p||!u?r.resolve(null):e?r.resolve(!0):a.newPromiseWithTimeout(u.isUserVerifyingPlatformAuthenticatorAvailable,i.PromiseTimeout,!1)},n.isConditionalMediationAvailable=function(){return u!==undefined&&null!==u&&"function"==typeof u.isConditionalMediationAvailable?a.newPromiseWithTimeout(u.isConditionalMediationAvailable,i.PromiseTimeout,!1):r.resolve(!1)},n.getIdpFromUserHandle=function(e){var n=o.arrayBufferToString(e);return n.match(/^M.:/)?g.MsaOnly:n.match(/^O.:/)?g.AadOnly:void 0},n.postFidoAssertionToIdp=function(e,n,t,i,a,o,r,s,c,d,l){e(!0);var u={type:f.NGC,ps:f.NGC,assertion:n,hpgrequestid:i,ctx:a};t&&(u.lmcCanary=t),r&&(u[o]=r),u[s]=c,l(d,u,!1,!0)}},function(e,n,t){e.exports=t.p+"content/images/documentation_white_9ad8d18b22266935b952ba85a2fb252c.png"},function(e,n,t){e.exports=t.p+"content/images/documentation_white_7849019e114f05613d891a7b3805dda2.svg"},function(e,n,t){e.exports=t.p+"content/images/documentation_136bc3add990843012b1ec60612de803.png"},function(e,n,t){e.exports=t.p+"content/images/documentation_dae218aac2d25462ae286ceba8d80ce2.svg"},function(e,n,t){(function(n){e.exports=n.ko=t(69)}).call(this,t(41))},function(e,n,t){var i=t(17),a=t(8),o=t(11).getInstance(window.ServerData);e.exports=function(e){var n=null,t=e.username,r=e.proofData,s=e.proofType,c=e.purpose||i.Purpose.Password,d=e.flowToken,l=e.canaryFlowToken,u=!1!==e.isEncrypted,p=e.uiMode,f=e.lcid,g=e.unauthSessionId,m=e.proofConfirmation,b=e.phoneCountry,v=e.phoneCountryCode,h=e.siteId,_=e.clientId,C=e.forwardedClientId,S=e.noPaBubbleVersion,x=e.successCallback,w=e.failureCallback,y=e.clientTracingOptions,k={};function P(e,n,t,i,a){o.traceEndRequest(e,n,t,i,a)}function T(){P(k,"Success",null,!0,(function(){x&&x(n)}))}function D(){var e={otcStatus:n.getOtcStatus()};P(k,"Failed",e,!1,(function(){w&&w(n)}))}this.sendRequest=function(){var e,x={data:(e={username:t,proofData:r,proofType:s,purpose:c,flowToken:d,canaryFlowToken:l,isEncrypted:u,uiMode:p,lcid:f,unauthSessionId:g,proofConfirmation:m,phoneCountry:b,phoneCountryCode:v},new i.Proof(e)),siteId:h,clientId:_,forwardedClientId:C,noPaBubbleVersion:S,onSend:T,onSendFail:D,onFlowExpired:D},w={};w.proofType=s,w.purpose=c,w.uiMode=p,w.lcid=f,w.phoneCountry=b,w.phoneCountryCode=v,function(e,n){var t=y||{};t.hasOwnProperty("eventId")||(t.eventId=a.EventIds.Api_GetOneTimeToken);if(t.eventId){var i={eventType:"POST",eventId:t.eventId,eventLevel:t.eventLevel||a.EventLevel.Info,eventArgs:n,eventOptions:t};o.traceBeginRequest(e,i)}}(k,w),(n=new i.Request(x)).sendRequest()}}},function(e,n,t){var i=t(0),a=t(7),o=t(5),r=t(14),s=t(1),c=t(3),d=t(12),l=t(8),u=s.Helper,p=s.QueryString,f=c.String,g=c.Object,m=i.CredentialType,b=i.RemoteNgcType,v=i.Error,h=i.ApiErrorCodes,_=i.EstsError,C=i.PaginatedState,S=i.IfExistsResult,x=i.ThrottleStatus,w=i.DomainType,y=i.BindProvider,k=a.AllowedIdentitiesType,P=i.SessionPullFlags;var T=E.GctResultAction={ShowError:1,SwitchView:2,Redirect:3},D=E.GctRequestHelperFlags={CheckCurrentIdpOnly:1,IsPhoneNumberFullyQualified:2,DisableDesktopSsoPreferredCred:4,DisableAutoSend:8,ForceOtcLogin:16,IsPostRequest:32,IsSignup:64};function E(e,n){var a=this,s=!1,E=null,I=null,A=null,R=!1,L={},B=null,O=e,U=0!=(n&D.CheckCurrentIdpOnly),F=0!=(n&D.IsPhoneNumberFullyQualified),N=0!=(n&D.DisableDesktopSsoPreferredCred),M=0!=(n&D.DisableAutoSend),V=0!=(n&D.ForceOtcLogin),j=0!=(n&D.IsPostRequest),H=0!=(n&D.IsSignup),W=O.str,$=O.sUnauthSessionID,G=O.iAllowedIdentities,q=O.fIsFedDisabled,K=!!O.fIsRemoteNGCSupported,z=!!O.fShowCookieBanner,Q=!!O.fIsFidoSupported,J=O.fIsOtcLoginDisabled,X=O.fIsNoPaOtcDisabled,Y=!!O.fIsExternalFederationDisallowed,Z=!!O.fIsPassthroughDisallowed,ee=!!O.fIsPhoneNumberSignupDisallowed,ne=O.fIsVerifiableCredentialsSupportEnabled,te=O.sCtx,ie=O.fDoIfExists,ae=O.fCheckProofForAliases,oe=O.fCheckApiCanary,re=O.urlGetCredentialType,se=O.fCBShowSignUp,ce=O.fAllowSkypeNameLogin,de=e.urlMsaSignUp,le=O.urlSignUp,ue=O.fUseCertificateInterstitialView,pe=O.oSignUpPostParams,fe=O.fUseConsumerEmailError,ge=O.oUrlOtherIdpPostParams,me=O.desktopSsoConfig,be=O.sFedQS,ve=O.staticTenantBranding,he=O.dynamicTenantBranding,_e=O.isGlobalTenant,Ce=(O.fCheckForWindowsSku,O.country),Se=O.arrProofData||{},xe=parseInt(O.sProofType),we=O.urlChangePassword,ye=!!O.fAllowRemoteConnect,ke=O.fBindCookiesUsingPoP,Pe=O.iGctFederationFlags||0,Te=O.fIgnoreViralUsers,De=O.fAccessPassSupported,Ee=O.fHidePhoneCobasiInOtherSignIn,Ie=O.fIsRestrictedWsi,Ae=O.fUseResetPwdUrlForPwdRequiredErr,Re=O.urlResetPassword,Le=O.fIsSelfServiceSignupUxEnabled,Be=O.fIsUserFlowLinked,Oe=O.fEnableWebNativeBridge,Ue=O.sWAMExtension,Fe=O.sWAMChannel,Ne=O.canaryTokenName,Me=O.canary,Ve=O.fDeprecateSmsAutoSend,je=O.fUseWebviewFidoCustomProtocol,He=!!O.fIsQrCodePinSupported,We=O.fEnableDFPIntegration,$e=O.fIsImprovedUsernameRecovery,Ge=O.fFixPhoneDisambigSignupRedirect,qe=O.fEnableRefreshCookiesFix,Ke=O.fEnableFIDOBluetoothError,ze=O.fFixUrlExternalIdpFederation,Qe=!!O.fEnablePasskeyAwpError;function Je(e,n,t){var i=e.Credentials&&e.Credentials.OtcLoginEligibleProofs,a=[];if(i){var o=e.Credentials&&e.Credentials.HasPhone&&e.Credentials.CobasiApp;c.Array.forEach(i,(function(e){if(e.isDefault===n){var t={credType:m.OneTimeCode,proof:e};switch(t.proof.isEncrypted=!0,e.type){case PROOF.Type.SMS:case PROOF.Type.Voice:if(!e.isVoiceOnly){var i=g.clone(t);i.proof.otcSent&&j&&xe===PROOF.Type.Voice&&(i.proof.otcSent=!1),i.proof.type=PROOF.Type.SMS,Ee&&!o&&(i.shownOnlyOnPicker=!0),a.push(i)}if(e.voiceEnabled){var r=g.clone(t);!r.proof.otcSent||j&&xe===PROOF.Type.Voice||(r.proof.otcSent=!1),r.proof.type=PROOF.Type.Voice,a.push(r)}break;case PROOF.Type.Email:a.push(g.clone(t))}}}))}if(n&&0===a.length&&en(e,t)===m.OneTimeCode){var r=e.Credentials&&e.Credentials.HasPassword,s={credType:m.OneTimeCode,proof:{display:e.Display,data:f.cleanseUsername(e.Display),otcSent:!0,isEncrypted:!1,isDefault:!0,isNopa:!r,type:f.isEmailAddress(e.Username)?PROOF.Type.Email:PROOF.Type.SMS}};a.push(s)}return a}function Xe(e){e.proof.str={},g.extend(e.proof.str,Se[e.proof.type]||{}),g.forEach(e.proof.str,(function(n,t){t&&(e.proof.str[n]=f.format(t,e.proof.display+"\u200e",e.proof.clearDigits||""))}))}function Ye(e,n,t,i,o,r){var c={},d=me&&i.EstsProperties&&i.EstsProperties.DesktopSsoEnabled&&!function(e){return e&&me.lastUsernameTried&&e.toLowerCase()===me.lastUsernameTried.toLowerCase()}(n),l=i.ErrorHR,g=rn(n,i,o),h=a.getGctSharedData(i,o,r);if(h.username=n,l===v.PP_E_INVALID_PHONENUMBER||l===v.PP_E_LIBPHONENUMBERINTEROP_NUMBERPARSE_EXCEPTION)c=function(e){if(F)return un(W.CT_PWD_STR_Error_InvalidPhoneNumber,!0);return ln(C.PhoneDisambiguation,{phoneDisambigError:e})}(l);else if(l===v.PP_E_NAME_INVALID||l===v.PP_E_INVALIDARG)c=un(W.CT_PWD_STR_Error_InvalidUsername);else if(l===v.PP_E_FEDERATION_INLINELOGIN_DISALLOWED)c=un(W.CT_PWD_STR_Error_FedNotAllowed,!0);else if(l===v.PP_E_LOGIN_NOPA_USER_PASSWORD_REQUIRED)c=un(Ae?f.format(W.CT_STR_Error_PasswordRequired,p.stripQueryStringAndFragment(Re)):f.format(W.CT_STR_Error_PasswordRequired,p.stripQueryStringAndFragment(we)));else if(i.RequiresPhoneDisambiguation)c=ln(C.PhoneDisambiguation);else{if(i.AliasDisabledForLogin)return un(W.CT_PWD_STR_Error_AliasDisabled,!0);if(i.IfExistsResult===S.NotExist)c=function(e,n,t,i){var a,o=dn(e,t),r=t.EstsProperties||{},s=r.DomainType&&r.DomainType!==w.Unknown&&r.DomainType!==w.Consumer;if(t.IsProofForAlias)return $e?on(e,t,de):ln(C.ConfirmRecoverUsername);if(se&&o&&(ie||ae))return ie?on(e,t):ln(C.ConfirmSignup);a=Le||Be?W.CT_PWD_STR_SSSU_Error_EmailAccountNotFound:n?t.ThrottleStatus===x.NotThrottled&&s?W.CT_PWD_STR_Error_UsernameNotExist_Alternate_VerifiedDomain:W.CT_PWD_STR_Error_UsernameNotExist_Alternate:en(t,i)===m.OneTimeCode?t.ThrottleStatus===x.MsaThrottled?W.CT_PWD_STR_Error_UsernameNotExists_EmailOtpAllowed_MsaFailed:W.CT_PWD_STR_Error_UsernameNotExists_EmailOtpAllowed:fe&&r.DomainType===w.Consumer?W.CT_PWD_STR_Error_UsernameNotExist_ConsumerEmail:t.ThrottleStatus===x.NotThrottled&&s?o?W.CT_PWD_STR_Error_UsernameNotExist_VerifiedDomain_SignupAllowed:W.CT_PWD_STR_Error_UsernameNotExist_VerifiedDomain:t.ThrottleStatus===x.MsaThrottled?s&&o?W.CT_PWD_STR_Error_UsernameNotExist_VerifiedDomain_MsaFailed_SignupAllowed:s?W.CT_PWD_STR_Error_UsernameNotExist_VerifiedDomain_MsaFailed:se&&!le?W.CT_PWD_STR_Error_UsernameNotExist_Guest_SignupAllowed_MsaFailed:W.CT_PWD_STR_Error_UnknownDomain_MsaFailed:H&&!s?W.CT_PWD_STR_Error_UsernameNotExist_Guest_Signup:F?W.CT_PWD_STR_Error_InvalidPhoneNumber:W.CT_PWD_STR_Error_UsernameNotExist;return un(f.format(a,u.htmlEscape(f.extractDomain(e)),u.htmlEscape(e)),!0)}(n,t,i,o);else if(i.IfExistsResult===S.ExistsBothIDPs)c=ln(C.IdpDisambiguation,{desktopSsoEnabled:d,idpRedirectUrl:g.idpRedirectUrl,idpRedirectPostParams:g.idpRedirectPostParams,idpRedirectProvider:g.idpRedirectProvider});else if(i.IfExistsResult===S.ExistsInOtherMicrosoftIDP)c=an(e,n);else if(!s||i.IfExistsResult!==S.Error&&i.IfExistsResult!==S.Throttled&&0==(i.ThrottleStatus&x.MsaThrottled))if(i.ShowRemoteConnect)c=ln(C.RemoteLoginPolling);else{c=function(e,n,t,i,a,o){var r=en(i,o);if(Ie&&!(i.Credentials.HasPassword||i.Credentials.HasGoogleFed||i.Credentials.HasCertAuth||i.Credentials.HasFido||i.Credentials.HasRemoteNGC||i.Credentials.HasPhone||i.Credentials.HasFacebookFed)&&(B=!0,r!==m.AccessPass))return ln(C.MoreInfo);if(!N&&n)return ln(C.FetchSessionsProgress,{unsafe_desktopSsoDomainToUse:f.extractDomain(e),sessionPullType:P.Dsso});if(i.NativeBridgeRequest&&!R)return ln(C.WebNativeBridge,qe?{request:i.NativeBridgeRequest,flowToken:i.FlowToken,serverData:O,nonce:i.Nonce||null}:{request:i.NativeBridgeRequest,flowToken:i.FlowToken,serverData:O});var s={idpRedirectUrl:t.idpRedirectUrl,idpRedirectPostParams:t.idpRedirectPostParams,idpRedirectProvider:t.idpRedirectProvider};switch(r){case m.OneTimeCode:var c=C.OneTimeCode;return(M||i.Credentials.OTCNotAutoSent)&&(c=a?C.ProofConfirmation:C.ConfirmSend),ln(c);case m.PublicIdentifierCode:return Ve&&i.Credentials.OtcNotAutoSent?ln(C.ConfirmSend):M||i.Credentials.SasParams&&i.Credentials.SasParams.Success?ln(M?C.ConfirmSend:C.OneTimeCode):un(W.CT_PWD_STR_Error_GetCredentialTypeError,!1,!0);case m.Fido:return ln(C.Fido);case m.RemoteNGC:var d=i.Credentials.RemoteNgcParams.DefaultType===b.PushNotification;return ln(M&&d?C.ConfirmSend:C.RemoteNGC,s);case m.Federation:case m.CloudFederation:return ln(C.IdpRedirect,s);case m.LinkedIn:case m.GitHub:case m.Google:case m.Facebook:return ln(nn(i,o).length>1||tn(i,o).length>0?C.IdpRedirectSpeedbump:C.IdpRedirect,s);case m.AccessPass:return ln(C.AccessPass,s);case m.NoPreferredCredential:return ln(C.CredentialPicker,s);case m.OtherMicrosoftIdpFederation:return on(e,i,de);case m.Certificate:return ue?ln(C.CertificateInterstitialView):pn(i.Credentials.CertAuthParams.CertAuthUrl,sn(i.FlowToken));case m.QrCodePin:return ln(C.QrCodeScan);case m.Password:default:return ln(C.Password)}}(n,d,g,i,h.otcCredential&&h.otcCredential.proof.clearDigits,o)}else c=ln(C.IdpDisambiguation,{hasIdpDisambigError:!0,desktopSsoEnabled:d,idpRedirectUrl:g.idpRedirectUrl,idpRedirectPostParams:g.idpRedirectPostParams,idpRedirectProvider:g.idpRedirectProvider})}return c.flowToken=i.FlowToken||null,c.bypassCache||(L[n]=i,L[n].FlowToken=null),c.sharedData=h,c}function Ze(e){var n={};if(e&&e.error)switch(e.error.code){case h.AuthFailure:n=un(W.CT_PWD_STR_Error_FlowTokenExpired);break;case _.PublicIdentifierSasBeginCallNonRetriableError:case _.PublicIdentifierSasBeginCallRetriableError:n=un(W.CT_PWD_STR_Error_GetCredentialTypeError,!1,!0);break;default:n=un(W.CT_PWD_STR_Error_GetCredentialTypeError)}else n=un(W.CT_PWD_STR_Error_GetCredentialTypeError);return n.flowToken=e.FlowToken||null,n}function en(e,n){var t=m.Password,i=e.Credentials;return i&&((t=i.PrefCredential)!==m.Fido||n||(t=i.RemoteNgcParams&&i.RemoteNgcParams.SessionIdentifier?m.RemoteNGC:m.Password)),t}function nn(e,n){var t=e.Credentials||{},i=t.SasParams,a=t.LinkedInParams,o=t.GitHubParams,r=t.GoogleParams,s=t.FacebookParams,d=t.CertAuthParams,l=t.QrCodePinParams,u=e.EstsProperties||{},p=[].concat(t.HasPassword&&u.DomainType!==w.Federated?{credType:m.Password}:[],t.FederationRedirectUrl&&u.DomainType===w.Federated?{credType:m.Federation}:[],t.FederationRedirectUrl&&u.DomainType===w.CloudFederated?{credType:m.CloudFederation}:[],t.HasRemoteNGC?{credType:m.RemoteNGC}:[],t.HasFido&&n?{credType:m.Fido}:[],t.HasPhone&&i?{credType:m.PublicIdentifierCode}:[],t.HasLinkedInFed&&a?{credType:m.LinkedIn,redirectUrl:a.LinkedInRedirectUrl}:[],t.HasGitHubFed&&o?{credType:m.GitHub,redirectUrl:o.GithubRedirectUrl}:[],t.HasGoogleFed&&r?{credType:m.Google,redirectUrl:r.GoogleRedirectUrl}:[],t.HasFacebookFed&&s?{credType:m.Facebook,redirectUrl:s.FacebookRedirectUrl}:[],t.HasAccessPass?{credType:m.AccessPass}:[],t.HasVerifiableCredential&&ne?{credType:m.VerifiableCredential}:[],t.HasQrCodePin&&He&&l?{credType:m.QrCodePin}:[],t.HasCertAuth?{credType:m.Certificate,redirectUrl:d.CertAuthUrl,redirectPostParams:sn(e.FlowToken)}:[]);if(Se[PROOF.Type.Email]&&Se[PROOF.Type.SMS]&&Se[PROOF.Type.Voice]){var f=Je(e,!0,n);f.length>0&&(c.Array.forEach(f,Xe),p=p.concat(f))}return p}function tn(e,n){var t=[];if(Se[PROOF.Type.Email]&&Se[PROOF.Type.SMS]&&Se[PROOF.Type.Voice]){var i=Je(e,!1,n);i.length>0&&(c.Array.forEach(i,Xe),t=t.concat(i),c.Array.first(i,(function(e){return!e.proof.isNopa}))&&(t=t.concat({credType:m.Password,isDefault:!1})))}return t}function an(e,n){var t=encodeURIComponent(n).replace(new RegExp("'","g"),"%27");e=p.appendOrReplace(e,"username",t),e=p.appendOrReplace(e,"login_hint",t);var i=ge?g.clone(ge):null;return i&&(i.unsafe_username=n),pn(e,i,!0,l.EventIds.Redirect_OtherIdpRedirection)}function on(e,n,t){t=t||le,t=p.remove(t,"username"),t=p.remove(t,"login_hint");var i=pe?g.clone(pe):null;return n&&(n.IfExistsResult===S.NotExist||n.IsUnmanaged&&n.IfExistsResult===S.Exists)&&dn(e,n)&&(i?i.unsafe_username=e:(t=p.appendOrReplace(t,"username",encodeURIComponent(e)),t=p.appendOrReplace(t,"login_hint",encodeURIComponent(e)))),pn(t,i,!1,l.EventIds.Redirect_MSASignUpPage)}function rn(e,n,t){var i={},a=en(n,t),o=n.EstsProperties||{};if(!(n.Credentials&&(n.Credentials.FederationRedirectUrl||n.Credentials.LinkedInParams||n.Credentials.GitHubParams||n.Credentials.GoogleParams||n.Credentials.FacebookParams)))return i;switch(a){case m.RemoteNGC:case m.Federation:case m.AccessPass:case m.NoPreferredCredential:o&&o.SamlRequest&&o.RelayState?(i.idpRedirectUrl=n.Credentials.FederationRedirectUrl,i.idpRedirectPostParams={SAMLRequest:o.SamlRequest,RelayState:o.RelayState,unsafe_username:e}):ze&&n.Credentials.HasExternalOidcFed?i.idpRedirectUrl=n.Credentials.FederationRedirectUrl:i.idpRedirectUrl=function(e,n){if(be){var t=p.appendOrReplace("?"+be,"wctx","LoginOptions%3D3%26"+p.extract("wctx","?"+be));t=t.substr(1),e=p.append(e,t)}else e=p.appendOrReplace(e,"wctx","LoginOptions%3D3%26"+p.extract("wctx",e));return e=p.appendOrReplace(e,"cbcxt",encodeURIComponent(decodeURIComponent(p.extract("cbcxt")))),e=p.appendOrReplace(e,"username",encodeURIComponent(n)),e=p.appendOrReplace(e,"mkt",encodeURIComponent(decodeURIComponent(p.extract("mkt")))),e=p.appendOrReplace(e,"lc",encodeURIComponent(decodeURIComponent(p.extract("lc"))))}(n.Credentials.FederationRedirectUrl,e);break;case m.CloudFederation:i.idpRedirectUrl=n.Credentials.FederationRedirectUrl;break;case m.LinkedIn:i.idpRedirectUrl=n.Credentials.LinkedInParams.LinkedInRedirectUrl,i.idpRedirectProvider=y.LinkedIn;break;case m.GitHub:i.idpRedirectUrl=n.Credentials.GitHubParams.GithubRedirectUrl,i.idpRedirectProvider=y.GitHub;break;case m.Google:i.idpRedirectUrl=n.Credentials.GoogleParams.GoogleRedirectUrl,i.idpRedirectProvider=y.Google;break;case m.Facebook:i.idpRedirectUrl=n.Credentials.FacebookParams.FacebookRedirectUrl,i.idpRedirectProvider=y.Facebook}return i}function sn(e){var n={ctx:te,flowToken:e};return Ne&&(n[Ne]=Me),n}function cn(e){return f.cleanseUsername(e,!0)}function dn(e,n){var t=ce&&f.isSkypeName(e),i=n.EstsProperties||{};return i.DomainType&&i.DomainType!==w.Unknown&&i.DomainType!==w.Consumer?!1===n.IsSignupDisallowed:!n.IsSignupDisallowed&&!t}function ln(e,n){return{action:T.SwitchView,viewId:e,viewParams:n}}function un(e,n,t){return{action:T.ShowError,error:e,isBlockingError:n,bypassCache:t}}function pn(e,n,t,i){return{action:T.Redirect,redirectUrl:e,redirectPostParams:n,isIdpRedirect:t,eventId:i}}a.sendAsync=function(e,n,t){var a=cn(n),r=L[a]?L[a]:null,c=!!r,u=r?o.resolve(r):function(e,n){var t=[I].concat(A||[]);return o.all(t).then((function(t){var a=t[0],r=t[1];return new o((function(t,o){new d({checkApiCanary:oe}).Json({url:re,eventId:l.EventIds.Api_GetCredentialType},function(e,n,t,i){var a={unsafe_username:e,uaid:$,isOtherIdpSupported:s,isFederationDisabled:q,checkPhones:f.isPhoneNumber(e),isRemoteNGCSupported:K,isCookieBannerShown:z,isFidoSupported:t,originalRequest:te,country:Ce,forceotclogin:V,otclogindisallowed:J,isExternalFederationDisallowed:Y,isRemoteConnectSupported:ye,federationFlags:Pe,isSignup:H,flowToken:n};(ae||$e)&&(a.checkProofForAliases=!0);X&&(a.noPaOtcDisallowed=!0);Z&&(a.isPassthroughDisallowed=!0);ee&&(a.isPhoneNumberSignupDisallowed=!0);Te&&(a.ignoreViralUsers=!0);ke&&(i=i||{cpa:"",err:"Authenticator not generated."},a.cpa=i.cpa,a.cpa_err=i.err);De&&(a.isAccessPassSupported=!0);He&&(a.isQrCodePinSupported=!0);return a}(e,n,a,r),t,o,i.DefaultRequestTimeout)}))}))}(a,t);return o.all([E,u,I]).then((function(n){var t=n[0],i=n[1],o=n[2];return Ye(e,a,t,i,o,c)}),Ze)},a.getResult=function(e,n,t,i){return Ye(e,cn(n),!1,t,i,!1)},a.getState=function(){return{cache:L}},a.restoreState=function(e){e&&(L=e.cache||{})},a.cacheResponse=function(e,n){Ge&&f.isPhoneNumber(e)||(L[cn(e)]=n)},a.getSignupRedirectGctResult=function(e){var n=cn(e);return on(n,L[n]?L[n]:null)},a.getOtherIdpRedirectGctResult=function(e,n){return an(e,cn(n))},a.getGctSharedData=function(e,n,t){var i={},a=en(e,n),o=e.Credentials||{},s=o.RemoteNgcParams,d=o.FidoParams,l=o.QrCodePinParams,u=e.EstsProperties||{},p=s?s.DefaultType:null,f=!!s&&s.ShowAnimatedGifWhilePolling,g=!!s&&s.StyleCredSwitchLinkAsButton,b=r.loadTenantBranding(u.UserTenantBranding||he),v=r.loadTenantBranding(ve),h=rn(e.Username,e,n),_=e&&e.AcmaProperties&&e.AcmaProperties.RecoveryContinuationToken,C=e&&e.AcmaProperties&&e.AcmaProperties.UndirectedRecoveryUrl;return We&&e.DfpProperties&&e.DfpProperties.DfpUrl&&(i.urlDeviceFingerprinting=e.DfpProperties.DfpUrl),i.preferredCredential=a,i.location=e.Location,i.fedRedirectParams=h,i.isTapRestrictedWsi=B,i.supportsNativeCredentialRecovery=e.SupportsNativeCredentialRecovery,i.isSignup=e.IfExistsResult===S.NotExist&&Le,e.Display&&(i.displayName=e.Display),i.availableCreds=nn(e,n),i.evictedCreds=tn(e,n),a===m.OneTimeCode&&(i.otcCredential=c.Array.first(i.availableCreds,(function(e){return e.credType===m.OneTimeCode&&e.proof.otcSent})),!i.otcCredential&&e.Credentials&&e.Credentials.OTCNotAutoSent&&(i.otcCredential=c.Array.first(i.availableCreds,(function(e){return e.credType===m.OneTimeCode&&e.proof.type===PROOF.Type.SMS}))),i.useEvictedCredentials=!1),i.remoteNgcParams={requestSent:!M&&!t&&a===m.RemoteNGC&&s&&s.hasOwnProperty("Entropy"),sessionIdentifier:s?s.SessionIdentifier:null,entropy:s?s.Entropy:null,defaultType:p,showAnimatedGifWhilePolling:f,styleCredSwitchLinkAsButton:g},i.otcParams={requestSent:(a===m.OneTimeCode||a===m.PublicIdentifierCode)&&e.IfExistsResult!==S.ExistsBothIDPs},d&&d.AllowList&&(i.fidoParams=Qe&&Ke?{allowList:d.AllowList,hasMsftAuthAppPasskey:d.HasCrossDeviceCapablePasskey,hasMsftAndroidAuthAppPasskey:d.HasMsftAndroidAuthAppPasskey,hasOnlyMsftAndroidAuthAppPasskey:d.HasOnlyMsftAndroidAuthAppPasskey}:Ke?{allowList:d.AllowList,hasMsftAuthAppPasskey:d.HasCrossDeviceCapablePasskey}:{allowList:d.AllowList}),He&&l&&l.AllowList&&(i.qrCodePinParams={allowList:l.AllowList,nonce:l.Nonce}),i.callMetadata=u&&u.CallMetadata?u.CallMetadata:{},i.userTenantBranding=r.getMergedBranding(v,b,_e),i.undirectedRecoveryContinuationToken=_,i.undirectedRecoveryUrl=C,i},s=!U&&G===k.Both,E=o.resolve(!1),I=u.isFidoSupportedAsync(Q,je),ke&&t.e(28).then(function(){var e=t(543);A=e.computePoPAuthenticator("POST",re).then((function(e){return{cpa:e,err:null}}),(function(e){return{cpa:"",err:e.message}}))}.bind(null,t))["catch"](t.oe),Oe&&t.e(42).then(function(){t(500).getBrowserCoreProvider(Ue,Fe).then((function(){}),(function(){R=!0}))}.bind(null,t))["catch"](t.oe)}e.exports=E},function(e,n,t){var i=t(10),a=t(1),o=t(3),r=o.Object,s=a.HttpCode,c=window;n.RequestType={Post:"POST",Get:"GET"};var d=n.State={Unsupported:-1,Unsent:0,Done:4,Timeout:5};n.Event={OnSuccess:"ajaxsuccess",OnError:"ajaxerror",OnTimeout:"ajaxtimeout"},n.Helper={generateRequestString:function(e){var n="";return e&&r.forEach(e,(function(e,t){(t||""===t)&&(n.length>0&&(n+="&"),n+=e+"="+t)})),n}},n.Handler=function(e){var n=this,t="",l=[],u=null,p=null,f=!1,g=!0,m=null,b=!1,v=!!e.contentType,h=!!e.headers,_=!!e.headerValue,C=e.data||"",S=e.targetUrl||"",x=e.requestType||"",w=!1!==e.isAsync,y=e.timeout||0,k=e.username||"",P=e.password||"",T=e.contentType||"application/x-www-form-urlencoded",D=e.withCredentials||!1,E=e.breakCache||!1,I=e.responseType||"",A=e.headers||{},R=e.successCallback,L=e.failureCallback,B=e.timeoutCallback;function O(e,i){e||n.isSuccess()?R&&R(u,t):(i||!n.isSuccess()&&!f)&&L&&L(u,p,p.statusText)}function U(){if(m=null,f=!0,n.cancel(),B){var e={status:s.Timeout,statusText:"timeout"};B(u,e,e.statusText)}}function F(e){if(j(),!n.isComplete()&&!p.canceled&&L){var t={status:s.ClientClosedRequest,statusText:"abort"};L(e,t,t.statusText)}}function N(e){n.isComplete()||V(e)}function M(e){n.isComplete()&&!f&&V(e)}function V(e){j(),t=p.responseText,u=e,O()}function j(){m&&(clearTimeout(m),m=null)}function H(e){j(),t=p.responseText,O(e,!e)}n.sendRequest=function(e){u=e,function(){f=!1;var e="withCredentials"in new XMLHttpRequest;if(!b||e){var t=S;(p=new XMLHttpRequest).onreadystatechange=M,p.addEventListener&&(p.addEventListener("abort",F),p.addEventListener("error",N)),E&&(t=a.QueryString.appendOrReplace(t,"_",(new Date).getTime())),k.length>0?p.open(x,t,w,k,P):p.open(x,t,w),n.clearResponse(),r.forEach(A,(function(e,n){p.setRequestHeader(e,n)})),p.responseType=I,p.withCredentials=D}else c.XDomainRequest?!w||k||P||v||h||_||D?g=!1:((p=new c.XDomainRequest).onerror=function(){H(!1)},p.onload=function(){H(!0)},p.open(x,S),n.clearResponse()):g=!1}(),g&&(y>0&&(m=setTimeout((function(){U.call(n)}),y)),p.send(C))},n.getState=function(){return g?f?d.Timeout:p?p.readyState:d.Unsent:d.Unsupported},n.getStatus=function(){return f?s.Timeout:p?p.status:0},n.cancel=function(){p&&(p.canceled=!0,p.abort())},n.getResponseJson=function(){return t?i.parse(t):{}},n.isComplete=function(){return n.getState()===d.Done||n.getState()===d.Timeout},n.isSuccess=function(){return n.isComplete()&&l[n.getStatus()]},n.clearResponse=function(){t=""},function(){A["Content-type"]=T,l[s.Ok]=!0,l[s.NotModified]=!0,l[s.Timeout]=!1;var e=o.String.extractDomainFromUrl(S);e&&(b=o.String.extractDomainFromUrl(document.location.href)!==e)}()}},function(e,n){var t=n.stringToArrayBuffer=function(e){for(var n=new ArrayBuffer(e.length),t=new Uint8Array(n),i=0,a=e.length;i<a;++i)t[i]=e.charCodeAt(i);return n},i=n.arrayBufferToString=function(e){return String.fromCharCode.apply(null,new Uint8Array(e))};n.base64UrlStringToArrayBuffer=function(e){var n=e.replace(/[-_]/g,(function(e){switch(e){case"-":return"+";case"_":return"/"}})),i=atob(n);return t(i)},n.arrayBufferToBase64UrlString=function(e){var n=i(e),t=btoa(n);return a(t)},n.objectToBase64UrlString=function(e){if(e){var n=JSON.stringify(e),t=btoa(n);return a(t)}return null};var a=n.base64ToBase64UrlString=function(e){return e.replace(/[+/=]/g,(function(e){switch(e){case"+":return"-";case"/":return"_";case"=":return""}}))}},function(e,n,t){var i=t(3),a=t(8),o={},r=null;n.setDataPoint=function(e,n,t,i){var o={scope:i||a.DataPointScope.ClientEvent},r=s(e);r.tracingDataPoints=r.tracingDataPoints||{},r.tracingDataPoints[n]={options:o,value:function(){return t}}};var s=n.getTracingContextObject=function(e){return e?(o[e]||(o[e]={}),o[e]):r=r||{}};n.getTracingContextObjects=function(){var e=[];return i.Object.forEach(o,(function(n,t){t&&e.push({viewModel:n,context:t})})),r&&e.push(r),e},n.registerTracingObservables=function(e,n,t){var i=s(e);i.tracingObservables=i.tracingObservables||[],i.tracingObservables.push({options:t,value:n})},n.deleteTracingContextObject=function(e){e?o[e]&&delete o[e]:r=null},n.attachViewLoadClientTracingOptions=function(e,n){s(e).viewLoadClientTracingOptions=n}},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=window;function s(e,n){var t=this,i=e.dialogId,a=e.data,r=n;t.onRegisterDialog=o.create(),t.onUnregisterDialog=o.create(),t.dispose=function(){t.onUnregisterDialog(i)},t.onRegisterDialog(i,{templateNodes:r,data:a})}i.components.register("dialog-content-control",{viewModel:{createViewModel:function(e,n){return new s(e,n.templateNodes)}},template:"\x3c!-- --\x3e",synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=t(0),s=window,c=r.AgreementType,d=a.Helper,l=r.PaginatedState;function u(e){var n=this,t=e.serverData,a=e.showLogo,r=e.showLinks,u=e.hideFooter,p=e.debugDetails,f=e.showDebugDetails,g=e.hasDarkBackground,m=e.useDefaultBackground,b=e.showFooter,v=e.hideTOU,h=e.hidePrivacy,_=e.termsText,C=e.privacyText,S=e.termsLink,x=e.privacyLink,w=t.str,y=t.fIsHosted,k=t.fIsChinaDc,P=t.urlFooterTOU||t.urlHostedTOULink,T=t.urlFooterPrivacy||t.urlHostedPrivacyLink,D=t.urlImpressum,E=t.a11yConformeLink,I=t.urlGallatinIcp,A=t.fEnableLivePreview;function R(e,t){y&&!k?n.onAgreementClick(e):s.open(t,"_blank")}n.onAgreementClick=o.create(),n.onShowDebugDetails=o.create(),n.onSwitchView=o.create(),n.showDebugDetails=i.observable(!!f),n.focusMoreInfo=i.observable(!1).extend({notify:"always"}),n.showFooter=b===undefined||b,n.hideTOU=v||!1,n.hidePrivacy=h||!1,n.termsText=d.htmlUnescape(_)||w.MOBILE_STR_Footer_Terms,n.privacyText=d.htmlUnescape(C)||w.MOBILE_STR_Footer_Privacy,n.termsLink=S||P,n.privacyLink=x||T,n.showLogo=a&&!k,n.showLinks=r,n.hideFooter=u,n.showIcpLicense=k,n.debugDetails=p,n.impressumLink=D,n.a11yConformeLink=E,n.icpLink=I,n.hasDarkBackground=g,n.useDefaultBackground=m,n.privacyLink_onClick=function(){R(c.Privacy,n.privacyLink)},n.termsLink_onClick=function(){R(c.Tou,n.termsLink)},n.impressumLink_onClick=function(){R(c.Impressum,n.impressumLink)},n.services_onClick=function(){n.onSwitchView(l.SeeHowDataIsManaged)},n.a11yConformeLink_onClick=function(){R(c.A11yConforme,n.a11yConformeLink)},n.moreInfo_onClick=function(){A||(n.setDebugDetailsState(!n.showDebugDetails()),n.onShowDebugDetails(),n.showDebugDetails()||n.focusMoreInfo(!0))},n.setDebugDetailsState=function(e){n.showDebugDetails(e)},n.focusMoreInfoLink=function(){n.focusMoreInfo(!0)}}i.components.register("footer-control",{viewModel:u,template:t(471),synchronous:!s.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(s.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=u},function(e,n){n.PlatformTimeout=0,n.Timeout=6e5,n.PromiseTimeout=250,n.SupportedKeyAlgorithms=[-7,-257],n.Error={Internal:"InternalError",FidoCreateCallUnexpectedResponse:"FidoCreateCallUnexpectedResponse"}},function(e,n,t){var i=t(2),a=t(1),o=t(3),r=t(4),s=t(12),c=t(457),d=window,l=o.DateTime,u=a.Helper;function p(e){var n=this,t=null,a=e.debugDetails,o=e.serverData,c=e.isDebugTracingEnabled,d=e.useWiderWidth,p=o.strServiceExceptionMessage,f=o.urlSetDebugMode,g=o.sPOST_Username,m=o.sSigninName,b=o.str,v=o.sErrorCode;function h(){n.sending(!1),n.isDebugTracingEnabled(!n.isDebugTracingEnabled()),n.onSetDebugTracing()}function _(){n.sending(!1),n.debugModeError(b.STR_Error_Details_Debug_Mode_Failure)}n.onCloseBanner=r.create(),n.onSetDebugTracing=r.create(),n.debugModeError=i.observable(),n.isDebugTracingEnabled=i.observable(c),n.sending=i.observable(!1),n.showBanner=i.observable(!0),n.showDebugDetailsCopyMessage=i.observable(!1),n.isFocusActivated=i.observable(!1),n.unsafe_exceptionMessage=null,n.debugDetails=null,n.useWiderWidth=d,n.hideBanner_onClick=function(){n.onCloseBanner(),n.showBanner(!1)},n.setDebugMode_onClick=function(){if(!n.sending()){var e=new s;n.sending(!0),n.debugModeError("");var i={mode:n.isDebugTracingEnabled()?0:1,user:t};e.Json({url:f},i,h,_)}},n.activateFocus=function(){this.isFocusActivated(!0)},t=g||m||"",n.unsafe_exceptionMessage=u.htmlUnescape(p),n.debugDetails=a||{},v&&(n.debugDetails.errorCode=v),n.debugDetails.timestamp||(n.debugDetails.timestamp=l.getUTCString())}c.applyExtensions(i),i.components.register("debug-details-control",{viewModel:p,template:t(458),synchronous:!d.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(d.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=p},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=t(0),s=window,c=r.DialogId;function d(e){var n=this,t=e.isPlatformAuthenticatorAvailable;n.onRegisterDialog=o.create(),n.onUnregisterDialog=o.create(),n.onShowDialog=o.create(),n.isPlatformAuthenticatorAvailable=t,n.hasFocus=i.observable(!1),n.fidoHelp_onClick=function(){n.onShowDialog(c.FidoHelp).then((function(){n.hasFocus(!0)}))}}i.components.register("fido-help-button-control",{viewModel:d,template:t(492),synchronous:!s.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(s.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=d},function(e,n,t){var i=t(28);n.applyExtenders=function(e){e.extenders.preventExternalWrite=function(n){var t=n(),i=e.observable(t).extend({notify:"always"});return e.pureComputed({read:function(){return i()},write:function(e){e!==t&&i(t)}}).extend({notify:"always"})},e.extenders.flowTokenUpdate=function(n,t){return e.pureComputed({read:n,write:function(e){e&&(t&&(t.sFTTag&&(t.sFTTag=t.sFTTag.replace(t.sFT,e)),t.sFT=e),n(e))}}).extend({notify:"always"})},e.extenders.logValue=function(e,n){return n&&i.registerTracingObservables(n.viewModel,e,n.tracingOptions),e},e.extenders.loadImageFromUrl=function(n){return e.pureComputed({read:n,write:function(e){if(n()!==e)if(e){var t=new Image;t.onload=function(){n(e)},t.src=e}else n(e)}}).extend({notify:"always"})}}},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=t(0),s=t(18),c=t(9),d=t(24),l=t(17),u=t(3),p=window,f=u.String,g=r.PaginatedState,m=r.CredentialType,b=r.DialogId;function v(e){var n=this,t=e.serverData,a=e.username,r=e.availableCreds||[],u=e.currentCred||{},p=e.flowToken,v=e.showForgotUsername,h=e.hideCredSwitchLink,_=e.ariaDescribedBy,C=e.setFocus,S=t.str,x=t.urlForgotUsername,w=t.sSiteId,y=t.sClientId,k=t.sForwardedClientId,P=t.sNoPaBubbleVersion,T=t.fShowSignInOptionsAsButton,D=t.fOfflineAccountVisible,E=t.fUseCertificateInterstitialView,I=t.fIsPasskeySupportEnabled,A=t.fIsVerifiableCredentialsSupportEnabled,R=t.fUseWebviewFidoCustomProtocol,L=[],B=null,O=i.observable();function U(e){N(e),n.onSetPendingRequest(!1),n.onSwitchView(g.OneTimeCode,!1,B)}function F(e){var t,i=e.getOtcStatus();switch(N(e),i){case l.Status.FTError:t=S.CT_OTC_STR_Error_FlowExpired;break;default:t=B.proof.str.CT_OTCS_STR_Error_SendCodeServer||""}n.onSetPendingRequest(!1),n.credLinkError(t)}function N(e){e&&(e.getFlowToken?(p=e.getFlowToken(),n.onUpdateFlowToken(p)):e.FlowToken&&(p=e.FlowToken,n.onUpdateFlowToken(p)))}n.onSwitchView=o.create(),n.onRedirect=o.create(),n.onRegisterDialog=o.create(),n.onUnregisterDialog=o.create(),n.onShowDialog=o.create(),n.onSetPendingRequest=o.create(),n.onUpdateFlowToken=o.create(),n.credentialCount=0,n.selectedCredType=null,n.selectedCredShownOnlyOnPicker=!1,n.switchToCredId=null,n.switchToCredText=null,n.showSwitchToCredPickerLink=!1,n.showForgotUsername=v,n.isUserKnown=!!u.credType,n.displayHelp=!u.credType,n.hideCredSwitchLink=h,n.isOfflineAccountVisible=D,n.ariaDescribedBy=_,n.setFocus=C,n.isPlatformAuthenticatorAvailable=i.observable(!1),n.credLinkError=i.observable(),n.fidoHelp_onClick=function(){n.onShowDialog(b.FidoHelp)},n.view_onUpdateFlowToken=function(e){p=e},n.switchToCredPicker_onClick=function(){n.onSwitchView(g.CredentialPicker)},n.switchToCred_onClick=function(){var e=B&&B.credType||m.Password;switch(n.credLinkError(""),e){case m.OneTimeCode:if(B.proof.clearDigits)n.onSwitchView(g.ProofConfirmation,!1,B);else{var t=function(){var e={username:f.cleanseUsername(a),proofData:B.proof.data,proofType:B.proof.type,purpose:B.proof.isNopa?l.Purpose.NoPassword:l.Purpose.OtcLogin,flowToken:p,isEncrypted:B.proof.isEncrypted,siteId:w,clientId:y,forwardedClientId:k,noPaBubbleVersion:P,successCallback:U,failureCallback:F};if(e.isEncrypted)switch(B.proof.type){case PROOF.Type.Email:e.proofConfirmation=B.proof.display;break;case PROOF.Type.SMS:case PROOF.Type.Voice:e.proofConfirmation=f.cleanseUsername(B.proof.display).slice(-4)}return e}(),i=new d(t);n.onSetPendingRequest(!0),i.sendRequest()}break;case m.OtherMicrosoftIdpFederation:case m.LinkedIn:case m.GitHub:case m.Google:case m.Facebook:n.onRedirect(B.redirectUrl,B.redirectPostParams||null);break;case m.Certificate:E?n.onSwitchView(g.CertificateInterstitialView):n.onRedirect(B.redirectUrl,B.redirectPostParams||null);break;default:n.onSwitchView(L[e].viewId)}},n.forgotUsername_onClick=function(){document.location.assign(x)},n.getSwitchToCredText=function(){return i.unwrap(n.switchToCredText)},function(){if(L[m.Password]={viewId:g.Password,credId:"idA_PWD_SwitchToPassword",credText:S.CT_RNGC_STR_SwitchToPassword_Link},L[m.RemoteNGC]={viewId:g.RemoteNGC,credId:"idA_PWD_SwitchToRemoteNGC",credText:S.CT_PWD_STR_SwitchToRemoteNGC_Link},L[m.Fido]={viewId:g.Fido,credId:"idA_PWD_SwitchToFido",credText:I?S.CT_PWD_STR_SwitchToPasskey_Link:O},L[m.Certificate]={credId:"idA_PWD_SwitchToCertificate",credText:S.CT_STR_CredentialPicker_Option_Certificate},L[m.OtherMicrosoftIdpFederation]={credId:"useMicrosoftLink",credText:S.CT_PWD_STR_UseMicrosoft_Link},L[m.LinkedIn]={credId:"useLinkedInLink",credText:S.CT_PWD_STR_UseLinkedIn_Link},L[m.GitHub]={credId:"useGitHubLink",credText:S.CT_PWD_STR_UseGitHub_Link},L[m.Google]={credId:"useGoogleLink",credText:S.CT_PWD_STR_UseGoogle_Link},L[m.Facebook]={credId:"useGoogleLink",credText:S.CT_PWD_STR_UseFacebook_Link},L[m.Federation]={viewId:g.IdpRedirect,credId:"redirectToIdpLink",credText:S.CT_RNGC_STR_SwitchToFederated_Link},L[m.RemoteLogin]={viewId:g.RemoteLoginPolling,credId:"remoteLoginLink",credText:S.CT_PWD_STR_RemoteLoginLink},L[m.OneTimeCode]={viewId:g.OneTimeCode,credId:"otcLoginLink",credText:S.CT_PWD_STR_SwitchToOTC_Link},L[m.AccessPass]={viewId:g.AccessPass,credId:"accessPassLink",credText:S.CT_PWD_STR_Login_SwitchToAccessPassLink},A&&(L[m.VerifiableCredential]={viewId:g.VCPresentation,credId:"vcPresentation",credText:S.CT_PWD_STR_Login_SwitchToVerifiableCredentialLink}),O(S.CT_PWD_STR_SwitchToFidoCrossPlatform_Link),i.utils.arrayForEach(r,(function(e){if(L[e.credType]){var t=e.credType===u.credType,i=e.credType===m.OneTimeCode,a=t&&i&&e.proof.data===u.proof.data,o=t&&i&&e.proof.type===u.proof.type;(!t||i&&!a||i&&!o)&&(n.credentialCount++,B=e)}e.credType===m.Fido&&c.throwUnhandledExceptionOnRejection(s.isPlatformAuthenticatorAvailable(R).then(null,(function(){return!1})).then((function(e){e&&(O(S.CT_PWD_STR_SwitchToFido_Link),n.isPlatformAuthenticatorAvailable(!0))}))),e.credType===m.OfflineAccount&&(n.credentialCount++,n.selectedCredShownOnlyOnPicker=!0)})),B&&1===n.credentialCount&&(n.selectedCredType=B.credType,n.selectedCredShownOnlyOnPicker=!!B.shownOnlyOnPicker,n.switchToCredId=L[n.selectedCredType||m.Password].credId,n.switchToCredText=L[n.selectedCredType||m.Password].credText,n.selectedCredType===m.OneTimeCode))switch(B.proof.type){case PROOF.Type.Email:n.switchToCredText=f.format(S.CT_OTC_STR_SwitchToOtc_EmailLink,B.proof.display);break;case PROOF.Type.SMS:n.switchToCredText=f.format(S.CT_OTC_STR_SwitchToOtc_SmsLink,B.proof.display);break;case PROOF.Type.Voice:n.switchToCredText=f.format(S.CT_OTC_STR_SwitchToOtc_VoiceLink,B.proof.display)}n.showSwitchToCredPickerLink=(!T||n.isUserKnown)&&(n.credentialCount>1||1===n.credentialCount&&(v||n.selectedCredShownOnlyOnPicker))}()}i.components.register("cred-switch-link-control",{viewModel:v,template:t(491),synchronous:!p.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(p.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=v},function(e,n,t){var i=t(2),a=t(3).String;e.exports=function(e,n){var t=this,o=null;t.placeholderTextboxMethods=i.observable(),t.value=i.observable(n||""),t.focused=i.observable(!1).extend({notify:"always"}),t.error=e,t.textbox_onUpdateFocus=function(e){t.focused(e)},t.getTrimmedValue=function(e){var n=a.trim(t.value()||"");return e&&e>0&&(n=n.substring(0,e)),n},t.placeholderTextboxMethods.subscribe((function(e){e&&!o&&function(e){var n=t.value.peek();e.toggleVisibility(!n),o=t.value.subscribe((function(n){e.toggleVisibility(!n)}))}(e)}))}},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=window;function s(e){var n=this,t="placeholder"in document.createElement("input"),r=e.serverData,s=e.hintText,c=e.hintCss||"placeholder",d=r.fIsHosted;n.onUpdateFocus=o.create(),n.hintText=s,n.usePlaceholderAttribute=!1,n.placeholderVisible=i.observable(!0),n.hintCss=i.pureComputed((function(){var e={};return c&&i.utils.arrayForEach(c.split(" "),(function(n){e[n]=!0})),e})),n.placeholderText=i.pureComputed((function(){if(n.usePlaceholderAttribute)return n.hintText})),n.toggleVisibility=function(e){n.placeholderVisible(e)},n.placeholder_onClick=function(){n.onUpdateFocus(!0)},t&&a.Helper.isPlaceholderAttributeAllowed(d)&&(n.usePlaceholderAttribute=!0)}i.components.register("placeholder-textbox-field",{viewModel:s,template:t(490),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){var i=t(2),a=t(1),o=t(3),r=window,s=o.String,c=a.Helper;function d(e){var n=this,t=e.serverData,i=e.title,a=e.subtitle,o=!1!==e.useSubtitle,r=e.isSignUpView,d=e.headerDescription,l=t.oAppCobranding,u=t.str,p=t.fIsSelfServiceSignupUxEnabled,f=t.fIsCiamUserFlowUxNewLogicEnabled&&t.fIsCiamUserFlowUx,g=t.sCompanyDisplayName,m=t.fUseNonMicrosoftDefaultBrandingForCiam;n.title=null,n.subtitle=null,n.headerDescription=null,function(){var e=!(!l||!l.friendlyAppName);if(n.isSubtitleVisible=o&&(e||a),n.title=i,n.subtitle=a,p||f){var t=c.htmlUnescape(g);if(d)n.headerDescription=d;else if(g){var b=r?u.STR_SSSU_SignUp_HeaderDescription:u.STR_SSSU_SignIn_HeaderDescription;n.headerDescription=m?s.format(b,t):s.format(b,g)}}}()}i.components.register("header-control",{viewModel:d,template:t(494),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents)}),e.exports=d},function(e,n,t){e.exports=t.p+"content/images/picker_account_msa_7a63b3ce03943629f052226aaa378291.png"},function(e,n,t){e.exports=t.p+"content/images/picker_account_msa_3b879963b4f70829fd7a25cbc9519792.svg"},function(e,n){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(i){"object"==typeof window&&(t=window)}e.exports=t},function(e,n,t){var i=t(2);n.errorComputed=function(e){var n=i.observable(),t=i.observable(),a=i.pureComputed(e).extend({notify:"always"}),o=i.pureComputed((function(){if(a())return a();if(n()){var e=n();return n(null),e}return null}));return i.utils.extend(i.pureComputed((function(){if(o())return o();if(t()){var e=t();return t(null),e}return null})),{isBlocking:function(){return null!==o()},setBlockingError:function(e){n(e)},setNonBlockingError:function(e){t(e)},setError:function(e,i){i?n(e):t(e)},clearNonBlockingError:function(){t(null),t.valueHasMutated()}})}},function(e,n,t){var i=t(10),a=t(3),o=t(1),r=t(12),s=t(5),c=t(323),d=t(324),l=window,u=o.QueryString,p=o.Cookies,f=a.Array;e.exports=function(e){var n=e,t=n.bsso||{enabled:!1},a=n.fIsCloudBuild,o=n.fTrimChromeBssoUrl,g=!1!==n.checkApiCanary,m=t.cookieNames,b=null;function v(){var e=new Date;e.setSeconds(e.getSeconds()+60),p.writeWithExpiration(m.ssoPulled,"1",!a,e.toUTCString());var n=l.location.href;return n=u.appendOrReplace(n,"sso_reload","true"),t.reloadOnFailure||"select_account"!==u.extract("prompt").toLowerCase()||(n=u.appendOrReplace(n,"prompt","")),n}function h(e){b.traces.push(e)}function _(e,n){b.data[e]=n}function C(e){return b.result=e.newSessions?"UserList":"Reload",x().then((function(){return e}))}function S(e){return b.result="Error",e instanceof c.Error?"OSError"===e.code&&e.externalData&&e.externalData.error?b.error=e.externalData.error:b.error=e.code:b.error=e,x().then((function(){return s.reject(e)}))}function x(){return new s((function(e){try{l.console&&l.console.info("BSSO Telemetry: "+i.stringify(b))}catch(n){}t.telemetry.url?new r({checkApiCanary:g}).Beacon({url:t.telemetry.url},b,e,e,500):e()}))}this.loginWindowsUserAsync=function(e){return t.enabled?function(e){if(!l.navigator||"function"!=typeof l.navigator.msLaunchUri)return _("BSSO.info","not-supported"),h("window.navigator.msLaunchUri is not available for _loginWindowsUser"),s.reject("bssoNotSupported");var n="abort"===t.initiatePullTimeoutAction;return new d({logMessage:h,logDataPoint:_},t.initiatePullTimeoutMs,t.overallTimeoutMs,n).loginWindowsUserAsync(e).then((function(e){return e.reload?(h("SSO cookie detected. Refreshing page."),v()):s.reject("error")}))}(e).then(C,S):s.reject("bssoDisabled")},this.pullBrowserSsoCookieAsync=function(){var e,n=t.failureRedirectUrl||t.reloadOnFailure,r=t.type;return t.enabled?("windows"===r?e=function(){if(!l.navigator||"function"!=typeof l.navigator.msLaunchUri)return _("BSSO.info","not-supported"),h("window.navigator.msLaunchUri is not available for _pullBrowserSsoCookie"),s.reject("bssoNotSupported");var e=p.getCookie(m.ssoTiles)||t.forceTiles;if(!e&&p.getCookie(m.ssoPulled))return _("BSSO.info","throttled"),h("Cookie pull throttled"),s.reject("throttled");var n="tbauth://login.windows.net?context="+encodeURIComponent(l.location.href.split("/",3).join("/"))+(t.nonce?"&request_nonce="+encodeURIComponent(t.nonce):"")+(t.rid?"&rid="+encodeURIComponent(t.rid):""),a=n;e&&(a=u.appendOrReplace(a,"user_id","*"),p.remove(m.ssoTiles));var o="abort"===t.initiatePullTimeoutAction;return new d({logMessage:h,logDataPoint:_},t.initiatePullTimeoutMs,t.overallTimeoutMs,o).pullBrowserSsoCookieAsync(a).then((function(e){if(e.reload)return h("SSO cookie detected. Refreshing page."),{redirectUrl:v()};if(e.userList){var t=function(e,n){var t=[],a=i.parse(e).users;a&&a.length>0?(f.forEach(a,(function(e){var i={ssoUniqueId:e.unique_id,displayName:e.display_name||"",name:e.upn,isWindowsSso:!0,isSignedIn:!0,url:n};t.push(i)})),h("User list processed. List: "+i.stringify(t))):h("User list is empty.");return t}(e.userList,n);return t.length>0?{newSessions:t}:s.reject("noUsers")}}))}():"chrome"===r&&(e=new c({logMessage:h,logDataPoint:_},t.nonce,"login.microsoftonline.com",a,o).getCookiesAsync().then((function(e){if(!e||!e.length)return s.reject(new c.Error("PageException","Extension returned no cookies"));for(var n=0,t=e.length;n<t;++n){var i=e[n].data;-1!==i.indexOf(";")&&(i=i.substr(0,i.indexOf(";"))),p.write(e[n].name,i,!a)}return h("SSO cookie detected. Refreshing page."),{redirectUrl:v()}})).then(null,(function(e){return p.write(m.aadSso,e.toCookieString(),!a),h("Error: "+e.toString()),s.reject(e)}))),e.then((function(e){return n&&!e.redirectUrl?s.reject("silentPullFailed"):e})).then(C,S).then(null,(function(e){return n?t.reloadOnFailure?{redirectUrl:v()}:{redirectUrl:t.failureRedirectUrl}:s.reject(e)}))):s.reject("bssoDisabled")},this.isEnabled=function(){return!!t.enabled},t.enabled&&(b={result:null,error:null,type:t.telemetry.type||null,data:{},traces:[]},t.initiatePullTimeoutMs=t.initiatePullTimeoutMs||t.overallTimeoutMs,t.initiatePullTimeoutAction=t.initiatePullTimeoutAction||"abort",h("BrowserSSO Initialized"))}},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_fido_white_864e6fa7d7fc3258841e849a2a379a0d.png"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_fido_white_e029710271b14f3937af7172f6076039.svg"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_fido_a8572092eb6b15420112017f4c356e31.png"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_fido_dc174eba8703c4b23780692b84de3fb1.svg"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_passkey_white_4312cd76f00ffeb4f262f0f60e0e5339.png"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_passkey_white_e66de563e5288734b1a0b8c5ce46e308.svg"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_passkey_4534333b7bb65cc5e3870e8afbe29e3f.png"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_passkey_1500b2043f4d1698f9df6089f67559d7.svg"},function(e,n,t){var i=t(12),a=t(0),o=t(5),r=t(8),s={DeviceAuth:{PageId:"ConvergedRemoteConnect",ActionId:"OAuth2DeviceAuth",ConfirmationViewId:a.PaginatedState.RemoteConnectCanaryValidation},FidoAuth:{PageId:"PaginatedLogin",ActionId:"FidoGet",ConfirmationViewId:a.PaginatedState.PartnerCanaryValidation}},c=l.PartnerCanaryScenario={Undefined:0,Fido:1},d=l.CanaryValidationSuccessAction={SwitchView:1,Redirect:2};function l(e){var n=e,t=n.sExternalCanary,u=n.urlCanaryValidation,p=!!n.sRemoteConnectAppName,f=!!n.fIsRemoteConnectSignup,g=n.urlSignUp,m=n.oSignUpPostParams,b=n.iPartnerCanaryScenario;this.validateAsync=function(){return new o((function(e,n){var o=function(){if(p)return s.DeviceAuth;if(b===c.Fido)return s.FidoAuth;throw"Canary Validation: Flow IDs not known."}();o.Canary=t;var v=function(){if(f)return{action:d.Redirect,redirectUrl:g,redirectPostParams:m,isIdpRedirect:!1};return{action:d.SwitchView}}();new i({checkApiCanary:!1,withCredentials:!0}).Json({url:u,eventId:r.EventIds.Api_CanaryValidation},o,(function(){e(v)}),(function(e){n(new l.CanaryValidationError(e,o.ConfirmationViewId,v))}),a.DefaultRequestTimeout)}))}}l.CanaryValidationError=function(e,n,t){this.name="CanaryValidationError",this.message="Canary validation failed, user confirmation required.",this.stack=(new Error).stack,this.innerError=e,this.confirmationViewId=n,this.postConfirmationAction=t},e.exports=l},function(e,n,t){var i=t(3),a=t(7),o=i.Array,r={mergeSessions:function(e,n,t){var i=[];return o.forEach(n,(function(n){var a=function(e,n){for(var t=0;t<n.length;t++)if(n[t].name===e.name&&n[t].idp===e.idp)return t;return-1}(n,e);-1===a?(n.isWindowsSso?e.unshift(n):e.push(n),i.push(n)):n.isWindowsSso?(e.splice(a,1),e.unshift(n),i.push(n)):t&&(e.splice(a,1),e.push(n),i.push(n))})),i},parseMeControlSessions:function(e){return o.map(e,(function(e){var n=e.firstName,t=e.lastName;return t&&(n?n+=" "+t:n=t),{id:e.sessionId,fullName:n,name:e.memberName||e.signInName,displayName:e.memberName||e.signInName,idp:a.SessionIdp.Msa,isOtherIdp:!0,isSignedIn:e.isSignedIn||1===e.authenticatedState||2===e.authenticatedState,isWindowsSso:e.isWindowsSso||!1,isMeControlSession:!0,isGitHubFed:e.isGitHubFed||!1}}))},parseBssoSessions:function(e){return o.map(e,(function(e){return{ssoUniqueId:e.ssoUniqueId,name:e.name,displayName:e.displayName,idp:a.SessionIdp.Aad,ssoLink:e.url,isWindowsSso:e.isWindowsSso,isSignedIn:e.isSignedIn}}))}};e.exports=r},function(e,n,t){var i=t(2);e.exports=function(e,n,t){var a=this,o=e,r=o.fHideFooter,s=o.fShowPageLevelTitleAndDesc,c=null;a.paginationControlMethods=n||i.observable(),a.backgroundLogoUrl=t||i.observable(),a.animationEnd=i.pureComputed((function(){return a.paginationControlMethods()&&a.paginationControlMethods().view_onAnimationEnd})),a.showBackgroundLogoHolder=i.pureComputed((function(){return a.backgroundLogoUrl()&&a.showLogo()})),a.showErrorPageDebugDetails=i.pureComputed((function(){return a.paginationControlMethods()&&a.paginationControlMethods().currentViewHasMetadata("extraDebugDetails")})),a.showFooterControl=i.pureComputed((function(){return!r&&a.paginationControlMethods()&&a.paginationControlMethods().hasInitialViewShown()})),a.showLogo=i.pureComputed((function(){return!(a.paginationControlMethods()&&a.paginationControlMethods().currentViewHasMetadata("hideLogo"))})),a.showLwaDisclaimer=i.pureComputed((function(){return a.paginationControlMethods()&&!a.paginationControlMethods().currentViewHasMetadata("hideLwaDisclaimer")})),a.showPageLevelTitleControl=i.pureComputed((function(){return s&&!(a.paginationControlMethods()&&a.paginationControlMethods().currentViewHasMetadata("hidePageLevelTitleAndDesc"))})),a.useWiderWidth=i.pureComputed((function(){return a.paginationControlMethods()&&a.paginationControlMethods().currentViewHasMetadata("wide")})),a.onLoad=function(){a.paginationControlMethods().restoreState(c),c=null},a.onUnload=function(e){c=e}}},function(e,n){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,n,t){var i=t(2),a=t(1),o=t(370),r=window;function s(e){var n=this,t=e.serverData.isCustomPerf,a=0,s=!1,c=!1;function d(e){var n={};if(e.toJSON)return e.toJSON();for(var t in e)n[t]=e[t];return n}n.timeOnPage=i.observable(null),n.recordSubmit=function(){r.performance&&r.performance.timing&&n.timeOnPage((new Date).getTime()-r.performance.timing.loadEventEnd)},n.setPageLoadCompleted=function(){s=!0,setTimeout((function(){!function(){if(c||!s)return;c=!0;var e=function(){var e=r.performance,n=r.navigator,t={};if(!e)return null;e.navigation&&(t.navigation=d(e.navigation));e.timing&&(t.timing=d(e.timing),a>0&&(t.timing.customLoadEventEnd=a));e.getEntries&&(t.entries=i.utils.arrayMap(e.getEntries(),d));n.connection&&(t.connection=d(n.connection));return t}();try{o.SendTelemetryPerfData(e,"LPerf")}catch(n){}}()}),0)},n.setCustomPageLoadCompletedTime=function(e){a=e||(new Date).getTime()},t||setTimeout((function(){n.setPageLoadCompleted()}),100)}i.components.register("instrumentation-control",{viewModel:s,template:t(371),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){var i=t(2),a=t(1),o=a.Helper,r=window;function s(e){var n=this,t=r.ServerData,i=e.bannerLogoUrl,a=e.isChinaDc,s=t.fIsCiamUserFlowUx,c=t.sCompanyDisplayName,d=t.fUseNonMicrosoftDefaultBrandingForCiam;n.bannerLogoUrl=i,n.isChinaDc=a,n.isCiamUserFlowUx=s,n.bannerLogoText=null,function(){if(d){var e=o.htmlUnescape(c);n.bannerLogoText=e.toUpperCase()}}()}i.components.register("logo-control",{viewModel:s,template:t(372),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents)}),e.exports=s},function(e,n,t){var i=t(2),a=t(3),o=t(1),r=t(4),s=null,c=null,d=t(380),l=window,u=null;function p(e,n){var c=this,l=!1,p=e.serverData,f=e.showButtons||!1,g=e.showFooterLinks,m=!1!==e.showFooterLogo,b=e.useWizardBehavior,v=e.hideFromAria||i.observable(!1),h=p.iProductIcon;p.fFixSignoutBrandingCiam;function _(e,n){var t=["Microsoft","OneDrive","Skype","Bing","Xbox","Word","Outlook","Office","Excel","PowerPoint","Cortana","SkypeDialer","Health","MileIQ","Beam","MSN","Minecraft"];return(e<0||e>=t.length)&&(e=0),a.String.format("./AppCentipede_{0}{1}.{2}",t[e],n?"_white":"",l?"svg":"png")}c.useLayoutTemplates=!0,c.templateNodes={},c.showButtons=f,c.footer={showLinks:g,showLogo:m},c.centipede={getLightUrl:function(){return d(_(h,!0))},getDarkUrl:function(){return d(_(h,!1))}},c.hideFromAria=v,c.isInternalModeEnabled="1"===o.QueryString.extract("psi"),c.viewModel=null,c.viewAgreement=i.observable(!1),c.agreementType=i.observable(),c.isLightboxTemplate=i.observable(!0),c.isVerticalSplitTemplate=i.observable(!1),c.isTemplateLoaded=i.observable(!1),c.onFooterAgreementClick=r.create(),c.footer_agreementClick=function(e){c.onFooterAgreementClick(e)},c.agreement_backButtonClick=function(){c.viewAgreement(!1)},c.showAgreement=function(e){c.agreementType(e),c.viewAgreement(!0)},c.updateBranding=function(e){var n;n=e,s.getLayoutTemplateConfig(n).layoutType===u.VerticalSplit?(c.isLightboxTemplate(!1),c.isVerticalSplitTemplate(!0),t.e(40).then(function(){t(544),c.isTemplateLoaded(!0)}.bind(null,t))["catch"](t.oe)):(c.isVerticalSplitTemplate(!1),c.isLightboxTemplate(!0))},function(){if(l=o.Helper.isSvgImgSupported(),n&&i.utils.arrayForEach(n,(function(e){e.id&&(c.templateNodes[e.id]=e.childNodes)})),b){0}}()}s=t(14),c=t(0),u=c.LayoutTemplateType,i.components.register("master-page",{viewModel:{createViewModel:function(e,n){return new p(e,n.templateNodes)}},template:t(449),synchronous:!l.ServerData.iMaxStackForKnockoutAsyncComponents||o.Helper.isStackSizeGreaterThan(l.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=p},function(e,n,t){var i=t(2),a=t(1),o=window;function r(){var e=this;e.isAppBranding=i.observable(!1),e.backgroundStyle=i.observable(),e.smallImageUrl=i.observable(),e.backgroundImageUrl=i.observable(),e.useImageMask=i.observable(!1),e.useTransparentLightBox=i.observable(!1),e.updateBranding=function(n){e.isAppBranding(!!n.backgroundLogoUrl),e.backgroundStyle(n.color),e.smallImageUrl(n.smallImageUrl),e.backgroundImageUrl(n.backgroundImageUrl),e.useImageMask(!!n.useImageMask),e.useTransparentLightBox(!!n.useTransparentLightBox)}}i.components.register("background-image-control",{viewModel:r,template:t(452),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=r},function(e,n,t){var i=t(2),a=t(1),o=window;i.components.register("environment-banner-control",{template:t(453),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents)})},function(e,n,t){var i=t(2),a=t(1),o=window;function r(e){var n=e.isVerticalSplitTemplate,t=e.hasHeader||!1;this.isVerticalSplitTemplate=n||!1,this.hasHeader=t}i.components.register("content-control",{viewModel:r,template:t(454),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=r},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_qrpin_white_97ee76155bdef68d51f882d111ce1a79.png"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_qrpin_white_33f29ddd025aa33fa298befbb72a24a7.svg"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_qrpin_4a5523d6de79a0f34c943d6029ea5168.png"},function(e,n,t){e.exports=t.p+"content/images/credentialoptions/cred_option_qrpin_ada5adc951d9c983f746704ac9363507.svg"},function(e,n,t){var i=t(2),a=t(0),o=t(1),r=t(472),s=t(4),c=t(9),d=t(15),l=t(11).getInstance(window.ServerData),u=t(8),p=window,f=document,g=f.head,m=a.PaginatedState.Previous,b=a.AnimationState,v=a.AnimationName,h=a.AnimationTimeout,_=o.Helper;function C(e,n,t,a){var o=this,C=p.ServerData,S=e.initialViewId||null,x=e.currentViewId||null,w=e.initialSharedData||{},y=e.initialError,k=e.enableCssAnimation,P=e.disableAnimationIfAnimationEndUnsupported,T=d.getInstance(C),D=e.saveSharedDataOnBack||!1,E=C.fRemoveMinWidthFromLightBox,I=C.fDisplayResourceAppConsentDetails,A=C.fPassTelephonyAuthMethod,R=(C.fFixSignoutBrandingCiam,t),L=a,B=null,O=b.End,U=!1,F=null,N=null,M=!1,V=null,j=i.observable();function H(){var e=o.currentViewIndex();return R[e]?R[e]():null}function W(e){j(null),o.view_onSetIdentityBackButtonState(),o.isInitialState=B.getState().isInitialState,o.onShowView(L[e].metadata,e),j(e),o.hasInitialViewShown(!0);var n=document.querySelectorAll("[data-viewid]");if(T&&n&&n.length>0)try{T.applyClientEventBindings(n[0])}catch(t){}}function $(e,n){var t=j(),i=o.onLoadView(e);if(T&&t)try{T.set("viewId",t,!0),T.post(!0)}catch(a){}i?c.throwUnhandledExceptionOnRejection(i.then((function(){G(t,e,n)}))):G(t,e,n)}function G(e,n,t){M&&(e!==n||t)&&(!y||e)?(P&&null===F&&(V=setTimeout((function(){M=!1,o.hidePaginatedView(!1),o.hidePaginatedView.hideSubView(!1),o.onSetLightBoxFadeIn(!1),O=b.End,$(n)}),h)),F=e,N=n,O=b.Begin,o.view_onAnimationEnd()):(o.animate(v.None),o.view_onAnimationEnd(),W(n),o.unsafe_displayName(o.showIdentityBanner()?_.htmlUnescape(o.sharedData.displayName||p.ServerData.sPOST_Username):""))}function q(e){var n=H();if(n){e&&n.saveSharedData(o.sharedData);var t=n.getState(),i=B.getState();i.viewState=t,B.replaceState(i)}}function K(){U=!0,q(!1)}function z(){U=!1}function Q(e){o.initialError=null,$(e.viewId,e.forceTransitionAnimation)}o.views=n,o.viewInterfaces=t,o.sharedData=w,o.initialError=y,o.isInitialState=!1,o.showLogo=e.showLogo||!1,o.bannerLogoUrl=e.bannerLogoUrl||"",o.isBackButtonVisible=i.observable(!1),o.isBackButtonFocused=i.observable(!1),o.backButtonDescribedBy=i.observable(null),o.hasInitialViewShown=i.observable(!1),o.unsafe_displayName=i.observable(),o.hidePaginatedView=i.utils.extend(i.observable(!1),{hideSubView:i.observable(!1)}),o.animate=i.utils.extend(i.observable(v.None),{animateBanner:i.observable(!1),isSlideOutNext:i.pureComputed((function(){return o.animate()===v.SlideOutNext})),isSlideInNext:i.pureComputed((function(){return o.animate()===v.SlideInNext})),isSlideOutBack:i.pureComputed((function(){return o.animate()===v.SlideOutBack})),isSlideInBack:i.pureComputed((function(){return o.animate()===v.SlideInBack}))}),o.showIdentityBanner=i.pureComputed((function(){var e=j();return e&&L[e].metadata&&L[e].metadata.showIdentityBanner})),o.currentViewIndex=i.pureComputed((function(){var e=j();return L[e]&&!isNaN(L[e].index)?L[e].index:-1})),o.onCancel=s.create(),o.onUnload=s.create(),o.onLoadView=s.create(),o.onShowView=s.create(),o.onSetLightBoxFadeIn=s.create(),o.onAnimationStateChange=s.create(),o.dispose=function(){o.onUnload(B.getState().viewId),B.dispose()},o.setDefaultFocus=function(){var e=H();e&&e.setDefaultFocus&&e.setDefaultFocus()},o.getCurrentViewId=function(){return j()},o.getSharedData=function(){return o.sharedData||{}},o.getSharedDataItem=function(e){return o.getSharedData()[e]},o.getCurrentView=function(){return{viewId:j(),viewInterface:H()}},o.setSharedDataItem=function(e,n){o.sharedData||(o.sharedData={}),o.sharedData[e]=n},o.saveSharedDataOnCurrentView=function(){var e=H();e&&e.saveSharedData(o.sharedData)},o.currentViewHasMetadata=function(e){var n=j();return!!L[n]&&!!L[n].metadata[e]},o.submitCurrentView=function(){var e=H();e&&e.submit&&e.submit()},o.identityBanner_onBackButtonClick=function(){(I||A)&&D&&o.saveSharedDataOnCurrentView(),o.view_onSwitchView(m)},o.restoreState=function(e){var n=B.getState();e&&e!==x&&(n={viewId:x=e},B.pushState(n)),setTimeout((function(){$(n.viewId)}),0)},o.view_onLoad=function(){var e=B.getState(),n=H();if(n){n.restoreState(e?e.viewState:null);var t=o.getCurrentViewId();if(l.setViewViewModel(n,t,L[t].metadata),l.logViewState(n),T&&t)try{T.set("viewId",t,!0)}catch(i){}o.setDefaultFocus()}},o.view_onSwitchView=function(e,n,t){l.logEvent({eventType:"view_onSwitchView",eventId:u.EventIds.Event_PaginationControl_ViewSwitch,eventLevel:u.EventLevel.Critical,eventArgs:{viewId:e,replaceHistory:n},eventOptions:{hidingMode:u.HidingMode.None}}),l.switchView(H());var i=B.getState()||{};t&&(i.forceTransitionAnimation=t,B.replaceState(i)),o.initialError=null,e===m?(U=!0,i.isInitialState?o.onCancel():B.goBack()):(U=!1,q(!0),(n|=e===j()&&!1!==n)?(i.viewId=e,i.viewState=null,B.replaceState(i)):(i={viewId:e},B.pushState(i)),$(e,t))},o.view_onCancel=function(){o.onCancel()},o.view_onSetIdentityBackButtonState=function(e,n,t){o.isBackButtonVisible(e||!1),o.isBackButtonFocused(n||!1),o.backButtonDescribedBy(t||null)},o.view_onAnimationEnd=function(){switch(o.onAnimationStateChange(O,U,!!F),O){case b.Begin:o.animate(v.None),o.animate.animateBanner(!F||L[F].metadata.showIdentityBanner!==L[N].metadata.showIdentityBanner),O=b.RenderNewView,F?o.animate(U?v.SlideOutBack:v.SlideOutNext):o.view_onAnimationEnd();break;case b.RenderNewView:W(N),o.animate.animateBanner()?o.hidePaginatedView(!0):o.hidePaginatedView.hideSubView(!0),o.unsafe_displayName(o.showIdentityBanner()?_.htmlUnescape(o.sharedData.displayName||p.ServerData.sPOST_Username):""),O=b.AnimateNewView,F?setTimeout(o.view_onAnimationEnd,0):o.onSetLightBoxFadeIn(!0);break;case b.AnimateNewView:V&&(clearTimeout(V),V=null),O=b.End,o.hidePaginatedView(!1),o.hidePaginatedView.hideSubView(!1),o.animate(U?v.SlideInBack:v.SlideInNext)}},function(){M=k&&_.isCSSAnimationSupported(),B=new r(K,z,Q);var e=null;if(null===S&&null===x||(S=null===S?x:S,x=null===x?S:x,e={viewId:S,isInitialState:!0},B.replaceState(e)),x!==S&&(e={viewId:x},B.pushState(e)),E){var n=f.createElement("style");n.type="text/css",n.innerHTML=".inner,.promoted-fed-cred-box,.sign-in-box,.new-session-popup-v2sso,.debug-details-banner,.vertical-split-content{min-width:0;}",g.appendChild(n)}}()}function S(e){var n={};return i.utils.arrayForEach(["wide","hideLogo","hideDefaultLogo","dynamicBranding","hideLwaDisclaimer","showIdentityBanner","showFedCredButton","showSignupFedCredButton","hidePageLevelTitleAndDesc","extraDebugDetails","showQrCodeSignInButton"],(function(t){var i=e.getAttribute("data-"+t);i&&(n[t]="true"===i.toLowerCase())})),n}i.components.register("pagination-control",{viewModel:{createViewModel:function(e,n){var t=[],a=[],o={},r=0;return i.utils.arrayForEach(n.templateNodes,(function(e){var n;1===e.nodeType&&null!==(n=e.getAttribute("data-viewid"))&&(t.push(e),a.push(i.observable()),n=isNaN(n)?n:parseInt(n),o[n]={index:r++,metadata:S(e)})})),new C(e,t,a,o)}},template:t(473),synchronous:!p.ServerData.iMaxStackForKnockoutAsyncComponents||o.Helper.isStackSizeGreaterThan(p.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0})},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=window;function s(e){var n=e.isPlatformAuthenticatorAvailable;this.isPlatformAuthenticatorAvailable=n,this.onRegisterDialog=o.create(),this.onUnregisterDialog=o.create()}i.components.register("fido-help-dialog-content-control",{viewModel:s,template:t(493),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){var i=window;t.p=i.ServerData.urlCdn,i.ServerData.urlImagePath=i.ServerData.urlCdn+"images/";var a=t(2),o=t(70),r=t(0),s=t(1),c=t(73),d=t(7),l=t(74);t(15).getInstance(window.ServerData);var u=r.LoginMode,p=s.Helper,f=s.QueryString,g=s.Cookies,m=d.LoginOption,b=!1;function v(e){!function(e){if(e){(new Image).src=e}}(e.urlIPv6Experiment),function(e){if(e.fUpgradeEVCert&&null!==new RegExp("Windows NT ([0-9]{1,}[.0-9]{0,})").exec(navigator.userAgent)&&parseFloat(RegExp.$1)<6&&p.getIEVersion()>=7)try{document.getElementById("ev").src=e.urlEVCertUpgrade}catch(n){}}(e)}o.applyExtensions(a),a.utils.registerEventHandler(i,"load",(function(){var e=i.ServerData;if(e.str=c.getStrings("str",e),e.html=c.getStrings("html",e),e.arrProofData=c.getStrings("proofData"),!b&&!function(e){try{if(top!==self&&top.location.replace(self.location.href),2===e.iFedState&&e.urlFed)return function(e,n,t,i){var a=i.sFedQS;n===m.NothingChecked&&(a=f.appendOrReplace("?"+a,"wctx","LoginOptions%3D3%26"+f.extract("wctx","?"+a)).substr(1));e=f.appendOrReplace(e,"cbcxt",encodeURIComponent(decodeURIComponent(f.extract("cbcxt")))),e=f.appendOrReplace(e,"vv",encodeURIComponent(decodeURIComponent(f.extract("cbcxt")))),e=f.appendOrReplace(e,"username",encodeURIComponent(t)),e=f.appendOrReplace(e,"mkt",encodeURIComponent(decodeURIComponent(f.extract("mkt")))),e=f.appendOrReplace(e,"lc",encodeURIComponent(decodeURIComponent(f.extract("lc")))),document.location.replace(f.append(e,a))}(e.urlFed,e.iDefaultLoginOptions,decodeURIComponent(f.extract("username")),e),!0;if(!g.enabled())return document.location=e.urlNoCookies,!0}catch(n){e.iLoginMode=u.GenericError}return!1}(e))switch(b=!0,e.iLoginMode){case u.GenericError:case u.GenericErrorMobile:case u.GenericErrorHost:case u.SwitchUser:case u.SwitchUserMobile:case u.SwitchUserHost:case u.InviteBlocked:case u.ServiceBlocked:case u.IDPFailed:case u.HIP_Lockout:case u.HIP_LockoutMobile:case u.HIP_LockoutHost:case u.BindFailed:t.e(1).then(function(){var n=t(502);document.body.appendChild(document.createElement("div")).innerHTML=t(503),a.applyBindings(new n(e)),v(e)}.bind(null,t))["catch"](t.oe);break;default:document.body.appendChild(document.createElement("div")).innerHTML=t(359),a.applyBindings(new l(e)),v(e)}}))},function(e,n,t){var i,a,o;
    /*!
     * Knockout JavaScript library v3.5.1
     * (c) The Knockout.js team - http://knockoutjs.com/
     * License: MIT (http://www.opensource.org/licenses/mit-license.php)
     */!function(r){var s=this||(0,eval)("this"),c=s.document,d=s.navigator,l=s.jQuery,u=s.JSON;l||"undefined"==typeof jQuery||(l=jQuery),function(r){a=[n,t],(o="function"==typeof(i=r)?i.apply(n,a):i)===undefined||(e.exports=o)}((function(e,n){function t(e,n){return(null===e||typeof e in S)&&e===n}function i(e,n){var t;return function(){t||(t=C.a.setTimeout((function(){t=r,e()}),n))}}function a(e,n){var t;return function(){clearTimeout(t),t=C.a.setTimeout(e,n)}}function o(e,n){n&&"change"!==n?"beforeChange"===n?this.pc(e):this.gb(e,n):this.qc(e)}function p(e,n){null!==n&&n.s&&n.s()}function f(e,n){var t=this.qd,i=t[P];i.ra||(this.Qb&&this.mb[n]?(t.uc(n,e,this.mb[n]),this.mb[n]=null,--this.Qb):i.I[n]||t.uc(n,e,i.J?{da:e}:t.$c(e)),e.Ja&&e.gd())}var g,m,b,v,h,_,C=void 0!==e?e:{};C.b=function(e,n){for(var t=e.split("."),i=C,a=0;a<t.length-1;a++)i=i[t[a]];i[t[t.length-1]]=n},C.L=function(e,n,t){e[n]=t},C.version="3.5.1",C.b("version",C.version),C.options={deferUpdates:!1,useOnlyNativeEvents:!1,foreachHidesDestroyed:!1},C.a=function(){function e(e,n){for(var t in e)a.call(e,t)&&n(t,e[t])}function n(e,n){if(n)for(var t in n)a.call(n,t)&&(e[t]=n[t]);return e}function t(e,n){return e.__proto__=n,e}function i(e,n,t,i){var a=e[n].match(h)||[];C.a.D(t.match(h),(function(e){C.a.Na(a,e,i)})),e[n]=a.join(" ")}var a=Object.prototype.hasOwnProperty,o={__proto__:[]}instanceof Array,p="function"==typeof Symbol,f={},g={};f[d&&/Firefox\/2/i.test(d.userAgent)?"KeyboardEvent":"UIEvents"]=["keyup","keydown","keypress"],f.MouseEvents="click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave".split(" "),e(f,(function(e,n){if(n.length)for(var t=0,i=n.length;t<i;t++)g[n[t]]=e}));var m,b={propertychange:!0},v=c&&function(){for(var e=3,n=c.createElement("div"),t=n.getElementsByTagName("i");n.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e",t[0];);return 4<e?e:r}(),h=/\S+/g;return{Jc:["authenticity_token",/^__RequestVerificationToken(_.*)?$/],D:function(e,n,t){for(var i=0,a=e.length;i<a;i++)n.call(t,e[i],i,e)},A:"function"==typeof Array.prototype.indexOf?function(e,n){return Array.prototype.indexOf.call(e,n)}:function(e,n){for(var t=0,i=e.length;t<i;t++)if(e[t]===n)return t;return-1},Lb:function(e,n,t){for(var i=0,a=e.length;i<a;i++)if(n.call(t,e[i],i,e))return e[i];return r},Pa:function(e,n){var t=C.a.A(e,n);0<t?e.splice(t,1):0===t&&e.shift()},wc:function(e){var n=[];return e&&C.a.D(e,(function(e){0>C.a.A(n,e)&&n.push(e)})),n},Mb:function(e,n,t){var i=[];if(e)for(var a=0,o=e.length;a<o;a++)i.push(n.call(t,e[a],a));return i},jb:function(e,n,t){var i=[];if(e)for(var a=0,o=e.length;a<o;a++)n.call(t,e[a],a)&&i.push(e[a]);return i},Nb:function(e,n){if(n instanceof Array)e.push.apply(e,n);else for(var t=0,i=n.length;t<i;t++)e.push(n[t]);return e},Na:function(e,n,t){var i=C.a.A(C.a.bc(e),n);0>i?t&&e.push(n):t||e.splice(i,1)},Ba:o,extend:n,setPrototypeOf:t,Ab:o?t:n,P:e,Ga:function(e,n,t){if(!e)return e;var i,o={};for(i in e)a.call(e,i)&&(o[i]=n.call(t,e[i],i,e));return o},Tb:function(e){for(;e.firstChild;)C.removeNode(e.firstChild)},Yb:function(e){for(var n=((e=C.a.la(e))[0]&&e[0].ownerDocument||c).createElement("div"),t=0,i=e.length;t<i;t++)n.appendChild(C.oa(e[t]));return n},Ca:function(e,n){for(var t=0,i=e.length,a=[];t<i;t++){var o=e[t].cloneNode(!0);a.push(n?C.oa(o):o)}return a},va:function(e,n){if(C.a.Tb(e),n)for(var t=0,i=n.length;t<i;t++)e.appendChild(n[t])},Xc:function(e,n){var t=e.nodeType?[e]:e;if(0<t.length){for(var i=t[0],a=i.parentNode,o=0,r=n.length;o<r;o++)a.insertBefore(n[o],i);for(o=0,r=t.length;o<r;o++)C.removeNode(t[o])}},Ua:function(e,n){if(e.length){for(n=8===n.nodeType&&n.parentNode||n;e.length&&e[0].parentNode!==n;)e.splice(0,1);for(;1<e.length&&e[e.length-1].parentNode!==n;)e.length--;if(1<e.length){var t=e[0],i=e[e.length-1];for(e.length=0;t!==i;)e.push(t),t=t.nextSibling;e.push(i)}}return e},Zc:function(e,n){7>v?e.setAttribute("selected",n):e.selected=n},Db:function(e){return null===e||e===r?"":e.trim?e.trim():e.toString().replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")},Ud:function(e,n){return e=e||"",!(n.length>e.length)&&e.substring(0,n.length)===n},vd:function(e,n){if(e===n)return!0;if(11===e.nodeType)return!1;if(n.contains)return n.contains(1!==e.nodeType?e.parentNode:e);if(n.compareDocumentPosition)return 16==(16&n.compareDocumentPosition(e));for(;e&&e!=n;)e=e.parentNode;return!!e},Sb:function(e){return C.a.vd(e,e.ownerDocument.documentElement)},kd:function(e){return!!C.a.Lb(e,C.a.Sb)},R:function(e){return e&&e.tagName&&e.tagName.toLowerCase()},Ac:function(e){return C.onError?function(){try{return e.apply(this,arguments)}catch(n){throw C.onError&&C.onError(n),n}}:e},setTimeout:function(e,n){return setTimeout(C.a.Ac(e),n)},Gc:function(e){setTimeout((function(){throw C.onError&&C.onError(e),e}),0)},B:function(e,n,t){var i=C.a.Ac(t);if(t=b[n],C.options.useOnlyNativeEvents||t||!l)if(t||"function"!=typeof e.addEventListener){if("undefined"==typeof e.attachEvent)throw Error("Browser doesn't support addEventListener or attachEvent");var a=function(n){i.call(e,n)},o="on"+n;e.attachEvent(o,a),C.a.K.za(e,(function(){e.detachEvent(o,a)}))}else e.addEventListener(n,i,!1);else m||(m="function"==typeof l(e).on?"on":"bind"),l(e)[m](n,i)},Fb:function(e,n){if(!e||!e.nodeType)throw Error("element must be a DOM node when calling triggerEvent");var t;if(t=!("input"!==C.a.R(e)||!e.type||"click"!=n.toLowerCase())&&("checkbox"==(t=e.type)||"radio"==t),C.options.useOnlyNativeEvents||!l||t)if("function"==typeof c.createEvent){if("function"!=typeof e.dispatchEvent)throw Error("The supplied element doesn't support dispatchEvent");(t=c.createEvent(g[n]||"HTMLEvents")).initEvent(n,!0,!0,s,0,0,0,0,0,!1,!1,!1,!1,0,e),e.dispatchEvent(t)}else if(t&&e.click)e.click();else{if("undefined"==typeof e.fireEvent)throw Error("Browser doesn't support triggering events");e.fireEvent("on"+n)}else l(e).trigger(n)},f:function(e){return C.O(e)?e():e},bc:function(e){return C.O(e)?e.v():e},Eb:function(e,n,t){var a;n&&("object"==typeof e.classList?(a=e.classList[t?"add":"remove"],C.a.D(n.match(h),(function(n){a.call(e.classList,n)}))):"string"==typeof e.className.baseVal?i(e.className,"baseVal",n,t):i(e,"className",n,t))},Bb:function(e,n){var t=C.a.f(n);null!==t&&t!==r||(t="");var i=C.h.firstChild(e);!i||3!=i.nodeType||C.h.nextSibling(i)?C.h.va(e,[e.ownerDocument.createTextNode(t)]):i.data=t,C.a.Ad(e)},Yc:function(e,n){if(e.name=n,7>=v)try{var i=e.name.replace(/[&<>'"]/g,(function(e){return"&#"+e.charCodeAt(0)+";"}));e.mergeAttributes(c.createElement("<input name='"+i+"'/>"),!1)}catch(t){}},Ad:function(e){9<=v&&(e=1==e.nodeType?e:e.parentNode).style&&(e.style.zoom=e.style.zoom)},wd:function(e){if(v){var n=e.style.width;e.style.width=0,e.style.width=n}},Pd:function(e,n){e=C.a.f(e),n=C.a.f(n);for(var t=[],i=e;i<=n;i++)t.push(i);return t},la:function(e){for(var n=[],t=0,i=e.length;t<i;t++)n.push(e[t]);return n},Da:function(e){return p?Symbol(e):e},Zd:6===v,$d:7===v,W:v,Lc:function(e,n){for(var t=C.a.la(e.getElementsByTagName("input")).concat(C.a.la(e.getElementsByTagName("textarea"))),i="string"==typeof n?function(e){return e.name===n}:function(e){return n.test(e.name)},a=[],o=t.length-1;0<=o;o--)i(t[o])&&a.push(t[o]);return a},Nd:function(e){return"string"==typeof e&&(e=C.a.Db(e))?u&&u.parse?u.parse(e):new Function("return "+e)():null},hc:function(e,n,t){if(!u||!u.stringify)throw Error("Cannot find JSON.stringify(). Some browsers (e.g., IE < 8) don't support it natively, but you can overcome this by adding a script reference to json2.js, downloadable from http://www.json.org/json2.js");return u.stringify(C.a.f(e),n,t)},Od:function(n,t,i){var a=(i=i||{}).params||{},o=i.includeFields||this.Jc,r=n;if("object"==typeof n&&"form"===C.a.R(n)){r=n.action;for(var s=o.length-1;0<=s;s--)for(var d=C.a.Lc(n,o[s]),l=d.length-1;0<=l;l--)a[d[l].name]=d[l].value}t=C.a.f(t);var u=c.createElement("form");for(var p in u.style.display="none",u.action=r,u.method="post",t)(n=c.createElement("input")).type="hidden",n.name=p,n.value=C.a.hc(C.a.f(t[p])),u.appendChild(n);e(a,(function(e,n){var t=c.createElement("input");t.type="hidden",t.name=e,t.value=n,u.appendChild(t)})),c.body.appendChild(u),i.submitter?i.submitter(u):u.submit(),setTimeout((function(){u.parentNode.removeChild(u)}),0)}}}(),C.b("utils",C.a),C.b("utils.arrayForEach",C.a.D),C.b("utils.arrayFirst",C.a.Lb),C.b("utils.arrayFilter",C.a.jb),C.b("utils.arrayGetDistinctValues",C.a.wc),C.b("utils.arrayIndexOf",C.a.A),C.b("utils.arrayMap",C.a.Mb),C.b("utils.arrayPushAll",C.a.Nb),C.b("utils.arrayRemoveItem",C.a.Pa),C.b("utils.cloneNodes",C.a.Ca),C.b("utils.createSymbolOrString",C.a.Da),C.b("utils.extend",C.a.extend),C.b("utils.fieldsIncludedWithJsonPost",C.a.Jc),C.b("utils.getFormFields",C.a.Lc),C.b("utils.objectMap",C.a.Ga),C.b("utils.peekObservable",C.a.bc),C.b("utils.postJson",C.a.Od),C.b("utils.parseJson",C.a.Nd),C.b("utils.registerEventHandler",C.a.B),C.b("utils.stringifyJson",C.a.hc),C.b("utils.range",C.a.Pd),C.b("utils.toggleDomNodeCssClass",C.a.Eb),C.b("utils.triggerEvent",C.a.Fb),C.b("utils.unwrapObservable",C.a.f),C.b("utils.objectForEach",C.a.P),C.b("utils.addOrRemoveItem",C.a.Na),C.b("utils.setTextContent",C.a.Bb),C.b("unwrap",C.a.f),Function.prototype.bind||(Function.prototype.bind=function(e){var n=this;if(1===arguments.length)return function(){return n.apply(e,arguments)};var t=Array.prototype.slice.call(arguments,1);return function(){var i=t.slice(0);return i.push.apply(i,arguments),n.apply(e,i)}}),C.a.g=new function(){var e,n,t=0,i="__ko__"+(new Date).getTime(),a={};return C.a.W?(e=function(e,n){var o=e[i];if(!o||"null"===o||!a[o]){if(!n)return r;o=e[i]="ko"+t++,a[o]={}}return a[o]},n=function(e){var n=e[i];return!!n&&(delete a[n],e[i]=null,!0)}):(e=function(e,n){var t=e[i];return!t&&n&&(t=e[i]={}),t},n=function(e){return!!e[i]&&(delete e[i],!0)}),{get:function(n,t){var i=e(n,!1);return i&&i[t]},set:function(n,t,i){(n=e(n,i!==r))&&(n[t]=i)},Ub:function(n,t,i){return(n=e(n,!0))[t]||(n[t]=i)},clear:n,Z:function(){return t+++i}}},C.b("utils.domData",C.a.g),C.b("utils.domData.clear",C.a.g.clear),C.a.K=new function(){function e(e,n){var t=C.a.g.get(e,i);return t===r&&n&&(t=[],C.a.g.set(e,i,t)),t}function n(n){if(i=e(n,!1))for(var i=i.slice(0),a=0;a<i.length;a++)i[a](n);C.a.g.clear(n),C.a.K.cleanExternalData(n),o[n.nodeType]&&t(n.childNodes,!0)}function t(e,t){for(var i,a=[],o=0;o<e.length;o++)if((!t||8===e[o].nodeType)&&(n(a[a.length]=i=e[o]),e[o]!==i))for(;o--&&-1==C.a.A(a,e[o]););}var i=C.a.g.Z(),a={1:!0,8:!0,9:!0},o={1:!0,9:!0};return{za:function(n,t){if("function"!=typeof t)throw Error("Callback must be a function");e(n,!0).push(t)},yb:function(n,t){var a=e(n,!1);a&&(C.a.Pa(a,t),0==a.length&&C.a.g.set(n,i,r))},oa:function(e){return C.u.G((function(){a[e.nodeType]&&(n(e),o[e.nodeType]&&t(e.getElementsByTagName("*")))})),e},removeNode:function(e){C.oa(e),e.parentNode&&e.parentNode.removeChild(e)},cleanExternalData:function(e){l&&"function"==typeof l.cleanData&&l.cleanData([e])}}},C.oa=C.a.K.oa,C.removeNode=C.a.K.removeNode,C.b("cleanNode",C.oa),C.b("removeNode",C.removeNode),C.b("utils.domNodeDisposal",C.a.K),C.b("utils.domNodeDisposal.addDisposeCallback",C.a.K.za),C.b("utils.domNodeDisposal.removeDisposeCallback",C.a.K.yb),g=[0,"",""],h={thead:m=[1,"<table>","</table>"],tbody:m,tfoot:m,tr:[2,"<table><tbody>","</tbody></table>"],td:b=[3,"<table><tbody><tr>","</tr></tbody></table>"],th:b,option:v=[1,"<select multiple='multiple'>","</select>"],optgroup:v},_=8>=C.a.W,C.a.ua=function(e,n){var t;if(l){if(l.parseHTML)t=l.parseHTML(e,n)||[];else if((t=l.clean([e],n))&&t[0]){for(var i=t[0];i.parentNode&&11!==i.parentNode.nodeType;)i=i.parentNode;i.parentNode&&i.parentNode.removeChild(i)}}else{(t=n)||(t=c),i=t.parentWindow||t.defaultView||s;var a,o=C.a.Db(e).toLowerCase(),r=t.createElement("div");for(a=(o=o.match(/^(?:\x3c!--.*?--\x3e\s*?)*?<([a-z]+)[\s>]/))&&h[o[1]]||g,o=a[0],a="ignored<div>"+a[1]+e+a[2]+"</div>","function"==typeof i.innerShiv?r.appendChild(i.innerShiv(a)):(_&&t.body.appendChild(r),r.innerHTML=a,_&&r.parentNode.removeChild(r));o--;)r=r.lastChild;t=C.a.la(r.lastChild.childNodes)}return t},C.a.Md=function(e,n){var t=C.a.ua(e,n);return t.length&&t[0].parentElement||C.a.Yb(t)},C.a.fc=function(e,n){if(C.a.Tb(e),null!==(n=C.a.f(n))&&n!==r)if("string"!=typeof n&&(n=n.toString()),l)l(e).html(n);else for(var t=C.a.ua(n,e.ownerDocument),i=0;i<t.length;i++)e.appendChild(t[i])},C.b("utils.parseHtmlFragment",C.a.ua),C.b("utils.setHtml",C.a.fc),C.aa=function(){var e={};return{Xb:function(n){if("function"!=typeof n)throw Error("You can only pass a function to ko.memoization.memoize()");var t=(4294967296*(1+Math.random())|0).toString(16).substring(1)+(4294967296*(1+Math.random())|0).toString(16).substring(1);return e[t]=n,"\x3c!--[ko_memo:"+t+"]--\x3e"},bd:function(n,t){var i=e[n];if(i===r)throw Error("Couldn't find any memo with ID "+n+". Perhaps it's already been unmemoized.");try{return i.apply(null,t||[]),!0}finally{delete e[n]}},cd:function(e,n){var t=[];!function s(e,n){if(e)if(8==e.nodeType)null!=(t=C.aa.Uc(e.nodeValue))&&n.push({ud:e,Kd:t});else if(1==e.nodeType)for(var t=0,i=e.childNodes,a=i.length;t<a;t++)s(i[t],n)}(e,t);for(var i=0,a=t.length;i<a;i++){var o=t[i].ud,r=[o];n&&C.a.Nb(r,n),C.aa.bd(t[i].Kd,r),o.nodeValue="",o.parentNode&&o.parentNode.removeChild(o)}},Uc:function(e){return(e=e.match(/^\[ko_memo\:(.*?)\]$/))?e[1]:null}}}(),C.b("memoization",C.aa),C.b("memoization.memoize",C.aa.Xb),C.b("memoization.unmemoize",C.aa.bd),C.b("memoization.parseMemoText",C.aa.Uc),C.b("memoization.unmemoizeDomNodeAndDescendants",C.aa.cd),C.na=function(){function e(){if(i)for(var e,n=i,a=0;o<i;)if(e=t[o++]){if(o>n){if(5e3<=++a){o=i,C.a.Gc(Error("'Too much recursion' after processing "+a+" task groups."));break}n=i}try{e()}catch(r){C.a.Gc(r)}}}function n(){e(),o=i=t.length=0}var t=[],i=0,a=1,o=0;return{scheduler:s.MutationObserver?function(e){var n=c.createElement("div");return new MutationObserver(e).observe(n,{attributes:!0}),function(){n.classList.toggle("foo")}}(n):c&&"onreadystatechange"in c.createElement("script")?function(e){var n=c.createElement("script");n.onreadystatechange=function(){n.onreadystatechange=null,c.documentElement.removeChild(n),n=null,e()},c.documentElement.appendChild(n)}:function(e){setTimeout(e,0)},zb:function(e){return i||C.na.scheduler(n),t[i++]=e,a++},cancel:function(e){(e-=a-i)>=o&&e<i&&(t[e]=null)},resetForTesting:function(){var e=i-o;return o=i=t.length=0,e},Sd:e}}(),C.b("tasks",C.na),C.b("tasks.schedule",C.na.zb),C.b("tasks.runEarly",C.na.Sd),C.Ta={throttle:function(e,n){e.throttleEvaluation=n;var t=null;return C.$({read:e,write:function(i){clearTimeout(t),t=C.a.setTimeout((function(){e(i)}),n)}})},rateLimit:function(e,n){var t,o,r;"number"==typeof n?t=n:(t=n.timeout,o=n.method),e.Hb=!1,r="function"==typeof o?o:"notifyWhenChangesStop"==o?a:i,e.ub((function(e){return r(e,t,n)}))},deferred:function(e,n){if(!0!==n)throw Error("The 'deferred' extender only accepts the value 'true', because it is not supported to turn deferral off once enabled.");e.Hb||(e.Hb=!0,e.ub((function(n){var t,i=!1;return function(){if(!i){C.na.cancel(t),t=C.na.zb(n);try{i=!0,e.notifySubscribers(r,"dirty")}finally{i=!1}}}})))},notify:function(e,n){e.equalityComparer="always"==n?null:t}};var S={undefined:1,boolean:1,number:1,string:1};C.b("extenders",C.Ta),C.ic=function(e,n,t){this.da=e,this.lc=n,this.mc=t,this.Ib=!1,this.fb=this.Jb=null,C.L(this,"dispose",this.s),C.L(this,"disposeWhenNodeIsRemoved",this.l)},C.ic.prototype.s=function(){this.Ib||(this.fb&&C.a.K.yb(this.Jb,this.fb),this.Ib=!0,this.mc(),this.da=this.lc=this.mc=this.Jb=this.fb=null)},C.ic.prototype.l=function(e){this.Jb=e,C.a.K.za(e,this.fb=this.s.bind(this))},C.T=function(){C.a.Ab(this,x),x.qb(this)};var x={qb:function(e){e.U={change:[]},e.sc=1},subscribe:function(e,n,t){var i=this;t=t||"change";var a=new C.ic(i,n?e.bind(n):e,(function(){C.a.Pa(i.U[t],a),i.hb&&i.hb(t)}));return i.Qa&&i.Qa(t),i.U[t]||(i.U[t]=[]),i.U[t].push(a),a},notifySubscribers:function(e,n){if("change"===(n=n||"change")&&this.Gb(),this.Wa(n)){var t="change"===n&&this.ed||this.U[n].slice(0);try{C.u.xc();for(var i,a=0;i=t[a];++a)i.Ib||i.lc(e)}finally{C.u.end()}}},ob:function(){return this.sc},Dd:function(e){return this.ob()!==e},Gb:function(){++this.sc},ub:function(e){var n,t,i,a,r,s=this,c=C.O(s);s.gb||(s.gb=s.notifySubscribers,s.notifySubscribers=o);var d=e((function(){s.Ja=!1,c&&a===s&&(a=s.nc?s.nc():s());var e=t||r&&s.sb(i,a);r=t=n=!1,e&&s.gb(i=a)}));s.qc=function(e,t){t&&s.Ja||(r=!t),s.ed=s.U.change.slice(0),s.Ja=n=!0,a=e,d()},s.pc=function(e){n||(i=e,s.gb(e,"beforeChange"))},s.rc=function(){r=!0},s.gd=function(){s.sb(i,s.v(!0))&&(t=!0)}},Wa:function(e){return this.U[e]&&this.U[e].length},Bd:function(e){if(e)return this.U[e]&&this.U[e].length||0;var n=0;return C.a.P(this.U,(function(e,t){"dirty"!==e&&(n+=t.length)})),n},sb:function(e,n){return!this.equalityComparer||!this.equalityComparer(e,n)},toString:function(){return"[object Object]"},extend:function(e){var n=this;return e&&C.a.P(e,(function(e,t){var i=C.Ta[e];"function"==typeof i&&(n=i(n,t)||n)})),n}};C.L(x,"init",x.qb),C.L(x,"subscribe",x.subscribe),C.L(x,"extend",x.extend),C.L(x,"getSubscriptionsCount",x.Bd),C.a.Ba&&C.a.setPrototypeOf(x,Function.prototype),C.T.fn=x,C.Qc=function(e){return null!=e&&"function"==typeof e.subscribe&&"function"==typeof e.notifySubscribers},C.b("subscribable",C.T),C.b("isSubscribable",C.Qc),C.S=C.u=function(){function e(e){i.push(t),t=e}function n(){t=i.pop()}var t,i=[],a=0;return{xc:e,end:n,cc:function(e){if(t){if(!C.Qc(e))throw Error("Only subscribable things can act as dependencies");t.od.call(t.pd,e,e.fd||(e.fd=++a))}},G:function(t,i,a){try{return e(),t.apply(i,a||[])}finally{n()}},qa:function(){if(t)return t.o.qa()},Va:function(){if(t)return t.o.Va()},Ya:function(){if(t)return t.Ya},o:function(){if(t)return t.o}}}(),C.b("computedContext",C.S),C.b("computedContext.getDependenciesCount",C.S.qa),C.b("computedContext.getDependencies",C.S.Va),C.b("computedContext.isInitial",C.S.Ya),C.b("computedContext.registerDependency",C.S.cc),C.b("ignoreDependencies",C.Yd=C.u.G);var w=C.a.Da("_latestValue");C.ta=function(e){function n(){return 0<arguments.length?(n.sb(n[w],arguments[0])&&(n.ya(),n[w]=arguments[0],n.xa()),this):(C.u.cc(n),n[w])}return n[w]=e,C.a.Ba||C.a.extend(n,C.T.fn),C.T.fn.qb(n),C.a.Ab(n,y),C.options.deferUpdates&&C.Ta.deferred(n,!0),n};var y={equalityComparer:t,v:function(){return this[w]},xa:function(){this.notifySubscribers(this[w],"spectate"),this.notifySubscribers(this[w])},ya:function(){this.notifySubscribers(this[w],"beforeChange")}};C.a.Ba&&C.a.setPrototypeOf(y,C.T.fn);var k=C.ta.Ma="__ko_proto__";y[k]=C.ta,C.O=function(e){if((e="function"==typeof e&&e[k])&&e!==y[k]&&e!==C.o.fn[k])throw Error("Invalid object that looks like an observable; possibly from another Knockout instance");return!!e},C.Za=function(e){return"function"==typeof e&&(e[k]===y[k]||e[k]===C.o.fn[k]&&e.Nc)},C.b("observable",C.ta),C.b("isObservable",C.O),C.b("isWriteableObservable",C.Za),C.b("isWritableObservable",C.Za),C.b("observable.fn",y),C.L(y,"peek",y.v),C.L(y,"valueHasMutated",y.xa),C.L(y,"valueWillMutate",y.ya),C.Ha=function(e){if("object"!=typeof(e=e||[])||!("length"in e))throw Error("The argument passed when initializing an observable array must be an array, or null, or undefined.");return e=C.ta(e),C.a.Ab(e,C.Ha.fn),e.extend({trackArrayChanges:!0})},C.Ha.fn={remove:function(e){for(var n=this.v(),t=[],i="function"!=typeof e||C.O(e)?function(n){return n===e}:e,a=0;a<n.length;a++){var o=n[a];if(i(o)){if(0===t.length&&this.ya(),n[a]!==o)throw Error("Array modified during remove; cannot remove item");t.push(o),n.splice(a,1),a--}}return t.length&&this.xa(),t},removeAll:function(e){if(e===r){var n=this.v(),t=n.slice(0);return this.ya(),n.splice(0,n.length),this.xa(),t}return e?this.remove((function(n){return 0<=C.a.A(e,n)})):[]},destroy:function(e){var n=this.v(),t="function"!=typeof e||C.O(e)?function(n){return n===e}:e;this.ya();for(var i=n.length-1;0<=i;i--){var a=n[i];t(a)&&(a._destroy=!0)}this.xa()},destroyAll:function(e){return e===r?this.destroy((function(){return!0})):e?this.destroy((function(n){return 0<=C.a.A(e,n)})):[]},indexOf:function(e){var n=this();return C.a.A(n,e)},replace:function(e,n){var t=this.indexOf(e);0<=t&&(this.ya(),this.v()[t]=n,this.xa())},sorted:function(e){var n=this().slice(0);return e?n.sort(e):n.sort()},reversed:function(){return this().slice(0).reverse()}},C.a.Ba&&C.a.setPrototypeOf(C.Ha.fn,C.ta.fn),C.a.D("pop push reverse shift sort splice unshift".split(" "),(function(e){C.Ha.fn[e]=function(){var n=this.v();this.ya(),this.zc(n,e,arguments);var t=n[e].apply(n,arguments);return this.xa(),t===n?this:t}})),C.a.D(["slice"],(function(e){C.Ha.fn[e]=function(){var n=this();return n[e].apply(n,arguments)}})),C.Pc=function(e){return C.O(e)&&"function"==typeof e.remove&&"function"==typeof e.push},C.b("observableArray",C.Ha),C.b("isObservableArray",C.Pc),C.Ta.trackArrayChanges=function(e,n){function t(){function n(){if(d){var n,t=[].concat(e.v()||[]);e.Wa("arrayChange")&&((!c||1<d)&&(c=C.a.Pb(o,t,e.Ob)),n=c),o=t,c=null,d=0,n&&n.length&&e.notifySubscribers(n,"arrayChange")}}s?n():(s=!0,a=e.subscribe((function(){++d}),null,"spectate"),o=[].concat(e.v()||[]),c=null,i=e.subscribe(n))}if(e.Ob={},n&&"object"==typeof n&&C.a.extend(e.Ob,n),e.Ob.sparse=!0,!e.zc){var i,a,o,s=!1,c=null,d=0,l=e.Qa,u=e.hb;e.Qa=function(n){l&&l.call(e,n),"arrayChange"===n&&t()},e.hb=function(n){u&&u.call(e,n),"arrayChange"!==n||e.Wa("arrayChange")||(i&&i.s(),a&&a.s(),a=i=null,s=!1,o=r)},e.zc=function(e,n,t){function i(e,n,t){return a[a.length]={status:e,value:n,index:t}}if(s&&!d){var a=[],o=e.length,r=t.length,l=0;switch(n){case"push":l=o;case"unshift":for(n=0;n<r;n++)i("added",t[n],l+n);break;case"pop":l=o-1;case"shift":o&&i("deleted",e[l],l);break;case"splice":n=Math.min(Math.max(0,0>t[0]?o+t[0]:t[0]),o),o=1===r?o:Math.min(n+(t[1]||0),o),r=n+r-2,l=Math.max(o,r);for(var u=[],p=[],f=2;n<l;++n,++f)n<o&&p.push(i("deleted",e[n],n)),n<r&&u.push(i("added",t[f],n));C.a.Kc(p,u);break;default:return}c=a}}}};var P=C.a.Da("_state");C.o=C.$=function(e,n,t){function i(){if(0<arguments.length){if("function"!=typeof a)throw Error("Cannot write a value to a ko.computed unless you specify a 'write' option. If you wish to read the current value, don't pass any parameters.");return a.apply(o.nb,arguments),this}return o.ra||C.u.cc(i),(o.ka||o.J&&i.Xa())&&i.ha(),o.X}if("object"==typeof e?t=e:(t=t||{},e&&(t.read=e)),"function"!=typeof t.read)throw Error("Pass a function that returns the value of the ko.computed");var a=t.write,o={X:r,sa:!0,ka:!0,rb:!1,jc:!1,ra:!1,wb:!1,J:!1,Wc:t.read,nb:n||t.owner,l:t.disposeWhenNodeIsRemoved||t.l||null,Sa:t.disposeWhen||t.Sa,Rb:null,I:{},V:0,Ic:null};return i[P]=o,i.Nc="function"==typeof a,C.a.Ba||C.a.extend(i,C.T.fn),C.T.fn.qb(i),C.a.Ab(i,T),t.pure?(o.wb=!0,o.J=!0,C.a.extend(i,D)):t.deferEvaluation&&C.a.extend(i,E),C.options.deferUpdates&&C.Ta.deferred(i,!0),o.l&&(o.jc=!0,o.l.nodeType||(o.l=null)),o.J||t.deferEvaluation||i.ha(),o.l&&i.ja()&&C.a.K.za(o.l,o.Rb=function(){i.s()}),i};var T={equalityComparer:t,qa:function(){return this[P].V},Va:function(){var e=[];return C.a.P(this[P].I,(function(n,t){e[t.Ka]=t.da})),e},Vb:function(e){if(!this[P].V)return!1;var n=this.Va();return-1!==C.a.A(n,e)||!!C.a.Lb(n,(function(n){return n.Vb&&n.Vb(e)}))},uc:function(e,n,t){if(this[P].wb&&n===this)throw Error("A 'pure' computed must not be called recursively");this[P].I[e]=t,t.Ka=this[P].V++,t.La=n.ob()},Xa:function(){var e,n,t=this[P].I;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&(n=t[e],this.Ia&&n.da.Ja||n.da.Dd(n.La)))return!0},Jd:function(){this.Ia&&!this[P].rb&&this.Ia(!1)},ja:function(){var e=this[P];return e.ka||0<e.V},Rd:function(){this.Ja?this[P].ka&&(this[P].sa=!0):this.Hc()},$c:function(e){if(e.Hb){var n=e.subscribe(this.Jd,this,"dirty"),t=e.subscribe(this.Rd,this);return{da:e,s:function(){n.s(),t.s()}}}return e.subscribe(this.Hc,this)},Hc:function(){var e=this,n=e.throttleEvaluation;n&&0<=n?(clearTimeout(this[P].Ic),this[P].Ic=C.a.setTimeout((function(){e.ha(!0)}),n)):e.Ia?e.Ia(!0):e.ha(!0)},ha:function(e){var n=this[P],t=n.Sa,i=!1;if(!n.rb&&!n.ra){if(n.l&&!C.a.Sb(n.l)||t&&t()){if(!n.jc)return void this.s()}else n.jc=!1;n.rb=!0;try{i=this.zd(e)}finally{n.rb=!1}return i}},zd:function(e){var n=this[P],t=!1,i=n.wb?r:!n.V;t={qd:this,mb:n.I,Qb:n.V},C.u.xc({pd:t,od:f,o:this,Ya:i}),n.I={},n.V=0;var a=this.yd(n,t);return n.V?t=this.sb(n.X,a):(this.s(),t=!0),t&&(n.J?this.Gb():this.notifySubscribers(n.X,"beforeChange"),n.X=a,this.notifySubscribers(n.X,"spectate"),!n.J&&e&&this.notifySubscribers(n.X),this.rc&&this.rc()),i&&this.notifySubscribers(n.X,"awake"),t},yd:function(e,n){try{var t=e.Wc;return e.nb?t.call(e.nb):t()}finally{C.u.end(),n.Qb&&!e.J&&C.a.P(n.mb,p),e.sa=e.ka=!1}},v:function(e){var n=this[P];return(n.ka&&(e||!n.V)||n.J&&this.Xa())&&this.ha(),n.X},ub:function(e){C.T.fn.ub.call(this,e),this.nc=function(){return this[P].J||(this[P].sa?this.ha():this[P].ka=!1),this[P].X},this.Ia=function(e){this.pc(this[P].X),this[P].ka=!0,e&&(this[P].sa=!0),this.qc(this,!e)}},s:function(){var e=this[P];!e.J&&e.I&&C.a.P(e.I,(function(e,n){n.s&&n.s()})),e.l&&e.Rb&&C.a.K.yb(e.l,e.Rb),e.I=r,e.V=0,e.ra=!0,e.sa=!1,e.ka=!1,e.J=!1,e.l=r,e.Sa=r,e.Wc=r,this.Nc||(e.nb=r)}},D={Qa:function(e){var n=this,t=n[P];if(!t.ra&&t.J&&"change"==e){if(t.J=!1,t.sa||n.Xa())t.I=null,t.V=0,n.ha()&&n.Gb();else{var i=[];C.a.P(t.I,(function(e,n){i[n.Ka]=e})),C.a.D(i,(function(e,i){var a=t.I[e],o=n.$c(a.da);o.Ka=i,o.La=a.La,t.I[e]=o})),n.Xa()&&n.ha()&&n.Gb()}t.ra||n.notifySubscribers(t.X,"awake")}},hb:function(e){var n=this[P];n.ra||"change"!=e||this.Wa("change")||(C.a.P(n.I,(function(e,t){t.s&&(n.I[e]={da:t.da,Ka:t.Ka,La:t.La},t.s())})),n.J=!0,this.notifySubscribers(r,"asleep"))},ob:function(){var e=this[P];return e.J&&(e.sa||this.Xa())&&this.ha(),C.T.fn.ob.call(this)}},E={Qa:function(e){"change"!=e&&"beforeChange"!=e||this.v()}};C.a.Ba&&C.a.setPrototypeOf(T,C.T.fn);var I=C.ta.Ma;T[I]=C.o,C.Oc=function(e){return"function"==typeof e&&e[I]===T[I]},C.Fd=function(e){return C.Oc(e)&&e[P]&&e[P].wb},C.b("computed",C.o),C.b("dependentObservable",C.o),C.b("isComputed",C.Oc),C.b("isPureComputed",C.Fd),C.b("computed.fn",T),C.L(T,"peek",T.v),C.L(T,"dispose",T.s),C.L(T,"isActive",T.ja),C.L(T,"getDependenciesCount",T.qa),C.L(T,"getDependencies",T.Va),C.xb=function(e,n){return"function"==typeof e?C.o(e,n,{pure:!0}):((e=C.a.extend({},e)).pure=!0,C.o(e,n))},C.b("pureComputed",C.xb),function(){function e(t,i,a){if(a=a||new n,"object"!=typeof(t=i(t))||null===t||t===r||t instanceof RegExp||t instanceof Date||t instanceof String||t instanceof Number||t instanceof Boolean)return t;var o=t instanceof Array?[]:{};return a.save(t,o),function(e,n){if(e instanceof Array){for(var t=0;t<e.length;t++)n(t);"function"==typeof e.toJSON&&n("toJSON")}else for(t in e)n(t)}(t,(function(n){var s=i(t[n]);switch(typeof s){case"boolean":case"number":case"string":case"function":o[n]=s;break;case"object":case"undefined":var c=a.get(s);o[n]=c!==r?c:e(s,i,a)}})),o}function n(){this.keys=[],this.values=[]}C.ad=function(n){if(0==arguments.length)throw Error("When calling ko.toJS, pass the object you want to convert.");return e(n,(function(e){for(var n=0;C.O(e)&&10>n;n++)e=e();return e}))},C.toJSON=function(e,n,t){return e=C.ad(e),C.a.hc(e,n,t)},n.prototype={constructor:n,save:function(e,n){var t=C.a.A(this.keys,e);0<=t?this.values[t]=n:(this.keys.push(e),this.values.push(n))},get:function(e){return 0<=(e=C.a.A(this.keys,e))?this.values[e]:r}}}(),C.b("toJS",C.ad),C.b("toJSON",C.toJSON),C.Wd=function(e,n,t){function i(n){var i=C.xb(e,t).extend({ma:"always"}),a=i.subscribe((function(e){e&&(a.s(),n(e))}));return i.notifySubscribers(i.v()),a}return"function"!=typeof Promise||n?i(n.bind(t)):new Promise(i)},C.b("when",C.Wd),C.w={M:function(e){switch(C.a.R(e)){case"option":return!0===e.__ko__hasDomDataOptionValue__?C.a.g.get(e,C.c.options.$b):7>=C.a.W?e.getAttributeNode("value")&&e.getAttributeNode("value").specified?e.value:e.text:e.value;case"select":return 0<=e.selectedIndex?C.w.M(e.options[e.selectedIndex]):r;default:return e.value}},cb:function(e,n,t){switch(C.a.R(e)){case"option":"string"==typeof n?(C.a.g.set(e,C.c.options.$b,r),"__ko__hasDomDataOptionValue__"in e&&delete e.__ko__hasDomDataOptionValue__,e.value=n):(C.a.g.set(e,C.c.options.$b,n),e.__ko__hasDomDataOptionValue__=!0,e.value="number"==typeof n?n:"");break;case"select":""!==n&&null!==n||(n=r);for(var i,a=-1,o=0,s=e.options.length;o<s;++o)if((i=C.w.M(e.options[o]))==n||""===i&&n===r){a=o;break}(t||0<=a||n===r&&1<e.size)&&(e.selectedIndex=a,6===C.a.W&&C.a.setTimeout((function(){e.selectedIndex=a}),0));break;default:null!==n&&n!==r||(n=""),e.value=n}}},C.b("selectExtensions",C.w),C.b("selectExtensions.readValue",C.w.M),C.b("selectExtensions.writeValue",C.w.cb),C.m=function(){function e(e){123===(e=C.a.Db(e)).charCodeAt(0)&&(e=e.slice(1,-1));var n,t=[],r=(e+="\n,").match(i),s=[],c=0;if(1<r.length){for(var d,l=0;d=r[l];++l){var u=d.charCodeAt(0);if(44===u){if(0>=c){t.push(n&&s.length?{key:n,value:s.join("")}:{unknown:n||s.join("")}),n=c=0,s=[];continue}}else if(58===u){if(!c&&!n&&1===s.length){n=s.pop();continue}}else{if(47===u&&1<d.length&&(47===d.charCodeAt(1)||42===d.charCodeAt(1)))continue;47===u&&l&&1<d.length?(u=r[l-1].match(a))&&!o[u[0]]&&(r=(e=e.substr(e.indexOf(d)+1)).match(i),l=-1,d="/"):40===u||123===u||91===u?++c:41===u||125===u||93===u?--c:n||s.length||34!==u&&39!==u||(d=d.slice(1,-1))}s.push(d)}if(0<c)throw Error("Unbalanced parentheses, braces, or brackets")}return t}var n=["true","false","null","undefined"],t=/^(?:[$_a-z][$\w]*|(.+)(\.\s*[$_a-z][$\w]*|\[.+\]))$/i,i=RegExp("\"(?:\\\\.|[^\"])*\"|'(?:\\\\.|[^'])*'|`(?:\\\\.|[^`])*`|/\\*(?:[^*]|\\*+[^*/])*\\*+/|//.*\n|/(?:\\\\.|[^/])+/w*|[^\\s:,/][^,\"'`{}()/:[\\]]*[^\\s,\"'`{}()/:[\\]]|[^\\s]","g"),a=/[\])"'A-Za-z0-9_$]+$/,o={"in":1,"return":1,"typeof":1},r={};return{Ra:[],wa:r,ac:e,vb:function(i,a){function o(e,i){var a;if(!l){var u=C.getBindingHandler(e);if(u&&u.preprocess&&!(i=u.preprocess(i,e,o)))return;(u=r[e])&&(a=i,0<=C.a.A(n,a)?a=!1:(u=a.match(t),a=null!==u&&(u[1]?"Object("+u[1]+")"+u[2]:a)),u=a),u&&c.push("'"+("string"==typeof r[e]?r[e]:e)+"':function(_z){"+a+"=_z}")}d&&(i="function(){return "+i+" }"),s.push("'"+e+"':"+i)}var s=[],c=[],d=(a=a||{}).valueAccessors,l=a.bindingParams,u="string"==typeof i?e(i):i;return C.a.D(u,(function(e){o(e.key||e.unknown,e.value)})),c.length&&o("_ko_property_writers","{"+c.join(",")+" }"),s.join(",")},Id:function(e,n){for(var t=0;t<e.length;t++)if(e[t].key==n)return!0;return!1},eb:function(e,n,t,i,a){e&&C.O(e)?!C.Za(e)||a&&e.v()===i||e(i):(e=n.get("_ko_property_writers"))&&e[t]&&e[t](i)}}}(),C.b("expressionRewriting",C.m),C.b("expressionRewriting.bindingRewriteValidators",C.m.Ra),C.b("expressionRewriting.parseObjectLiteral",C.m.ac),C.b("expressionRewriting.preProcessBindings",C.m.vb),C.b("expressionRewriting._twoWayBindings",C.m.wa),C.b("jsonExpressionRewriting",C.m),C.b("jsonExpressionRewriting.insertPropertyAccessorsIntoJson",C.m.vb),function(){function e(e){return 8==e.nodeType&&o.test(a?e.text:e.nodeValue)}function n(e){return 8==e.nodeType&&r.test(a?e.text:e.nodeValue)}function t(t,i){for(var a=t,o=1,r=[];a=a.nextSibling;){if(n(a)&&(C.a.g.set(a,d,!0),0==--o))return r;r.push(a),e(a)&&o++}if(!i)throw Error("Cannot find closing comment tag to match: "+t.nodeValue);return null}function i(e,n){var i=t(e,n);return i?0<i.length?i[i.length-1].nextSibling:e.nextSibling:null}var a=c&&"\x3c!--test--\x3e"===c.createComment("test").text,o=a?/^\x3c!--\s*ko(?:\s+([\s\S]+))?\s*--\x3e$/:/^\s*ko(?:\s+([\s\S]+))?\s*$/,r=a?/^\x3c!--\s*\/ko\s*--\x3e$/:/^\s*\/ko\s*$/,s={ul:!0,ol:!0},d="__ko_matchedEndComment__";C.h={ea:{},childNodes:function(n){return e(n)?t(n):n.childNodes},Ea:function(n){if(e(n))for(var t=0,i=(n=C.h.childNodes(n)).length;t<i;t++)C.removeNode(n[t]);else C.a.Tb(n)},va:function(n,t){if(e(n)){C.h.Ea(n);for(var i=n.nextSibling,a=0,o=t.length;a<o;a++)i.parentNode.insertBefore(t[a],i)}else C.a.va(n,t)},Vc:function(n,t){var i;e(n)?(i=n.nextSibling,n=n.parentNode):i=n.firstChild,i?t!==i&&n.insertBefore(t,i):n.appendChild(t)},Wb:function(n,t,i){i?(i=i.nextSibling,e(n)&&(n=n.parentNode),i?t!==i&&n.insertBefore(t,i):n.appendChild(t)):C.h.Vc(n,t)},firstChild:function(t){if(e(t))return!t.nextSibling||n(t.nextSibling)?null:t.nextSibling;if(t.firstChild&&n(t.firstChild))throw Error("Found invalid end comment, as the first child of "+t);return t.firstChild},nextSibling:function(t){if(e(t)&&(t=i(t)),t.nextSibling&&n(t.nextSibling)){var a=t.nextSibling;if(n(a)&&!C.a.g.get(a,d))throw Error("Found end comment without a matching opening comment, as child of "+t);return null}return t.nextSibling},Cd:e,Vd:function(e){return(e=(a?e.text:e.nodeValue).match(o))?e[1]:null},Sc:function(t){if(s[C.a.R(t)]){var a=t.firstChild;if(a)do{if(1===a.nodeType){var o,r=null;if(o=a.firstChild)do{if(r)r.push(o);else if(e(o)){var c=i(o,!0);c?o=c:r=[o]}else n(o)&&(r=[o])}while(o=o.nextSibling);if(o=r)for(r=a.nextSibling,c=0;c<o.length;c++)r?t.insertBefore(o[c],r):t.appendChild(o[c])}}while(a=a.nextSibling)}}}}(),C.b("virtualElements",C.h),C.b("virtualElements.allowedBindings",C.h.ea),C.b("virtualElements.emptyNode",C.h.Ea),C.b("virtualElements.insertAfter",C.h.Wb),C.b("virtualElements.prepend",C.h.Vc),C.b("virtualElements.setDomNodeChildren",C.h.va),C.ga=function(){this.nd={}},C.a.extend(C.ga.prototype,{nodeHasBindings:function(e){switch(e.nodeType){case 1:return null!=e.getAttribute("data-bind")||C.j.getComponentNameForNode(e);case 8:return C.h.Cd(e);default:return!1}},getBindings:function(e,n){var t=(t=this.getBindingsString(e,n))?this.parseBindingsString(t,n,e):null;return C.j.tc(t,e,n,!1)},getBindingAccessors:function(e,n){var t=(t=this.getBindingsString(e,n))?this.parseBindingsString(t,n,e,{valueAccessors:!0}):null;return C.j.tc(t,e,n,!0)},getBindingsString:function(e){switch(e.nodeType){case 1:return e.getAttribute("data-bind");case 8:return C.h.Vd(e);default:return null}},parseBindingsString:function(e,n,t,i){try{var a,o=this.nd,r=e+(i&&i.valueAccessors||"");if(!(a=o[r])){var s,c="with($context){with($data||{}){return{"+C.m.vb(e,i)+"}}}";s=new Function("$context","$element",c),a=o[r]=s}return a(n,t)}catch(d){throw d.message="Unable to parse bindings.\nBindings value: "+e+"\nMessage: "+d.message,d}}}),C.ga.instance=new C.ga,C.b("bindingProvider",C.ga),function(){function e(e){var n=(e=C.a.g.get(e,S))&&e.N;n&&(e.N=null,n.Tc())}function n(n,t,i){this.node=n,this.yc=t,this.kb=[],this.H=!1,t.N||C.a.K.za(n,e),i&&i.N&&(i.N.kb.push(n),this.Kb=i)}function t(e){return function(){return e}}function i(e){return e()}function a(e){return C.a.Ga(C.u.G(e),(function(n,t){return function(){return e()[t]}}))}function o(e,n,i){return"function"==typeof e?a(e.bind(null,n,i)):C.a.Ga(e,t)}function d(e,n){return a(this.getBindings.bind(this,e,n))}function u(e,n){var t=C.h.firstChild(n);if(t){var i,a=C.ga.instance,o=a.preprocessNode;if(o){for(;i=t;)t=C.h.nextSibling(i),o.call(a,i);t=C.h.firstChild(n)}for(;i=t;)t=C.h.nextSibling(i),p(e,i)}C.i.ma(n,C.i.H)}function p(e,n){var t=e,i=1===n.nodeType;i&&C.h.Sc(n),(i||C.ga.instance.nodeHasBindings(n))&&(t=f(n,null,e).bindingContextForDescendants),t&&!h[C.a.R(n)]&&u(t,n)}function f(e,n,t){var a,o=C.a.g.Ub(e,S,{}),s=o.hd;if(!n){if(s)throw Error("You cannot apply bindings multiple times to the same element.");o.hd=!0}if(s||(o.context=t),o.Zb||(o.Zb={}),n&&"function"!=typeof n)a=n;else{var c=C.ga.instance,l=c.getBindingAccessors||d,u=C.$((function(){return(a=n?n(t,e):l.call(c,e,t))&&(t[m]&&t[m](),t[v]&&t[v]()),a}),null,{l:e});a&&u.ja()||(u=null)}var p,f=t;if(a){var g=function(){return C.a.Ga(u?u():a,i)},b=u?function(e){return function(){return i(u()[e])}}:function(e){return a[e]};g.get=function(e){return a[e]&&i(b(e))},g.has=function(e){return e in a},C.i.H in a&&C.i.subscribe(e,C.i.H,(function(){var n=(0,a[C.i.H])();if(n){var t=C.h.childNodes(e);t.length&&n(t,C.Ec(t[0]))}})),C.i.pa in a&&(f=C.i.Cb(e,t),C.i.subscribe(e,C.i.pa,(function(){var n=(0,a[C.i.pa])();n&&C.h.firstChild(e)&&n(e)}))),o=function(e){var n=[],t={},i=[];return C.a.P(e,(function a(o){if(!t[o]){var r=C.getBindingHandler(o);r&&(r.after&&(i.push(o),C.a.D(r.after,(function(n){if(e[n]){if(-1!==C.a.A(i,n))throw Error("Cannot combine the following bindings, because they have a cyclic dependency: "+i.join(", "));a(n)}})),i.length--),n.push({key:o,Mc:r})),t[o]=!0}})),n}(a),C.a.D(o,(function(n){var t=n.Mc.init,i=n.Mc.update,o=n.key;if(8===e.nodeType&&!C.h.ea[o])throw Error("The binding '"+o+"' cannot be used with virtual elements");try{"function"==typeof t&&C.u.G((function(){var n=t(e,b(o),g,f.$data,f);if(n&&n.controlsDescendantBindings){if(p!==r)throw Error("Multiple bindings ("+p+" and "+o+") are trying to control descendant bindings of the same element. You cannot use these bindings together on the same element.");p=o}})),"function"==typeof i&&C.$((function(){i(e,b(o),g,f.$data,f)}),null,{l:e})}catch(s){throw s.message='Unable to process binding "'+o+": "+a[o]+'"\nMessage: '+s.message,s}}))}return{shouldBindDescendants:o=p===r,bindingContextForDescendants:o&&f}}function g(e,n){return e&&e instanceof C.fa?e:new C.fa(e,r,r,n)}var m=C.a.Da("_subscribable"),b=C.a.Da("_ancestorBindingInfo"),v=C.a.Da("_dataDependency");C.c={};var h={script:!0,textarea:!0,template:!0};C.getBindingHandler=function(e){return C.c[e]};var _={};C.fa=function(e,n,t,i,a){function o(){var e=u?l():l,a=C.a.f(e);return n?(C.a.extend(c,n),b in n&&(c[b]=n[b])):(c.$parents=[],c.$root=a,c.ko=C),c[m]=s,d?a=c.$data:(c.$rawData=e,c.$data=a),t&&(c[t]=a),i&&i(c,n,a),n&&n[m]&&!C.S.o().Vb(n[m])&&n[m](),p&&(c[v]=p),c.$data}var s,c=this,d=e===_,l=d?r:e,u="function"==typeof l&&!C.O(l),p=a&&a.dataDependency;a&&a.exportDependencies?o():((s=C.xb(o)).v(),s.ja()?s.equalityComparer=null:c[m]=r)},C.fa.prototype.createChildContext=function(e,n,t,i){if(!i&&n&&"object"==typeof n&&(n=(i=n).as,t=i.extend),n&&i&&i.noChildContext){var a="function"==typeof e&&!C.O(e);return new C.fa(_,this,null,(function(i){t&&t(i),i[n]=a?e():e}),i)}return new C.fa(e,this,n,(function(e,n){e.$parentContext=n,e.$parent=n.$data,e.$parents=(n.$parents||[]).slice(0),e.$parents.unshift(e.$parent),t&&t(e)}),i)},C.fa.prototype.extend=function(e,n){return new C.fa(_,this,null,(function(n){C.a.extend(n,"function"==typeof e?e(n):e)}),n)};var S=C.a.g.Z();n.prototype.Tc=function(){this.Kb&&this.Kb.N&&this.Kb.N.sd(this.node)},n.prototype.sd=function(e){C.a.Pa(this.kb,e),!this.kb.length&&this.H&&this.Cc()},n.prototype.Cc=function(){this.H=!0,this.yc.N&&!this.kb.length&&(this.yc.N=null,C.a.K.yb(this.node,e),C.i.ma(this.node,C.i.pa),this.Tc())},C.i={H:"childrenComplete",pa:"descendantsComplete",subscribe:function(e,n,t,i,a){var o=C.a.g.Ub(e,S,{});return o.Fa||(o.Fa=new C.T),a&&a.notifyImmediately&&o.Zb[n]&&C.u.G(t,i,[e]),o.Fa.subscribe(t,i,n)},ma:function(e,n){var t=C.a.g.get(e,S);if(t&&(t.Zb[n]=!0,t.Fa&&t.Fa.notifySubscribers(e,n),n==C.i.H))if(t.N)t.N.Cc();else if(t.N===r&&t.Fa&&t.Fa.Wa(C.i.pa))throw Error("descendantsComplete event not supported for bindings on this node")},Cb:function(e,t){var i=C.a.g.Ub(e,S,{});return i.N||(i.N=new n(e,i,t[b])),t[b]==i?t:t.extend((function(e){e[b]=i}))}},C.Td=function(e){return(e=C.a.g.get(e,S))&&e.context},C.ib=function(e,n,t){return 1===e.nodeType&&C.h.Sc(e),f(e,n,g(t))},C.ld=function(e,n,t){return t=g(t),C.ib(e,o(n,t,e),t)},C.Oa=function(e,n){1!==n.nodeType&&8!==n.nodeType||u(g(e),n)},C.vc=function(e,n,t){if(!l&&s.jQuery&&(l=s.jQuery),2>arguments.length){if(!(n=c.body))throw Error("ko.applyBindings: could not find document.body; has the document been loaded?")}else if(!n||1!==n.nodeType&&8!==n.nodeType)throw Error("ko.applyBindings: first parameter should be your view model; second parameter should be a DOM node");p(g(e,t),n)},C.Dc=function(e){return!e||1!==e.nodeType&&8!==e.nodeType?r:C.Td(e)},C.Ec=function(e){return(e=C.Dc(e))?e.$data:r},C.b("bindingHandlers",C.c),C.b("bindingEvent",C.i),C.b("bindingEvent.subscribe",C.i.subscribe),C.b("bindingEvent.startPossiblyAsyncContentBinding",C.i.Cb),C.b("applyBindings",C.vc),C.b("applyBindingsToDescendants",C.Oa),C.b("applyBindingAccessorsToNode",C.ib),C.b("applyBindingsToNode",C.ld),C.b("contextFor",C.Dc),C.b("dataFor",C.Ec)}(),function(e){function n(n,i){var r,s=Object.prototype.hasOwnProperty.call(a,n)?a[n]:e;s?s.subscribe(i):((s=a[n]=new C.T).subscribe(i),t(n,(function(e,t){var i=!(!t||!t.synchronous);o[n]={definition:e,Gd:i},delete a[n],r||i?s.notifySubscribers(e):C.na.zb((function(){s.notifySubscribers(e)}))})),r=!0)}function t(e,n){i("getConfig",[e],(function(t){t?i("loadComponent",[e,t],(function(e){n(e,t)})):n(null,null)}))}function i(n,t,a,o){o||(o=C.j.loaders.slice(0));var r=o.shift();if(r){var s=r[n];if(s){var c=!1;if(s.apply(r,t.concat((function(e){c?a(null):null!==e?a(e):i(n,t,a,o)})))!==e&&(c=!0,!r.suppressLoaderExceptions))throw Error("Component loaders must supply values by invoking the callback, not by returning values synchronously.")}else i(n,t,a,o)}else a(null)}var a={},o={};C.j={get:function(t,i){var a=Object.prototype.hasOwnProperty.call(o,t)?o[t]:e;a?a.Gd?C.u.G((function(){i(a.definition)})):C.na.zb((function(){i(a.definition)})):n(t,i)},Bc:function(e){delete o[e]},oc:i},C.j.loaders=[],C.b("components",C.j),C.b("components.get",C.j.get),C.b("components.clearCachedDefinition",C.j.Bc)}(),function(){function e(e,n,t,i){function o(){0==--s&&i(r)}var r={},s=2,c=t.template;t=t.viewModel,c?a(n,c,(function(n){C.j.oc("loadTemplate",[e,n],(function(e){r.template=e,o()}))})):o(),t?a(n,t,(function(n){C.j.oc("loadViewModel",[e,n],(function(e){r[d]=e,o()}))})):o()}function t(e){switch(C.a.R(e)){case"script":return C.a.ua(e.text);case"textarea":return C.a.ua(e.value);case"template":if(i(e.content))return C.a.Ca(e.content.childNodes)}return C.a.Ca(e.childNodes)}function i(e){return s.DocumentFragment?e instanceof DocumentFragment:e&&11===e.nodeType}function a(e,t,i){"string"==typeof t.require?n||s.require?(n||s.require)([t.require],(function(e){e&&"object"==typeof e&&e.Xd&&e["default"]&&(e=e["default"]),i(e)})):e("Uses require, but no AMD loader is present"):i(t)}function o(e){return function(n){throw Error("Component '"+e+"': "+n)}}var r={};C.j.register=function(e,n){if(!n)throw Error("Invalid configuration for "+e);if(C.j.tb(e))throw Error("Component "+e+" is already registered");r[e]=n},C.j.tb=function(e){return Object.prototype.hasOwnProperty.call(r,e)},C.j.unregister=function(e){delete r[e],C.j.Bc(e)},C.j.Fc={getConfig:function(e,n){n(C.j.tb(e)?r[e]:null)},loadComponent:function(n,t,i){var r=o(n);a(r,t,(function(t){e(n,r,t,i)}))},loadTemplate:function(e,n,a){if(e=o(e),"string"==typeof n)a(C.a.ua(n));else if(n instanceof Array)a(n);else if(i(n))a(C.a.la(n.childNodes));else if(n.element)if(n=n.element,s.HTMLElement?n instanceof HTMLElement:n&&n.tagName&&1===n.nodeType)a(t(n));else if("string"==typeof n){var r=c.getElementById(n);r?a(t(r)):e("Cannot find element with ID "+n)}else e("Unknown element type: "+n);else e("Unknown template value: "+n)},loadViewModel:function(e,n,t){!function i(e,n,t){if("function"==typeof n)t((function(e){return new n(e)}));else if("function"==typeof n[d])t(n[d]);else if("instance"in n){var a=n.instance;t((function(){return a}))}else"viewModel"in n?i(e,n.viewModel,t):e("Unknown viewModel value: "+n)}(o(e),n,t)}};var d="createViewModel";C.b("components.register",C.j.register),C.b("components.isRegistered",C.j.tb),C.b("components.unregister",C.j.unregister),C.b("components.defaultLoader",C.j.Fc),C.j.loaders.push(C.j.Fc),C.j.dd=r}(),function(){function e(e,t){if(i=e.getAttribute("params")){var i=n.parseBindingsString(i,t,e,{valueAccessors:!0,bindingParams:!0}),a=(i=C.a.Ga(i,(function(n){return C.o(n,null,{l:e})})),C.a.Ga(i,(function(n){var t=n.v();return n.ja()?C.o({read:function(){return C.a.f(n())},write:C.Za(t)&&function(e){n()(e)},l:e}):t})));return Object.prototype.hasOwnProperty.call(a,"$raw")||(a.$raw=i),a}return{$raw:{}}}C.j.getComponentNameForNode=function(e){var n=C.a.R(e);if(C.j.tb(n)&&(-1!=n.indexOf("-")||"[object HTMLUnknownElement]"==""+e||8>=C.a.W&&e.tagName===n))return n},C.j.tc=function(n,t,i,a){if(1===t.nodeType){var o=C.j.getComponentNameForNode(t);if(o){if((n=n||{}).component)throw Error('Cannot use the "component" binding on a custom element matching a component');var r={name:o,params:e(t,i)};n.component=a?function(){return r}:r}}return n};var n=new C.ga;9>C.a.W&&(C.j.register=function(e){return function(n){return e.apply(this,arguments)}}(C.j.register),c.createDocumentFragment=function(e){return function(){var n,t=e(),i=C.j.dd;for(n in i);return t}}(c.createDocumentFragment))}(),function(){var e=0;C.c.component={init:function(n,t,i,a,o){function r(){var e=s&&s.dispose;"function"==typeof e&&e.call(s),d&&d.s(),c=s=d=null}var s,c,d,l=C.a.la(C.h.childNodes(n));return C.h.Ea(n),C.a.K.za(n,r),C.o((function(){var i,a,u=C.a.f(t());if("string"==typeof u?i=u:(i=C.a.f(u.name),a=C.a.f(u.params)),!i)throw Error("No component name specified");var p=C.i.Cb(n,o),f=c=++e;C.j.get(i,(function(e){if(c===f){if(r(),!e)throw Error("Unknown component '"+i+"'");!function(e,n,t){if(!(n=n.template))throw Error("Component '"+e+"' has no template");e=C.a.Ca(n),C.h.va(t,e)}(i,e,n);var t=function(e,n,t){var i=e.createViewModel;return i?i.call(e,n,t):n}(e,a,{element:n,templateNodes:l});e=p.createChildContext(t,{extend:function(e){e.$component=t,e.$componentTemplateNodes=l}}),t&&t.koDescendantsComplete&&(d=C.i.subscribe(n,C.i.pa,t.koDescendantsComplete,t)),s=t,C.Oa(e,n)}}))}),null,{l:n}),{controlsDescendantBindings:!0}}},C.h.ea.component=!0}();var A={"class":"className","for":"htmlFor"};C.c.attr={update:function(e,n){var t=C.a.f(n())||{};C.a.P(t,(function(n,t){t=C.a.f(t);var i=n.indexOf(":"),a=(i="lookupNamespaceURI"in e&&0<i&&e.lookupNamespaceURI(n.substr(0,i)),!1===t||null===t||t===r);a?i?e.removeAttributeNS(i,n):e.removeAttribute(n):t=t.toString(),8>=C.a.W&&n in A?(n=A[n],a?e.removeAttribute(n):e[n]=t):a||(i?e.setAttributeNS(i,n,t):e.setAttribute(n,t)),"name"===n&&C.a.Yc(e,a?"":t)}))}},C.c.checked={after:["value","attr"],init:function(e,n,t){function i(){var i=e.checked,c=a();if(!C.S.Ya()&&(i||!s&&!C.S.qa())){var u=C.u.G(n);if(d){var f=l?u.v():u,g=p;p=c,g!==c?i&&(C.a.Na(f,c,!0),C.a.Na(f,g,!1)):C.a.Na(f,c,i),l&&C.Za(u)&&u(f)}else o&&(c===r?c=i:i||(c=r)),C.m.eb(u,t,"checked",c,!0)}}var a=C.xb((function(){return t.has("checkedValue")?C.a.f(t.get("checkedValue")):u?t.has("value")?C.a.f(t.get("value")):e.value:void 0})),o="checkbox"==e.type,s="radio"==e.type;if(o||s){var c=n(),d=o&&C.a.f(c)instanceof Array,l=!(d&&c.push&&c.splice),u=s||d,p=d?a():r;s&&!e.name&&C.c.uniqueName.init(e,(function(){return!0})),C.o(i,null,{l:e}),C.a.B(e,"click",i),C.o((function(){var t=C.a.f(n()),i=a();d?(e.checked=0<=C.a.A(t,i),p=i):e.checked=o&&i===r?!!t:a()===t}),null,{l:e}),c=r}}},C.m.wa.checked=!0,C.c.checkedValue={update:function(e,n){e.value=C.a.f(n())}},C.c["class"]={update:function(e,n){var t=C.a.Db(C.a.f(n()));C.a.Eb(e,e.__ko__cssValue,!1),e.__ko__cssValue=t,C.a.Eb(e,t,!0)}},C.c.css={update:function(e,n){var t=C.a.f(n());null!==t&&"object"==typeof t?C.a.P(t,(function(n,t){t=C.a.f(t),C.a.Eb(e,n,t)})):C.c["class"].update(e,n)}},C.c.enable={update:function(e,n){var t=C.a.f(n());t&&e.disabled?e.removeAttribute("disabled"):t||e.disabled||(e.disabled=!0)}},C.c.disable={update:function(e,n){C.c.enable.update(e,(function(){return!C.a.f(n())}))}},C.c.event={init:function(e,n,t,i,a){var o=n()||{};C.a.P(o,(function(o){"string"==typeof o&&C.a.B(e,o,(function(e){var r,s=n()[o];if(s){try{var c=C.a.la(arguments);i=a.$data,c.unshift(i),r=s.apply(i,c)}finally{!0!==r&&(e.preventDefault?e.preventDefault():e.returnValue=!1)}!1===t.get(o+"Bubble")&&(e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation())}}))}))}},C.c.foreach={Rc:function(e){return function(){var n=e(),t=C.a.bc(n);return t&&"number"!=typeof t.length?(C.a.f(n),{foreach:t.data,as:t.as,noChildContext:t.noChildContext,includeDestroyed:t.includeDestroyed,afterAdd:t.afterAdd,beforeRemove:t.beforeRemove,afterRender:t.afterRender,beforeMove:t.beforeMove,afterMove:t.afterMove,templateEngine:C.ba.Ma}):{foreach:n,templateEngine:C.ba.Ma}}},init:function(e,n){return C.c.template.init(e,C.c.foreach.Rc(n))},update:function(e,n,t,i,a){return C.c.template.update(e,C.c.foreach.Rc(n),t,i,a)}},C.m.Ra.foreach=!1,C.h.ea.foreach=!0,C.c.hasfocus={init:function(e,n,t){function i(i){e.__ko_hasfocusUpdating=!0;var a=e.ownerDocument;if("activeElement"in a){var o;try{o=a.activeElement}catch(r){o=a.body}i=o===e}a=n(),C.m.eb(a,t,"hasfocus",i,!0),e.__ko_hasfocusLastValue=i,e.__ko_hasfocusUpdating=!1}var a=i.bind(null,!0),o=i.bind(null,!1);C.a.B(e,"focus",a),C.a.B(e,"focusin",a),C.a.B(e,"blur",o),C.a.B(e,"focusout",o),e.__ko_hasfocusLastValue=!1},update:function(e,n){var t=!!C.a.f(n());e.__ko_hasfocusUpdating||e.__ko_hasfocusLastValue===t||(t?e.focus():e.blur(),!t&&e.__ko_hasfocusLastValue&&e.ownerDocument.body.focus(),C.u.G(C.a.Fb,null,[e,t?"focusin":"focusout"]))}},C.m.wa.hasfocus=!0,C.c.hasFocus=C.c.hasfocus,C.m.wa.hasFocus="hasfocus",C.c.html={init:function(){return{controlsDescendantBindings:!0}},update:function(e,n){C.a.fc(e,n())}},function(){function e(e,n,t){C.c[e]={init:function(e,i,a,o,r){var s,c,d,l,u,p={};if(n){o=a.get("as");var f=a.get("noChildContext");p={as:o,noChildContext:f,exportDependencies:u=!(o&&f)}}return l=(d="render"==a.get("completeOn"))||a.has(C.i.pa),C.o((function(){var a,o=C.a.f(i()),f=!t!=!o,g=!c;(u||f!==s)&&(l&&(r=C.i.Cb(e,r)),f&&(n&&!u||(p.dataDependency=C.S.o()),a=n?r.createChildContext("function"==typeof o?o:i,p):C.S.qa()?r.extend(null,p):r),g&&C.S.qa()&&(c=C.a.Ca(C.h.childNodes(e),!0)),f?(g||C.h.va(e,C.a.Ca(c)),C.Oa(a,e)):(C.h.Ea(e),d||C.i.ma(e,C.i.H)),s=f)}),null,{l:e}),{controlsDescendantBindings:!0}}},C.m.Ra[e]=!1,C.h.ea[e]=!0}e("if"),e("ifnot",!1,!0),e("with",!0)}(),C.c["let"]={init:function(e,n,t,i,a){return n=a.extend(n),C.Oa(n,e),{controlsDescendantBindings:!0}}},C.h.ea["let"]=!0;var R={};C.c.options={init:function(e){if("select"!==C.a.R(e))throw Error("options binding applies only to SELECT elements");for(;0<e.length;)e.remove(0);return{controlsDescendantBindings:!0}},update:function(e,n,t){function i(){return C.a.jb(e.options,(function(e){return e.selected}))}function a(e,n,t){var i=typeof n;return"function"==i?n(e):"string"==i?e[n]:t}function o(n,t){if(g&&l)C.i.ma(e,C.i.H);else if(f.length){var i=0<=C.a.A(f,C.w.M(t[0]));C.a.Zc(t[0],i),g&&!i&&C.u.G(C.a.Fb,null,[e,"change"])}}var s=e.multiple,c=0!=e.length&&s?e.scrollTop:null,d=C.a.f(n()),l=t.get("valueAllowUnset")&&t.has("value"),u=t.get("optionsIncludeDestroyed");n={};var p,f=[];l||(s?f=C.a.Mb(i(),C.w.M):0<=e.selectedIndex&&f.push(C.w.M(e.options[e.selectedIndex]))),d&&("undefined"==typeof d.length&&(d=[d]),p=C.a.jb(d,(function(e){return u||e===r||null===e||!C.a.f(e._destroy)})),t.has("optionsCaption")&&null!==(d=C.a.f(t.get("optionsCaption")))&&d!==r&&p.unshift(R));var g=!1;n.beforeRemove=function(n){e.removeChild(n)},d=o,t.has("optionsAfterRender")&&"function"==typeof t.get("optionsAfterRender")&&(d=function(e,n){o(0,n),C.u.G(t.get("optionsAfterRender"),null,[n[0],e!==R?e:r])}),C.a.ec(e,p,(function(n,i,o){return o.length&&(f=!l&&o[0].selected?[C.w.M(o[0])]:[],g=!0),i=e.ownerDocument.createElement("option"),n===R?(C.a.Bb(i,t.get("optionsCaption")),C.w.cb(i,r)):(o=a(n,t.get("optionsValue"),n),C.w.cb(i,C.a.f(o)),n=a(n,t.get("optionsText"),o),C.a.Bb(i,n)),[i]}),n,d),l||(s?f.length&&i().length<f.length:f.length&&0<=e.selectedIndex?C.w.M(e.options[e.selectedIndex])!==f[0]:f.length||0<=e.selectedIndex)&&C.u.G(C.a.Fb,null,[e,"change"]),(l||C.S.Ya())&&C.i.ma(e,C.i.H),C.a.wd(e),c&&20<Math.abs(c-e.scrollTop)&&(e.scrollTop=c)}},C.c.options.$b=C.a.g.Z(),C.c.selectedOptions={init:function(e,n,t){function i(){var i=n(),a=[];C.a.D(e.getElementsByTagName("option"),(function(e){e.selected&&a.push(C.w.M(e))})),C.m.eb(i,t,"selectedOptions",a)}function a(){var t=C.a.f(n()),i=e.scrollTop;t&&"number"==typeof t.length&&C.a.D(e.getElementsByTagName("option"),(function(e){var n=0<=C.a.A(t,C.w.M(e));e.selected!=n&&C.a.Zc(e,n)})),e.scrollTop=i}if("select"!=C.a.R(e))throw Error("selectedOptions binding applies only to SELECT elements");var o;C.i.subscribe(e,C.i.H,(function(){o?i():(C.a.B(e,"change",i),o=C.o(a,null,{l:e}))}),null,{notifyImmediately:!0})},update:function(){}},C.m.wa.selectedOptions=!0,C.c.style={update:function(e,n){var t=C.a.f(n()||{});C.a.P(t,(function(n,t){if(null!==(t=C.a.f(t))&&t!==r&&!1!==t||(t=""),l)l(e).css(n,t);else if(/^--/.test(n))e.style.setProperty(n,t);else{n=n.replace(/-(\w)/g,(function(e,n){return n.toUpperCase()}));var i=e.style[n];e.style[n]=t,t===i||e.style[n]!=i||isNaN(t)||(e.style[n]=t+"px")}}))}},C.c.submit={init:function(e,n,t,i,a){if("function"!=typeof n())throw Error("The value for a submit binding must be a function");C.a.B(e,"submit",(function(t){var i,o=n();try{i=o.call(a.$data,e)}finally{!0!==i&&(t.preventDefault?t.preventDefault():t.returnValue=!1)}}))}},C.c.text={init:function(){return{controlsDescendantBindings:!0}},update:function(e,n){C.a.Bb(e,n())}},C.h.ea.text=!0,function(){if(s&&s.navigator){var e,n,t,i,a,o=function(e){if(e)return parseFloat(e[1])},c=s.navigator.userAgent;(e=s.opera&&s.opera.version&&parseInt(s.opera.version()))||(a=o(c.match(/Edge\/([^ ]+)$/)))||o(c.match(/Chrome\/([^ ]+)/))||(n=o(c.match(/Version\/([^ ]+) Safari/)))||(t=o(c.match(/Firefox\/([^ ]+)/)))||(i=C.a.W||o(c.match(/MSIE ([^ ]+)/)))||(i=o(c.match(/rv:([^ )]+)/)))}if(8<=i&&10>i)var d=C.a.g.Z(),l=C.a.g.Z(),u=function(e){var n=this.activeElement;(n=n&&C.a.g.get(n,l))&&n(e)},p=function(e,n){var t=e.ownerDocument;C.a.g.get(t,d)||(C.a.g.set(t,d,!0),C.a.B(t,"selectionchange",u)),C.a.g.set(e,l,n)};C.c.textInput={init:function(o,s,c){function d(e,n){C.a.B(o,e,n)}function l(){f||(g=o.value,f=C.a.setTimeout(u,4))}function u(){clearTimeout(f),g=f=r;var e=o.value;m!==e&&(m=e,C.m.eb(s(),c,"textInput",e))}var f,g,m=o.value,b=9==C.a.W?l:u,v=!1;i&&d("keypress",u),11>i&&d("propertychange",(function(e){v||"value"!==e.propertyName||b(e)})),8==i&&(d("keyup",u),d("keydown",u)),p&&(p(o,b),d("dragend",l)),(!i||9<=i)&&d("input",b),5>n&&"textarea"===C.a.R(o)?(d("keydown",l),d("paste",l),d("cut",l)):11>e?d("keydown",l):4>t?(d("DOMAutoComplete",u),d("dragdrop",u),d("drop",u)):a&&"number"===o.type&&d("keydown",l),d("change",u),d("blur",u),C.o((function h(){var e=C.a.f(s());null!==e&&e!==r||(e=""),g!==r&&e===g?C.a.setTimeout(h,4):o.value!==e&&(v=!0,o.value=e,v=!1,m=o.value)}),null,{l:o})}},C.m.wa.textInput=!0,C.c.textinput={preprocess:function(e,n,t){t("textInput",e)}}}(),C.c.uniqueName={init:function(e,n){if(n()){var t="ko_unique_"+ ++C.c.uniqueName.rd;C.a.Yc(e,t)}}},C.c.uniqueName.rd=0,C.c.using={init:function(e,n,t,i,a){var o;return t.has("as")&&(o={as:t.get("as"),noChildContext:t.get("noChildContext")}),n=a.createChildContext(n,o),C.Oa(n,e),{controlsDescendantBindings:!0}}},C.h.ea.using=!0,C.c.value={init:function(e,n,t){var i=C.a.R(e),a="input"==i;if(!a||"checkbox"!=e.type&&"radio"!=e.type){var o=[],s=t.get("valueUpdate"),c=!1,d=null;s&&(o="string"==typeof s?[s]:C.a.wc(s),C.a.Pa(o,"change"));var l,u,p=function(){d=null,c=!1;var i=n(),a=C.w.M(e);C.m.eb(i,t,"value",a)};!C.a.W||!a||"text"!=e.type||"off"==e.autocomplete||e.form&&"off"==e.form.autocomplete||-1!=C.a.A(o,"propertychange")||(C.a.B(e,"propertychange",(function(){c=!0})),C.a.B(e,"focus",(function(){c=!1})),C.a.B(e,"blur",(function(){c&&p()}))),C.a.D(o,(function(n){var t=p;C.a.Ud(n,"after")&&(t=function(){d=C.w.M(e),C.a.setTimeout(p,0)},n=n.substring(5)),C.a.B(e,n,t)})),l=a&&"file"==e.type?function(){var t=C.a.f(n());null===t||t===r||""===t?e.value="":C.u.G(p)}:function(){var a=C.a.f(n()),o=C.w.M(e);null!==d&&a===d?C.a.setTimeout(l,0):a===o&&o!==r||("select"===i?(o=t.get("valueAllowUnset"),C.w.cb(e,a,o),o||a===C.w.M(e)||C.u.G(p)):C.w.cb(e,a))},"select"===i?C.i.subscribe(e,C.i.H,(function(){u?t.get("valueAllowUnset")?l():p():(C.a.B(e,"change",p),u=C.o(l,null,{l:e}))}),null,{notifyImmediately:!0}):(C.a.B(e,"change",p),C.o(l,null,{l:e}))}else C.ib(e,{checkedValue:n})},update:function(){}},C.m.wa.value=!0,C.c.visible={update:function(e,n){var t=C.a.f(n()),i="none"!=e.style.display;t&&!i?e.style.display="":!t&&i&&(e.style.display="none")}},C.c.hidden={update:function(e,n){C.c.visible.update(e,(function(){return!C.a.f(n())}))}},function(e){C.c[e]={init:function(n,t,i,a,o){return C.c.event.init.call(this,n,(function(){var n={};return n[e]=t(),n}),i,a,o)}}}("click"),C.ca=function(){},C.ca.prototype.renderTemplateSource=function(){throw Error("Override renderTemplateSource")},C.ca.prototype.createJavaScriptEvaluatorBlock=function(){throw Error("Override createJavaScriptEvaluatorBlock")},C.ca.prototype.makeTemplateSource=function(e,n){if("string"==typeof e){var t=(n=n||c).getElementById(e);if(!t)throw Error("Cannot find template with ID "+e);return new C.C.F(t)}if(1==e.nodeType||8==e.nodeType)return new C.C.ia(e);throw Error("Unknown template type: "+e)},C.ca.prototype.renderTemplate=function(e,n,t,i){return e=this.makeTemplateSource(e,i),this.renderTemplateSource(e,n,t,i)},C.ca.prototype.isTemplateRewritten=function(e,n){return!1===this.allowTemplateRewriting||this.makeTemplateSource(e,n).data("isRewritten")},C.ca.prototype.rewriteTemplate=function(e,n,t){n=n((e=this.makeTemplateSource(e,t)).text()),e.text(n),e.data("isRewritten",!0)},C.b("templateEngine",C.ca),C.kc=function(){function e(e,n,t,i){e=C.m.ac(e);for(var a=C.m.Ra,o=0;o<e.length;o++){var r=e[o].key;if(Object.prototype.hasOwnProperty.call(a,r)){var s=a[r];if("function"==typeof s){if(r=s(e[o].value))throw Error(r)}else if(!s)throw Error("This template engine does not support the '"+r+"' binding within its templates")}}return t="ko.__tr_ambtns(function($context,$element){return(function(){return{ "+C.m.vb(e,{valueAccessors:!0})+" } })()},'"+t.toLowerCase()+"')",i.createJavaScriptEvaluatorBlock(t)+n}var n=/(<([a-z]+\d*)(?:\s+(?!data-bind\s*=\s*)[a-z0-9\-]+(?:=(?:\"[^\"]*\"|\'[^\']*\'|[^>]*))?)*\s+)data-bind\s*=\s*(["'])([\s\S]*?)\3/gi,t=/\x3c!--\s*ko\b\s*([\s\S]*?)\s*--\x3e/g;return{xd:function(e,n,t){n.isTemplateRewritten(e,t)||n.rewriteTemplate(e,(function(e){return C.kc.Ld(e,n)}),t)},Ld:function(i,a){return i.replace(n,(function(n,t,i,o,r){return e(r,t,i,a)})).replace(t,(function(n,t){return e(t,"\x3c!-- ko --\x3e","#comment",a)}))},md:function(e,n){return C.aa.Xb((function(t,i){var a=t.nextSibling;a&&a.nodeName.toLowerCase()===n&&C.ib(a,e,i)}))}}}(),C.b("__tr_ambtns",C.kc.md),function(){C.C={},C.C.F=function(e){if(this.F=e){var n=C.a.R(e);this.ab="script"===n?1:"textarea"===n?2:"template"==n&&e.content&&11===e.content.nodeType?3:4}},C.C.F.prototype.text=function(){var e=1===this.ab?"text":2===this.ab?"value":"innerHTML";if(0==arguments.length)return this.F[e];var n=arguments[0];"innerHTML"===e?C.a.fc(this.F,n):this.F[e]=n};var e=C.a.g.Z()+"_";C.C.F.prototype.data=function(n){if(1===arguments.length)return C.a.g.get(this.F,e+n);C.a.g.set(this.F,e+n,arguments[1])};var n=C.a.g.Z();C.C.F.prototype.nodes=function(){var e=this.F;if(0==arguments.length){var t=C.a.g.get(e,n)||{},i=t.lb||(3===this.ab?e.content:4===this.ab?e:r);if(!i||t.jd){var a=this.text();a&&a!==t.bb&&(i=C.a.Md(a,e.ownerDocument),C.a.g.set(e,n,{lb:i,bb:a,jd:!0}))}return i}t=arguments[0],this.ab!==r&&this.text(""),C.a.g.set(e,n,{lb:t})},C.C.ia=function(e){this.F=e},C.C.ia.prototype=new C.C.F,C.C.ia.prototype.constructor=C.C.ia,C.C.ia.prototype.text=function(){if(0==arguments.length){var e=C.a.g.get(this.F,n)||{};return e.bb===r&&e.lb&&(e.bb=e.lb.innerHTML),e.bb}C.a.g.set(this.F,n,{bb:arguments[0]})},C.b("templateSources",C.C),C.b("templateSources.domElement",C.C.F),C.b("templateSources.anonymousTemplate",C.C.ia)}(),function(){function e(e,n,t){var i;for(n=C.h.nextSibling(n);e&&(i=e)!==n;)t(i,e=C.h.nextSibling(i))}function n(n,t){if(n.length){var i=n[0],a=n[n.length-1],o=i.parentNode,r=C.ga.instance,s=r.preprocessNode;if(s){if(e(i,a,(function(e,n){var t=e.previousSibling,o=s.call(r,e);o&&(e===i&&(i=o[0]||n),e===a&&(a=o[o.length-1]||t))})),n.length=0,!i)return;i===a?n.push(i):(n.push(i,a),C.a.Ua(n,o))}e(i,a,(function(e){1!==e.nodeType&&8!==e.nodeType||C.vc(t,e)})),e(i,a,(function(e){1!==e.nodeType&&8!==e.nodeType||C.aa.cd(e,[t])})),C.a.Ua(n,o)}}function t(e){return e.nodeType?e:0<e.length?e[0]:null}function i(e,i,a,r,s){s=s||{};var c=(e&&t(e)||a||{}).ownerDocument,d=s.templateEngine||o;if(C.kc.xd(a,d,c),"number"!=typeof(a=d.renderTemplate(a,r,s,c)).length||0<a.length&&"number"!=typeof a[0].nodeType)throw Error("Template engine must return an array of DOM nodes");switch(c=!1,i){case"replaceChildren":C.h.va(e,a),c=!0;break;case"replaceNode":C.a.Xc(e,a),c=!0;break;case"ignoreTargetNode":break;default:throw Error("Unknown renderMode: "+i)}return c&&(n(a,r),s.afterRender&&C.u.G(s.afterRender,null,[a,r[s.as||"$data"]]),"replaceChildren"==i&&C.i.ma(e,C.i.H)),a}function a(e,n,t){return C.O(e)?e():"function"==typeof e?e(n,t):e}var o;C.gc=function(e){if(e!=r&&!(e instanceof C.ca))throw Error("templateEngine must inherit from ko.templateEngine");o=e},C.dc=function(e,n,s,c,d){if(((s=s||{}).templateEngine||o)==r)throw Error("Set a template engine before calling renderTemplate");if(d=d||"replaceChildren",c){var l=t(c);return C.$((function(){var o=n&&n instanceof C.fa?n:new C.fa(n,null,null,null,{exportDependencies:!0}),r=a(e,o.$data,o);o=i(c,d,r,o,s),"replaceNode"==d&&(l=t(c=o))}),null,{Sa:function(){return!l||!C.a.Sb(l)},l:l&&"replaceNode"==d?l.parentNode:l})}return C.aa.Xb((function(t){C.dc(e,n,s,t,"replaceNode")}))},C.Qd=function(e,t,o,s,c){function d(e,n){C.u.G(C.a.ec,null,[s,e,u,o,l,n]),C.i.ma(s,C.i.H)}function l(e,t){n(t,p),o.afterRender&&o.afterRender(t,e),p=null}function u(n,t){p=c.createChildContext(n,{as:f,noChildContext:o.noChildContext,extend:function(e){e.$index=t,f&&(e[f+"Index"]=t)}});var r=a(e,n,p);return i(s,"ignoreTargetNode",r,p,o)}var p,f=o.as,g=!1===o.includeDestroyed||C.options.foreachHidesDestroyed&&!o.includeDestroyed;if(g||o.beforeRemove||!C.Pc(t))return C.$((function(){var e=C.a.f(t)||[];"undefined"==typeof e.length&&(e=[e]),g&&(e=C.a.jb(e,(function(e){return e===r||null===e||!C.a.f(e._destroy)}))),d(e)}),null,{l:s});d(t.v());var m=t.subscribe((function(e){d(t(),e)}),null,"arrayChange");return m.l(s),m};var s=C.a.g.Z(),c=C.a.g.Z();C.c.template={init:function(e,n){var t=C.a.f(n());if("string"==typeof t||"name"in t)C.h.Ea(e);else if("nodes"in t){if(t=t.nodes||[],C.O(t))throw Error('The "nodes" option must be a plain, non-observable array.');var i=t[0]&&t[0].parentNode;i&&C.a.g.get(i,c)||(i=C.a.Yb(t),C.a.g.set(i,c,!0)),new C.C.ia(e).nodes(i)}else{if(!(0<(t=C.h.childNodes(e)).length))throw Error("Anonymous template defined, but no template content was provided");i=C.a.Yb(t),new C.C.ia(e).nodes(i)}return{controlsDescendantBindings:!0}},update:function(e,n,t,i,a){var o=n();t=!0,i=null,"string"==typeof(n=C.a.f(o))?n={}:(o="name"in n?n.name:e,"if"in n&&(t=C.a.f(n["if"])),t&&"ifnot"in n&&(t=!C.a.f(n.ifnot)),t&&!o&&(t=!1)),"foreach"in n?i=C.Qd(o,t&&n.foreach||[],n,e,a):t?(t=a,"data"in n&&(t=a.createChildContext(n.data,{as:n.as,noChildContext:n.noChildContext,exportDependencies:!0})),i=C.dc(o,t,n,e)):C.h.Ea(e),a=i,(n=C.a.g.get(e,s))&&"function"==typeof n.s&&n.s(),C.a.g.set(e,s,!a||a.ja&&!a.ja()?r:a)}},C.m.Ra.template=function(e){return 1==(e=C.m.ac(e)).length&&e[0].unknown||C.m.Id(e,"name")?null:"This template engine does not support anonymous templates nested within its templates"},C.h.ea.template=!0}(),C.b("setTemplateEngine",C.gc),C.b("renderTemplate",C.dc),C.a.Kc=function(e,n,t){var i,a,o,r,s;if(e.length&&n.length)for(i=a=0;(!t||i<t)&&(r=e[a]);++a){for(o=0;s=n[o];++o)if(r.value===s.value){r.moved=s.index,s.moved=r.index,n.splice(o,1),i=o=0;break}i+=o}},C.a.Pb=function(){function e(e,n,t,i,a){var o,r,s,c,d,l=Math.min,u=Math.max,p=[],f=e.length,g=n.length,m=g-f||1,b=f+g+1;for(o=0;o<=f;o++)for(c=s,p.push(s=[]),d=l(g,o+m),r=u(0,o-1);r<=d;r++)s[r]=r?o?e[o-1]===n[r-1]?c[r-1]:l(c[r]||b,s[r-1]||b)+1:r+1:o+1;for(l=[],u=[],m=[],o=f,r=g;o||r;)g=p[o][r]-1,r&&g===p[o][r-1]?u.push(l[l.length]={status:t,value:n[--r],index:r}):o&&g===p[o-1][r]?m.push(l[l.length]={status:i,value:e[--o],index:o}):(--r,--o,a.sparse||l.push({status:"retained",value:n[r]}));return C.a.Kc(m,u,!a.dontLimitMoves&&10*f),l.reverse()}return function(n,t,i){return i="boolean"==typeof i?{dontLimitMoves:i}:i||{},t=t||[],(n=n||[]).length<t.length?e(n,t,"added","deleted",i):e(t,n,"deleted","added",i)}}(),C.b("utils.compareArrays",C.a.Pb),function(){function e(e,n,t,i,a){var o=[],s=C.$((function(){var r=n(t,a,C.a.Ua(o,e))||[];0<o.length&&(C.a.Xc(o,r),i&&C.u.G(i,null,[t,r,a])),o.length=0,C.a.Nb(o,r)}),null,{l:e,Sa:function(){return!C.a.kd(o)}});return{Y:o,$:s.ja()?s:r}}var n=C.a.g.Z(),t=C.a.g.Z();C.a.ec=function(i,a,o,s,c,d){function l(e){f={Aa:e,pb:C.ta(x++)},_.push(f),h||T.push(f)}function u(e){f=v[e],x!==f.pb.v()&&P.push(f),f.pb(x++),C.a.Ua(f.Y,i),_.push(f)}function p(e,n){if(e)for(var t=0,i=n.length;t<i;t++)C.a.D(n[t].Y,(function(i){e(i,t,n[t].Aa)}))}"undefined"==typeof(a=a||[]).length&&(a=[a]),s=s||{};var f,g,m,b,v=C.a.g.get(i,n),h=!v,_=[],S=0,x=0,w=[],y=[],k=[],P=[],T=[],D=0;if(h)C.a.D(a,l);else{if(!d||v&&v._countWaitingForRemove){var E=C.a.Mb(v,(function(e){return e.Aa}));d=C.a.Pb(E,a,{dontLimitMoves:s.dontLimitMoves,sparse:!0})}var A,R,L;for(E=0;A=d[E];E++)switch(R=A.moved,L=A.index,A.status){case"deleted":for(;S<L;)u(S++);R===r&&((f=v[S]).$&&(f.$.s(),f.$=r),C.a.Ua(f.Y,i).length&&(s.beforeRemove&&(_.push(f),D++,f.Aa===t?f=null:k.push(f)),f&&w.push.apply(w,f.Y))),S++;break;case"added":for(;x<L;)u(S++);R!==r?(y.push(_.length),u(R)):l(A.value)}for(;x<a.length;)u(S++);_._countWaitingForRemove=D}C.a.g.set(i,n,_),p(s.beforeMove,P),C.a.D(w,s.beforeRemove?C.oa:C.removeNode);try{b=i.ownerDocument.activeElement}catch(I){}if(y.length)for(;(E=y.shift())!=r;){for(f=_[E],g=r;E;)if((m=_[--E].Y)&&m.length){g=m[m.length-1];break}for(a=0;S=f.Y[a];g=S,a++)C.h.Wb(i,S,g)}for(E=0;f=_[E];E++){for(f.Y||C.a.extend(f,e(i,o,f.Aa,c,f.pb)),a=0;S=f.Y[a];g=S,a++)C.h.Wb(i,S,g);!f.Ed&&c&&(c(f.Aa,f.Y,f.pb),f.Ed=!0,g=f.Y[f.Y.length-1])}for(b&&i.ownerDocument.activeElement!=b&&b.focus(),p(s.beforeRemove,k),E=0;E<k.length;++E)k[E].Aa=t;p(s.afterMove,P),p(s.afterAdd,T)}}(),C.b("utils.setDomNodeChildrenFromArrayMapping",C.a.ec),C.ba=function(){this.allowTemplateRewriting=!1},C.ba.prototype=new C.ca,C.ba.prototype.constructor=C.ba,C.ba.prototype.renderTemplateSource=function(e,n,t,i){return(n=9>C.a.W||!e.nodes?null:e.nodes())?C.a.la(n.cloneNode(!0).childNodes):(e=e.text(),C.a.ua(e,i))},C.ba.Ma=new C.ba,C.gc(C.ba.Ma),C.b("nativeTemplateEngine",C.ba),function(){C.$a=function(){var e=this.Hd=function(){if(!l||!l.tmpl)return 0;try{if(0<=l.tmpl.tag.tmpl.open.toString().indexOf("__"))return 2}catch(e){}return 1}();this.renderTemplateSource=function(n,t,i,a){if(a=a||c,i=i||{},2>e)throw Error("Your version of jQuery.tmpl is too old. Please upgrade to jQuery.tmpl 1.0.0pre or later.");var o=n.data("precompiled");return o||(o=n.text()||"",o=l.template(null,"{{ko_with $item.koBindingContext}}"+o+"{{/ko_with}}"),n.data("precompiled",o)),n=[t.$data],t=l.extend({koBindingContext:t},i.templateOptions),(t=l.tmpl(o,n,t)).appendTo(a.createElement("div")),l.fragments={},t},this.createJavaScriptEvaluatorBlock=function(e){return"{{ko_code ((function() { return "+e+" })()) }}"},this.addTemplate=function(e,n){c.write("<script type='text/html' id='"+e+"'>"+n+"<\/script>")},0<e&&(l.tmpl.tag.ko_code={open:"__.push($1 || '');"},l.tmpl.tag.ko_with={open:"with($1) {",close:"} "})},C.$a.prototype=new C.ca,C.$a.prototype.constructor=C.$a;var e=new C.$a;0<e.Hd&&C.gc(e),C.b("jqueryTmplTemplateEngine",C.$a)}()}))}()},function(e,n,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=t(0),o=t(3),r=t(1),s=t(4),c=t(11).getInstance(window.ServerData),d=o.String,l=r.Helper,u=a.KeyCode;function p(e){e.preventDefault?e.preventDefault():e.returnValue=!1}n.applyExtensions=function(e){var n,a=1,o={};e.components.loaders.unshift({loadComponent:function(n,t,i){e.components.defaultLoader.loadComponent(n,t,(function(n){var a;t.enableExtensions&&(a=n.createViewModel,n.createViewModel=function(n,t){var i=a(n,t);return function(n,t){var i=t.componentId;if(i&&o[i]){var a=o[i],r=a.parentViewModel,d=a.alias,l=e.unwrap(a.events)||{};d&&("string"==typeof d&&(d=r[d]),e.isWritableObservable(d)&&(d(n),e.utils.domNodeDisposal.addDisposeCallback(t,(function(){d(null)})))),e.utils.objectForEach(l,(function(e,t){e&&t&&("load"===e?t.call(r,n):(e="on"+e.charAt(0).toUpperCase()+e.substr(1),s.isComponentEvent(n[e])&&n[e].subscribe((function(i){return n[e].tracingOptions&&c.logComponentEvent(n,n[e].tracingOptions,e,i),t.apply(r,i)}))))}))}}(i,t.element),i}),i(n)}))}}),n=e.bindingHandlers.component.init,e.bindingHandlers.component.init=function(t,i,r,s,c){var d=e.unwrap(i());if("string"!=typeof d){var l=d.publicMethods,u=d.event;if(d.disabled)return;if(l||u){var p=t.componentId=a++;o[p]={parentViewModel:s,alias:l,events:u},e.utils.domNodeDisposal.addDisposeCallback(t,(function(){delete o[p]}))}}return n(t,i,r,s,c)},e.bindingHandlers.pageViewComponent={init:function(n,t,i,a,o){var r=e.unwrap(t());r.publicMethods=a.viewInterfaces[o.$index()],r.event=r.event||{},r.event.load=a.view_onLoad,r.event.switchView=a.view_onSwitchView;return e.bindingHandlers.component.init(n,(function(){return r}),i,a,o)}},e.bindingHandlers.component.preprocess=function(e){return!e||'"'!==e.charAt(0)&&"'"!==e.charAt(0)?e:d.format("{ name: {0}, params: { } }",e)},e.bindingHandlers.defineGlobals={init:function(n,t,i,a,o){function r(e){var n="";try{var t=document.createElement("div");t.innerHTML=e,t.childNodes.length>0&&t.childNodes[0].value&&(n=t.childNodes[0].value)}catch(i){}return n}var s=e.unwrap(t());s.sFT=r(s.sFT)||s.sFT||r(s.sFTTag);var c=o.extend({svr:s,str:s.str,html:s.html,$location:e.observable()});if(c.$location.subscribe((function(e){e&&document.location.replace(e)})),i.has("bodyCssClass")){var d=l.getIEVersion();if(d){var u={css:{}};u.css["IE_M"+d]=!0,e.applyBindingsToNode(n,u)}if(l.isHighContrast()){var p=l.getHighContrastTheme(),f="black"===p;if(f||"white"===p){var g=f?"theme-dark":"theme-light",m={css:{}};m.css[g]=!0,e.applyBindingsToNode(n,m)}}}return e.applyBindingsToDescendants(c,n),{controlsDescendantBindings:!0}}},e.bindingHandlers.autoSubmit={update:function(n,t){var i=t();e.unwrap(i)&&(e.isWritableObservable(i)&&i(!1),n.submit())}},e.bindingHandlers.postRedirectForm={init:function(e){e.setAttribute("method","POST"),e.setAttribute("aria-hidden","true"),e.setAttribute("target","_top")},update:function(n,t){var i=e.unwrap(t());i&&i.url&&(n.setAttribute("action",i.url),i.target&&n.setAttribute("target",i.target),i.postParams&&e.utils.objectForEach(i.postParams,(function(e,t){"unsafe_"===e.substr(0,7)&&(e=e.substr(7)),null!==t&&t!==undefined||(t="");var i=document.createElement("input");i.setAttribute("type","hidden"),i.setAttribute("name",e),i.setAttribute("value",t),n.appendChild(i)})),n.submit())}},e.bindingHandlers.href={update:function(n,t){e.bindingHandlers.attr.update(n,(function(){return{href:t()}}))}},e.bindingHandlers.placeholder={update:function(n,t){e.bindingHandlers.attr.update(n,(function(){return{placeholder:t()}}))}},e.bindingHandlers.ariaLabel={update:function(n,t){e.bindingHandlers.attr.update(n,(function(){return{"aria-label":t()}}))}},e.bindingHandlers.ariaDescribedBy={update:function(n,t){e.bindingHandlers.attr.update(n,(function(){return{"aria-describedby":t()}}))}},e.bindingHandlers.htmlWithBindings={init:function(){return{controlsDescendantBindings:!0}},update:function(n,t,i,a,o){e.utils.setHtml(n,t());var r=i.get("childBindings");if(r)for(var s in r)if(r.hasOwnProperty(s)){var c=document.getElementById(s);c&&e.applyBindingsToNode(c,r[s],o)}e.applyBindingsToDescendants(o,n)}},e.bindingHandlers.backgroundImage={update:function(e,n){var t=n();function i(n){e.style.backgroundImage=n?d.format("url('{0}')",n):""}var a=window.$Loader,o=new Image;o.onerror=function(){a&&a.On&&a.On(o,!0,i)},o.src=t,i(t)}},e.bindingHandlers.addEventHandlers={init:function(e){var n=window.$Loader;e.onerror=function(){if(n&&n.OnError)return n.OnError(e,(function(n){e.src=n}))}}},e.bindingHandlers.wizardCssCheck={update:function(e,n,t,i,a){if(CSSLoadFail()){var o=document.getElementById("mainDiv");o&&(o.style.display="none")}}},e.bindingHandlers.withProperties={init:function(n,t,i,a,o){var r=o.extend(t);return e.applyBindingsToDescendants(r,n),{controlsDescendantBindings:!0}}},e.bindingHandlers.clickExpr={preprocess:function(e){return"function ($data, $event) { "+e+" }"},init:function(n,t,i,a,o){return e.bindingHandlers.click.init.call(this,n,t,i,a,o)}},e.bindingHandlers.imgSrc={init:function(e){var n=window.$Loader;e.onerror=function(){if(n&&n.On)return n.On(e,!0,(function(n){e.src=n}))},l.isSvgImgSupported()?e.src=e.getAttribute("svgSrc"):e.src=e.getAttribute("pngSrc")}},e.bindingHandlers.svgSrc={update:function(n,t,i){var a=e.unwrap(t());e.bindingHandlers.attr.update(n,(function(){a&&l.isSvgImgSupported()&&(a=a.replace(new RegExp(".png$"),".svg"));var e=i.get("format");if(e)for(var n in e)e.hasOwnProperty(n)&&!e[n]&&(a=a.replace(n,""));return{src:a}}))}},e.bindingHandlers.injectScript={init:function(n,t){var i=e.unwrap(t()),a=document.createElement("script");return a.type="text/javascript",a.src=i,n.appendChild(a),{controlsDescendantBindings:!0}}},e.bindingHandlers.injectIframe={init:function(n,t){var i=e.unwrap(t());if(i&&i.url){var a=document.createElement("iframe");a.height="0",a.width="0",a.style.display="none",a.src=e.unwrap(i.url),i.onload&&(a.onload=function(){i.onload(a)}),n.appendChild(a)}return{controlsDescendantBindings:!0}}},e.bindingHandlers.injectDfpIframe={init:function(n,t){var i=e.unwrap(t());if(i&&i.url){var a=document.createElement("iframe");a.id="iDeviceFingerPrinting",a.setAttribute("style","color:#000000;float:left;visibility:hidden;position:absolute;width:1px;height:1px;left:-10000px;top:-10000px;border:0px"),a.src=e.unwrap(i.url),i.onload&&(a.onload=function(){i.onload(a)}),n.appendChild(a)}return{controlsDescendantBindings:!0}}},e.bindingHandlers.hasFocusEx={init:e.bindingHandlers.hasFocus.init,update:function(n,t,i,a,o){if(e.bindingHandlers.hasFocus.update(n,t,i,a,o),e.unwrap(t())){if(n.value){var r=n.value.length;if("selectionStart"in n)setTimeout((function(){try{n.selectionStart=r,n.selectionEnd=r}catch(e){}}),0);else if("createTextRange"in n){var s=n.createTextRange();s.moveStart("character",r),s.collapse(),s.moveEnd("character",r),s.select()}}n.focus()}else n.blur()}},e.bindingHandlers.preventTabbing={init:function(n,t){var i=e.unwrap(t())||{};"none"!==i.direction&&e.utils.registerEventHandler(n,"keydown",(function(e){return"Tab"!==(e=e||window.event).code&&e.keyCode!==u.Tab||!(!i.direction||"both"===i.direction||"up"===i.direction&&e.shiftKey||"down"===i.direction&&!e.shiftKey)||(p(e),!1)}))}},e.bindingHandlers.ariaHidden={update:function(n,t){e.bindingHandlers.attr.update(n,(function(){return{"aria-hidden":e.unwrap(t())}}))}},e.bindingHandlers.moveOffScreen={update:function(n,t){var a=e.unwrap(t());if("object"!==i(a)){var o=!1!==a;a={setClass:o,setTabIndex:o,setAriaHidden:o}}e.bindingHandlers.css.update(n,(function(){return{moveOffScreen:!1!==a.setClass}})),e.bindingHandlers.attr.update(n,(function(){return{tabindex:!1!==a.setTabIndex?-1:0}})),e.bindingHandlers.ariaHidden.update(n,(function(){return!1!==a.setAriaHidden}))}},e.bindingHandlers.pressEnter={init:function(n,t,i,a,o){var r=e.unwrap(t()),s=o.$data;e.utils.registerEventHandler(n,"keydown",(function(e){return"Enter"!==(e=e||window.event).code&&e.keyCode!==u.Enter||(p(e),r(s,e),!1)}))}},e.bindingHandlers.isScrolledToBottom={init:function(n,t){var i=e.unwrap(t()),a=i.disabled,o=i.value,r=i.sticky;function s(){var e=n.scrollTop+n.offsetHeight>=n.scrollHeight;return o(e),r&&e&&c(),e}function c(){l.removeEventListener(n,"scroll",s),l.removeEventListener(window,"resize",s)}!a&&e.isWritableObservable(o)&&(r&&s()||(l.addEventListener(n,"scroll",s),l.addEventListener(window,"resize",s),e.utils.domNodeDisposal.addDisposeCallback(n,c)))},update:function(n,t){e.unwrap(t()).value()&&(n.scrollTop=n.scrollHeight)}},e.bindingHandlers.animationEnd={init:function(n,t,i,a,o){var r=l.getAnimationEndEventName();r&&e.bindingHandlers.event.init(n,(function(){var n={};return n[r]=e.unwrap(t()),n}),i,a,o)}},e.bindingHandlers.htmlWithMods={init:function(n,t,i){var a=e.unwrap(t());if(a){var o=i.get("htmlMods");if(o&&o.filterLinks){var r=document.createElement("div");r.innerHTML=a;for(var s=r.getElementsByTagName("a"),c=s.length-1;c>=0;c--){var d=s[c],l=d.innerText,u=d.protocol;if("mailto:"===u||"tel:"===u){if(!1!==o.allowContactProtocols)continue;l=f(l,d.pathname)}else l=f(l,d.getAttribute("href"));var p=document.createElement("span");p.innerText=l,d.parentNode.replaceChild(p,d)}a=r.innerHTML}e.utils.setHtml(n,a)}function f(e,n){return e!==n?e+" ("+n+")":e}}},e.bindingHandlers.externalCss={update:function(n,t){e.utils.objectForEach(e.unwrap(t()),(function(t,i){var a=e.unwrap(i);e.utils.toggleDomNodeCssClass(n,t,a),e.utils.toggleDomNodeCssClass(n,"ext-"+t,a)}))}},e.virtualElements.allowedBindings.withProperties=!0,(e.options=e.options||{}).createChildContextWithAs=!0,t(72).applyExtensions(e)}},function(e,n){e.exports={format:function(e){if(e)for(var n=1;n<arguments.length;n++)e=e.replace(new RegExp("\\{"+(n-1)+"\\}","g"),arguments[n]);return e}}},function(e,n,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=t(3),o=t(1),r=a.String;n.applyExtensions=function(e){var n;n=e.bindingProvider.instance.preprocessNode,e.bindingProvider.instance.preprocessNode=function(t){if(1===t.nodeType&&t.tagName&&"select"===t.tagName.toLowerCase()&&o.Helper.isIEOlderThan(8)){var i=t.getAttribute("data-bind");if(i){var a=[],s=r.doubleSplit(i,",",":",!1,r.trim),c={hasFocus:"hasFocusBasic"};e.utils.objectForEach(s,(function(e){var n=c[e]||e+"Ex";a.push(n+":"+s[e])})),t.setAttribute("data-bind",a.join())}}n&&n(t)},e.bindingHandlers.optionsEx={init:function(n,t,a,o,r){var s=e.unwrap(t()),c=a.get("optionsValueEx"),d=a.get("optionsTextEx"),l=a.get("valueEx");function u(n,t){var a=i(t);return"function"===a?e.unwrap(t(n)):"string"===a&&n[t]?"function"==typeof n[t]?e.unwrap(n[t]()):e.unwrap(n[t]):void 0}e.utils.arrayForEach(s,(function(e){var t=document.createElement("option");t.$data=e,c&&(t.value=u(e,c));var i=u(e,d),a=document.createTextNode(i);t.appendChild(a),n.appendChild(t)})),e.applyBindingsToNode(n,{event:{change:function(){var e=n.options[n.selectedIndex].$data;n.$data=e;var t=l.peek();l("object"===i(t)?e:n.value)}}});var p,f=l.subscribe((function(e){if(e){var t=i(e),a="object"===t,o="string"===t;if(a&&n.$data!==e||o&&n.value!==e)for(var r=0;r<n.options.length;r++){var s=n.options[r];if(a&&s.$data===e||o&&s.value===e)return s.selected=!0,void(n.$data=s.$data)}}}));p=f,e.utils.domNodeDisposal.addDisposeCallback(n,(function(){p.dispose()})),l.peek()?l.valueHasMutated():(l(n.options[0].value),n.$data=n.options[0].$data)}},e.bindingHandlers.hasFocusBasic={init:e.bindingHandlers.hasFocus.init,update:function(n,t){e.unwrap(t())?n.focus():n.blur()}}}},function(e,n){var t=window;t.StringRepository=e.exports=t.StringRepository||new function(){var e={};this.registerSource=function(n,t){e[n]=e[n]||[],e[n].push(t)},this.getStrings=function(n,t){for(var i={},a=e[n]||[],o=0,r=a.length;o<r;o++)a[o](i,t);return i}}},function(e,n,t){var i=t(2),a=t(3),o=t(14),r=t(52),s=t(0),c=t(1),d=t(322),l=t(43),u=t(5),p=t(25),f=t(53),g=(t(18),t(9)),m=t(11).getInstance(window.ServerData),b=t(7),v=t(54),h=t(325),_=t(326),C=null;C=t(356);var S=window,x=s.Error,w=s.EstsError,y=a.String,k=a.Object,P=a.ErrorData,T=s.LoginMode,D=s.PaginatedState,E=s.CredentialType,I=s.AnimationState,A=s.AnimationName,R=s.StringCustomizationPageId,L=c.Helper,B=c.QueryString,O=c.Cookies,U=p.GctResultAction,F=p.GctRequestHelperFlags,N=r.CanaryValidationSuccessAction,M=s.ExternalFederatedIdpType;e.exports=function(e){var n=this,a=e,c=[],V=null,j={},H=[],W=null,$=!1,G=null,q=null,K=i.observable(),z=i.observable(!1),Q=i.observable(!1),J=i.observable(!1),X=i.observable(!1),Y=i.observable(null),Z=a.str,ee=a.html,ne=a.iLoginMode,te=a.iLBodyDefault,ie=a.fPOST_ForceSignin,ae=a.sFTTag,oe=a.sFTCookieName,re=a.sCtx,se=a.fIsCloudBuild,ce=a.fAllowCancel,de=a.urlPost,le=a.urlCancel,ue=a.urlBack,pe=a.urlResetPassword,fe=a.urlHIPScript,ge=a.sPrefillUsername,me=a.sSignInUsername,be=a.sPOST_Username,ve=a.sZtdUpnHint,he=a.sFoundMSAs||"",_e=a.fLockUsername,Ce=a.sErrTxt,Se=a.arrValErrs,xe=a.fPrefixCookieDomainEnabled,we=a.staticTenantBranding,ye=a.oAppCobranding,ke=a.iBackgroundImage,Pe=a.sResetPasswordPrefillParam||"mn",Te=a.sRawQueryString,De=a.arrSessions,Ee=a.urlGoToAADError,Ie=a.oUrlOtherIdpPostParams,Ae=a.oCancelPostParams,Re=a.desktopSsoConfig,Le=a.oGetCredTypeResult,Be=a.sPOST_PaginatedLoginState,Oe=a.sPOST_PaginatedLoginStateRNGCSLK,Ue=a.sPOST_PaginatedLoginStateRNGCEntropy,Fe=a.sPOST_PaginatedLoginStateRNGCDefaultType,Ne=a.fPOST_IsSignupPost,Me=a.arrFidoAllowList,Ve=a.iPromotedFedCredType,je=a.arrPromotedFedCredTypes||[],He=a.urlLinkedInFed,We=a.urlGitHubFed,$e=a.urlGoogleFed,Ge=a.urlFacebookFed,qe=a.fEnableCssAnimation,Ke=a.sProofConfirm,ze=!!a.fIsFidoSupported,Qe=!!a.sExternalCanary,Je=a.iSessionPullType,Xe=a.fIsDebugTracingEnabled,Ye=a.fDetectBrowserCapabilities,Ze=a.fIsWriteWloptCookieDisallowed,en=a.urlOtherIdpSignup,nn=a.urlGoogleFedSignup,tn=a.urlFacebookFedSignup,an=a.isGlobalTenant,on=(a.fAutopilotProvisioningNavigation,a.fShowTilesAfterSessionPull),rn=a.fCBShowSignUp,sn=a.urlSignUp,cn=a.fShowSignInOptionsAsButton,dn=a.fShowForgotUsernameLink,ln=a.fShowRemoteConnectLocationPage,un=a.fIsRestrictedWsi,pn=a.fEnableUserStateFix,fn=a.urlOidcDiscoveryEndpointFormat,gn=a.urlDefaultFavicon,mn=a.arrLivePreviewOrigins,bn=a.fShouldSupportTargetCredentialForRecovery,vn=a.iCurrentTargetCredentialForRecovery,hn=a.fIsSelfServiceSignupUxEnabled,_n=a.fUsePromotedFedCredTypesArray,Cn=a.arrExternalTrustedRealmFederatedIdps||[],Sn=a.fEnableLivePreview,xn=a.iViewId,wn=a.fSamlAndOidcCompatibleUx,yn=a.fUseWebviewFidoCustomProtocol,kn=a.fIsQrCodePinSupported,Pn=a.fUpdateFacebookIcon,Tn=a.fEnableDFPIntegration,Dn=a.fShowPassKeyErrorUCP,En=a.fPasskeyAssertionRedirect;function In(e){n.livePreviewBranding(e),Qn(e)}function An(e,t){var i=n.paginationControlMethods(),a=i.getCurrentView(),o=i.getSharedDataItem("otherIdpRedirectUrl");o=B.add(o,t),i.setSharedDataItem("otherIdpRedirectUrl",o),c=c.concat(f.mergeSessions(i.getSharedDataItem("sessions"),e)),a&&n.asyncTileRequestCount<=0&&(a.viewId===D.Tiles?(a.viewInterface.addNewSessions(c),c=[]):a.viewId===D.Username&&(!n.newSession()&&c.length>0&&n.newSession(c[0]),c=[]))}function Rn(e,i){e?u.resolve().then(function(){t(32),n.showDebugDetails(!0),i&&n.debugDetailsMethods()&&n.debugDetailsMethods().activateFocus()}.bind(null,t))["catch"](t.oe):n.showDebugDetails(!1),!e&&n.footerMethods()&&n.footerMethods().focusMoreInfoLink()}function Ln(e){if(!e||!e.confirmationViewId)throw"No view ID was specified to handle the canary validation failure.";W=e.confirmationViewId,n.postCanaryValidationAction=e.postConfirmationAction}function Bn(e){n.postCanaryValidationAction=e}function On(e){var n="";try{var t=document.createElement("div");t.innerHTML=e,t.childNodes.length>0&&t.childNodes[0].value&&(n=t.childNodes[0].value)}catch(i){}return n}function Un(){return n.paginationControlMethods()&&n.paginationControlMethods().currentViewHasMetadata("showFedCredButton")&&!un}function Fn(){return kn&&n.paginationControlMethods()&&n.paginationControlMethods().currentViewHasMetadata("showQrCodeSignInButton")}function Nn(){return Un()&&n.otherSigninOptions()&&n.otherSigninOptions().length>0}function Mn(){return hn&&n.paginationControlMethods()&&n.paginationControlMethods().currentViewHasMetadata("showSignupFedCredButton")}function Vn(){return Mn()&&n.otherSignupOptions()&&n.otherSignupOptions().length>0}function jn(t,r){n.isFidoSupported(t),function(e,t){var i=o.loadTenantBranding(Le&&Le.EstsProperties&&Le.EstsProperties.UserTenantBranding),r=o.loadTenantBranding(we),s=o.getMergedBranding(r,i,an,!Le);if(n.initialSharedData={username:y.cleanseUsername(e),displayName:e,remoteNgcParams:{sessionIdentifier:Oe,entropy:Ue,defaultType:Fe},otcParams:{},fidoParams:{allowList:Me},hipRequiredForUsername:fe?y.cleanseUsername(e):"",sessions:De||[],flowToken:On(a.sFT)||a.sFT||On(ae),userTenantBranding:s||{},callMetadata:{},availableCreds:[],evictedCreds:[],otcCredential:{},showCredViewBrandingDesc:!(!ye||!ye.showDescOnCredViews),unsafe_desktopSsoDomainToUse:Re&&Re.startDesktopSsoOnPageLoad?Re.hintedDomainName:null,isSignupPost:Ne,otherIdpRedirectUrl:Ee,recoveryCredentialsData:{}},Le){(parseInt(Be)||D.Unknown)===D.OneTimeCode&&(Le.Credentials&&Le.Credentials.PrefCredential!==E.PublicIdentifierCode&&Le.Credentials.PrefCredential!==E.NoPreferredCredential&&(Le.Credentials.PrefCredential=E.OneTimeCode),Ke&&(n.initialSharedData.proofConfirmation=Ke));var c=V.getGctSharedData(Le,t,!1);n.initialSharedData.preferredCredential=c.preferredCredential,n.initialSharedData.availableCreds=c.availableCreds||[],n.initialSharedData.evictedCreds=c.evictedCreds||[],n.initialSharedData.otcCredential=c.otcCredential,n.initialSharedData.otcParams=c.otcParams,n.initialSharedData.idpRedirectUrl=c.fedRedirectParams.idpRedirectUrl,n.initialSharedData.idpRedirectPostParams=c.fedRedirectParams.idpRedirectPostParams,n.initialSharedData.idpRedirectProvider=c.fedRedirectParams.idpRedirectProvider,n.initialSharedData.supportsNativeCredentialRecovery=c.supportsNativeCredentialRecovery}bn&&(n.initialSharedData.targetCredentialForRecovery=vn),(ne===T.Signup||rn&&!sn)&&(n.initialSharedData.availableSignupCreds=qn())}(r,t),function(t,a){var o=[T.ForceSignin,T.ForceSigninMobile,T.ForceSigninHost],r=-1!==i.utils.arrayIndexOf(o,ne)||ie,s=!r&&De&&De.length?D.Tiles:D.Username,c=s;switch(ne){case T.LWAConsent:s=c=D.LwaConsent;break;case T.Tiles:s=c=D.Tiles;break;case T.FedConflict:s=c=D.FedConflict;break;case T.ProofFedConflict:s=c=D.ProofFedConflict;break;case T.AadFedConflict:s=c=D.AadFedConflict;break;case T.FedLink:s=c=D.FedLink;break;case T.Win10Host_HIP_Login:case T.Win10Host_HIP_Login_PhoneSignIn:s=c=D.Password;break;case T.Fido:s=c=D.Fido;break;case T.UserCredentialPolicyBlocked:case T.CredentialPicker:s=c=D.CredentialPicker;break;case T.FedBoundLink:c=D.FedLink;break;case T.Signup:c=n.initialSharedData.availableSignupCreds.length>0?D.SignupCredentialPicker:D.SignupUsername;break;case T.SignupBlocked:c=D.SignupBlocked;break;case T.QrCodePin:c=D.QrCodeScan;break;default:var d=parseInt(Be)||D.Unknown;if(kn&&d===D.QrCodePin)c=D.QrCodeScan;else if(d!==D.Unknown&&d!==D.FetchSessionsProgress)c=d;else if(Le){var l=V.getResult(n.initialSharedData.otherIdpRedirectUrl,t,Le,a);switch(Tn&&l.sharedData&&l.sharedData.urlDeviceFingerprinting&&n.dfpUrl(l.sharedData.urlDeviceFingerprinting),l.action){case U.ShowError:c=ne===T.FetchSessionsProgress?D.FetchSessionsProgress:s;break;case U.SwitchView:i.utils.extend(n.initialSharedData,i.utils.extend(l.sharedData,l.viewParams||{})),c=ne===T.FetchSessionsProgress?D.FetchSessionsProgress:l.viewId;break;case U.Redirect:if(ne!==T.FetchSessionsProgress||!on){n.view_onRedirect({url:l.redirectUrl,eventOptions:{eventId:l.eventId}},l.redirectPostParams,l.isIdpRedirect);break}}}else ne===T.FetchSessionsProgress?e.fEnableBackButtonBugFix?s=c=D.FetchSessionsProgress:c=D.FetchSessionsProgress:be&&n.getServerError()&&(c=D.Username);ge||r||_e||ve?s=c:Ne&&(s=D.SignupUsername)}ln&&(s=c=D.RemoteConnectLocation),Sn&&(s=c=xn),n.initialViewId=s,n.currentViewId=c}(r,t)}function Hn(e,n){return y.format("./signin-{0}{1}.{2}",e,n?"-white":"",$?"svg":"png")}function Wn(e,n){switch(e.IdpType){case M.Google:return Hn("google",!1);case M.Facebook:return Hn("fluent-facebook",!1);case M.Apple:return Hn("apple",n);default:return Hn("externalidp",!1)}}function $n(e,n,t){var a=null,o=null,r=null,s=null,c=null,d=null;switch(e){case E.LinkedIn:a=Z.CT_PWD_STR_UseLinkedIn_Link,o=He,r=He,c=_(Hn("linkedin",!0)),d=_(Hn("linkedin",!1));break;case E.GitHub:a=Z.CT_PWD_STR_UseGitHub_Link,o=We,r=We,c=_(Hn("github",!0)),d=_(Hn("github",!1));break;case E.Google:a=n?Z.CT_STR_SignupCred_UseGoogle:Z.CT_PWD_STR_UseGoogle_Link,r=n?nn:$e,c=d=_(Hn("google",!1));break;case E.Facebook:a=n?Z.CT_STR_SignupCred_UseFacebook:Z.CT_PWD_STR_UseFacebook_Link,r=n?tn:Ge,c=d=Pn?_(Hn("fluent-facebook"),!1):_("./signin-facebook.png");break;default:wn&&t?(a=y.format(n?Z.CT_STR_SignupCred_UseExternalIdp:Z.CT_PWD_STR_UseExternalIdp_Link,t.DisplayName),r=n?t.IdpSignUpUrl:t.IdpSignInUrl,s=n?t.SignUpPostParams:t.SignInPostParams,c=_(Wn(t,!0)),d=_(Wn(t,!1))):(t=i.utils.arrayFirst(Cn,(function(n){return e===n.IdpType})))&&(a=y.format(n?Z.CT_STR_SignupCred_UseExternalIdp:Z.CT_PWD_STR_UseExternalIdp_Link,t.DisplayName),r=n?t.IdpSignUpUrl:t.IdpSignInUrl,c=d=_(Hn("apple",!1)))}return{text:a,signInUrl:o,redirectUrl:r,redirectPostParams:s,lightIconUrl:c,darkIconUrl:d,credType:e,testId:(n?"signupFedCredButton":"fedCredButton")+e}}function Gn(e){return{text:e?Z.CT_STR_SignupCredPicker_Title:Z.CT_PWD_STR_SwitchToCredPicker_Link_NoUser,lightIconUrl:_(Hn("options",!0)),darkIconUrl:_(Hn("options",!1)),testId:e?"signupOptions":"signinOptions"}}function qn(){if(_n){var e=je.indexOf(E.Google)>=0,n=je.indexOf(E.Facebook)>=0,t=[].concat(en?{credType:E.OtherMicrosoftIdpFederation,redirectUrl:en}:[],nn&&!e?{credType:E.Google,redirectUrl:nn}:[],tn&&!n?{credType:E.Facebook,redirectUrl:tn}:[]);return wn?i.utils.arrayForEach(Cn,(function(e){e.Promoted||t.push({credType:e.IdpType,redirectUrl:e.IdpSignUpUrl,redirectPostParams:e.SignUpPostParams,displayName:e.DisplayName,isExternalFederatedIdp:!0})})):i.utils.arrayForEach(Cn,(function(e){var n=e.IdpType;je.indexOf(n)<0&&t.push({credType:n,redirectUrl:e.IdpSignUpUrl})})),t}return[].concat(en?{credType:E.OtherMicrosoftIdpFederation,redirectUrl:en}:[],nn?{credType:E.Google,redirectUrl:nn}:[],tn?{credType:E.Facebook,redirectUrl:tn}:[])}function Kn(e,n){n?document.location.replace(e):document.location.assign(e)}function zn(e){if(Tn&&n.dfpUrl()){if(!e||!y.doOriginsMatch(n.dfpUrl(),e.origin))return;var t=null,i=e.data,a=s.DFPPrefix;if(i&&"string"==typeof i&&""!==i){if(0!==i.indexOf(a))return;t=i.substring(a.length),n.dfpResult(t)}}}function Qn(e){o.updateMergedBrandingObservables(n,e),o.updateFavicon(e,gn),o.createCustomizationLoader(a,e);var t=o.getPageBranding(e,ye,ke);n.backgroundControlMethods()&&n.backgroundControlMethods().updateBranding(t),n.bannerLogoUrl(t.bannerLogoUrl),n.backgroundLogoUrl(t.backgroundLogoUrl),n.useDefaultBackground(t.useDefaultBackground),n.loadBannerLogo(n.paginationControlHelper.showLogo())}n.learnMore=null,n.initialViewId=null,n.currentViewId=null,n.postCanaryValidationAction=null,n.initialSharedData={},n.prefillNames=[],n.agreementType=null,n.asyncTileRequestCount=0,n.useCssAnimations=!1,n.sessionPullType=Je,n.isDebugTracingEnabled=Xe,n.showFedCredAndNewSession=!0,n.paginationControlMethods=i.observable(),n.backgroundControlMethods=i.observable(),n.learnMoreMethods=i.observable(),n.instrumentationMethods=i.observable(),n.footerMethods=i.observable(),n.debugDetailsMethods=i.observable(),n.asyncInitReady=i.observable(!1),n.initializeComplete=i.observable(!1),n.ctx=i.observable(),n.postUrl=i.observable(),n.userClickedCentipede=i.observable(!1),n.pageSubmitted=i.observable(!1),n.forceSubmit=i.observable(!1),n.ariaHidden=i.observable(!1),n.wasLearnMoreShown=i.observable(!1),n.postRedirect=i.observable(),n.postedLoginStateViewId=i.observable(),n.postedLoginStateViewRNGCEntropy=i.observable(),n.postedLoginStateViewRNGCDefaultType=i.observable(),n.postedLoginStateViewRNGCSLK=i.observable(),n.password=i.observable(),n.isRequestPending=i.observable(!1),n.showLightboxProgress=i.observable(!1),n.bannerLogoUrl=i.observable(),n.backgroundLogoUrl=i.observable(),n.useDefaultBackground=i.observable(!1),n.newSession=i.observable(),n.fadeInLightBox=i.observable(!1),n.activeDialog=i.observable(),n.isFidoSupported=i.observable(!1).extend({logValue:m.getPropertyLogOption(n,{eventId:b.ClientTracingEventIds.PropertyValue_LoginPaginatedPageView_IsFidoSupported,tracingChange:!1})}),n.showDebugDetails=i.observable(!1),n.isSignupPost=i.observable(!1),n.isRecoveryAttemptPost=i.observable(!1),n.targetCredentialForRecovery=i.observable(),n.loadBannerLogo=i.observable(!1),n.availableCredsWithoutUsername=i.observableArray([]),n.paginationControlHelper=new v(a,n.paginationControlMethods,n.backgroundLogoUrl),n.livePreviewBranding=i.observable(),n.isLoginPageHidden=i.observable(!1),n.dfpUrl=i.observable(null),n.dfpResult=i.observable(null),n.stringCustomizationObservables=new h(Y),n.animate=i.utils.extend(i.observable(A.None),{isSlideOutNext:i.pureComputed((function(){return n.animate()===A.SlideOutNext})),isSlideInNext:i.pureComputed((function(){return n.animate()===A.SlideInNext})),isSlideOutBack:i.pureComputed((function(){return n.animate()===A.SlideOutBack})),isSlideInBack:i.pureComputed((function(){return n.animate()===A.SlideInBack}))}),n.flowToken=i.pureComputed((function(){return K()||a.sFT})),n.newSessionInfo=i.pureComputed((function(){if(n.newSession()){var e={},t=n.newSession(),i=L.htmlUnescape(t.displayName),a=L.htmlUnescape(t.fullName),o=t.isSignedIn&&a;return o?(e.unsafe_newSessionFullName=o,e.unsafe_newSessionDisplayName=i):e.unsafe_newSessionFullName=i,e}return null})),n.showFedCredButtons=i.pureComputed((function(){return n.useCssAnimations?(n.animate(A.None),z()):Nn()})),n.showQrCodeSignInButton=!!kn&&i.pureComputed((function(){return n.useCssAnimations?(n.animate(A.None),J()):Fn()})),n.otherSigninOptions=i.pureComputed((function(){var e=[],t=[];if(!Un()||un)return null;_n?e=e.concat(je):Ve&&e.push(Ve),i.utils.arrayForEach(e,(function(e){t.push($n(e))})),wn&&i.utils.arrayForEach(Cn,(function(e){e.Promoted&&t.push($n(e.IdpType,!1,e))}));var a=n.availableCredsWithoutUsername().length>0||dn||fn,o=1===n.availableCredsWithoutUsername().length&&X();return cn&&(a||o)&&(_n?t.push(Gn()):t.push({text:Z.CT_PWD_STR_SwitchToCredPicker_Link_NoUser,lightIconUrl:_(Hn("options",!0)),darkIconUrl:_(Hn("options",!1)),testId:"signinOptions"})),t})),n.qrCodeSigninOption=kn?i.pureComputed((function(){return{text:Z.CT_STR_QrCodePin_Signin_ButtonText,lightIconUrl:_(Hn("qrpin",!0)),darkIconUrl:_(Hn("qrpin",!1)),testId:"signinQrCodePin",showHelpIcon:!0,helpText:Z.CT_STR_QrCodePin_Signin_EmployerDescription,showPreviewBadge:!0}})):null,n.showSignupFedCredButtons=i.pureComputed((function(){return n.useCssAnimations?(n.animate(A.None),Q()):Vn()})),n.otherSignupOptions=i.pureComputed((function(){var e=[],n=[];return hn||Mn()?(e=e.concat(je),i.utils.arrayForEach(e,(function(e){n.push($n(e,!0))})),wn&&i.utils.arrayForEach(Cn,(function(e){e.Promoted&&n.push($n(e.IdpType,!0,e))})),qn().length>0&&n.push(Gn(!0)),n):null})),n.asyncInitReady=i.pureComputed((function(){return n.stringCustomizationObservables.isCustomStringsLoadComplete()&&n.initializeComplete()})),n.dispose=function(){q&&q.removeListener(Qn)},n.getServerError=function(){var e=null,t=null;if(Ce)e=Ce;else if(Se&&Se.length)switch(Se[0]){case x.EmptyFields:case x.UsernameInvalid:case x.PP_E_MISSING_MEMBERNAME:case x.PP_E_NAME_INVALID:case x.PP_E_EMAIL_RIGHT_TOO_LONG:case x.PP_E_NAME_TOO_LONG:case x.PP_E_INVALID_PHONENUMBER:case x.PP_E_LIBPHONENUMBERINTEROP_NUMBERPARSE_EXCEPTION:e=Z.CT_PWD_STR_Error_InvalidUsername;break;case x.PP_E_EXCLUDED:e=Z.CT_PWD_STR_SSSU_Error_InvalidEmailOrPassword||ee.CT_PWD_STR_Error_WrongCreds;break;case x.PP_E_BAD_PASSWORD:case x.PP_E_PREVIOUS_PASSWORD:case x.PP_E_INVALID_MEMBERNAME:case x.PP_E_DB_MEMBERDOESNOTEXIST:case x.PP_E_PE_RULEFALSE:case w.InvalidUserNameOrPassword:case w.ProtectedKeyMisuse:case w.InvalidPasswordExpiredPassword:case w.UserAccountNotFound:case w.UserAccountDeleted:case w.UserAccountNotFoundNotConfiguredForRemoteNgc:case w.UserAccountNotFoundFailedToCreateRemoteSignIn:case w.UserUnauthorizedApiVersionNotSupported:case w.UserUnauthorizedMsaGuestUsersNotSupported:case w.InvalidTenantName:case w.InvalidTenantNameEmptyGuidIdentifier:case w.InvalidTenantNameEmptyIdentifier:case w.InvalidTenantNameFormat:case w.InvalidDomainName:e=Z.CT_PWD_STR_SSSU_Error_InvalidEmailOrPassword||ee[fe?"CT_IHL_STR_Error_WrongHip":"CT_PWD_STR_Error_WrongCreds"];break;case w.UserAlreadyExists:e=Z.CT_PWD_STR_SSSU_Error_EmailAccountExists;break;case w.UserUnauthorized:e=ee.CT_PWD_STR_Error_UsernameNotExist_Guest_SignupAllowed;break;case w.InvalidPassword:e=Z.CT_PWD_STR_Error_InvalidPassword;break;case x.PP_E_OLD_SKYPE_PASSWORD:e=Z.CT_IL_STR_Error_OldSkypePwd;break;case x.PP_E_ALIAS_AUTH_NOTPERMITTED:e=Z.CT_PWD_STR_Error_AliasNotAllowed;break;case x.PP_E_FEDERATION_INLINELOGIN_DISALLOWED:e=Z.CT_PWD_STR_Error_FedNotAllowed;break;case x.PasswordEmpty:case x.PP_E_MISSING_PASSWORD:case w.InvalidPasswordNullPassword:e=Z.CT_PWD_STR_Error_MissingPassword;break;case x.PP_E_IDP_LINKEDIN_BINDING_NOT_ALLOWED:e=Z.CT_STR_Error_FedUserNotFound_LinkedIn;break;case x.PP_E_IDP_GOOGLE_BINDING_NOT_ALLOWED:e=Z.CT_STR_Error_FedUserNotFound_Google;break;case x.PP_E_IDP_GITHUB_BINDING_NOT_ALLOWED:e=Z.CT_STR_Error_FedUserNotFound_GitHub;break;case x.PP_E_OTT_DATA_INVALID:case x.PP_E_OTT_ALREADY_CONSUMED:case x.PP_E_OTT_INVALID_PURPOSE:case x.PP_E_PPSA_RPT_NOTOADDRESS:case w.InvalidOneTimePasscode:case w.ExpiredOneTimePasscode:case w.OneTimePasscodeCacheError:case w.OneTimePasscodeEntryNotExist:case w.InvalidOneTimePasscodeOTPNotGiven:case w.PublicIdentifierSasEndCallNonRetriableError:case w.PublicIdentifierSasEndCallRetriableError:e=Z.CT_OTC_STR_Error_CodeIncorrect;break;case w.FlowTokenExpired:e=Z.CT_PWD_STR_Error_FlowTokenExpired;break;case w.IdsLocked:e=Z.CT_PWD_STR_SSSU_Error_AccountLocked||Z.CT_PWD_STR_Error_IdsLocked;break;case w.UserDisabled:case w.GuestUserDisabled:e=Z.CT_PWD_STR_Error_UserDisabled;break;case w.MissingCustomSigningKey:e=Z.CT_PWD_STR_Error_MissingCustomSigningKey;break;case w.BlockedAdalVersion:e=Z.CT_PWD_STR_Error_BlockedAdalVersion;break;case w.BlockedClientId:e=Z.CT_PWD_STR_Error_BlockedClientId;break;case w.UserAccountSelectionInvalid:e=Z.CT_PWD_STR_Error_SelectedAccountInvalid;break;case w.IdpLoopDetected:e=Z.CT_PWD_STR_Error_IdpLoopDetected;break;case w.InvalidPasswordLastPasswordUsed:e=Z.CT_PWD_STR_Error_LastPasswordUsed;break;case w.PhoneSignInBlockedByUserCredentialPolicy:e=Z.STR_UserCredentialPolicy_Blocked,t=Z.STR_UserCredentialPolicy_Blocked_PhoneSignIn_Remediation;break;case w.PublicIdentifierAuthUserNotAllowedByPolicy:e=Z.STR_UserCredentialPolicy_Blocked;break;case w.PasskeyAuthInterrupted:En&&(e=Z.CT_FIDO_STR_Page_PasskeyError);break;case w.FidoBlockedByPolicy:Dn?e=Z.CT_FIDO_STR_Page_PasskeyError_NotMetCriteria:(e=Z.STR_UserCredentialPolicy_Blocked,t=Z.STR_UserCredentialPolicy_Blocked_Fido_Remediation);break;case w.PasskeyBlockedByPolicyOtherPasskeyAvailable:e=Z.CT_FIDO_STR_Page_PasskeyError_UseOtherPasskey;break;case w.PasskeyBlockedByPolicyOtherAuthAppPasskeyAvailable:e=Z.CT_FIDO_STR_Page_PasskeyError_NotMetCriteria_MSAuthenticator;break;case w.UserAccountNotFoundForFidoSignIn:e=Z.CT_FIDO_STR_Error_NotFound;break;case w.AccessPassBlockedByPolicy:e=Z.CT_PWD_STR_Error_AccessPassBlocked;break;case w.InvalidAccessPass:e=Z.CT_PWD_STR_Error_IncorrectAccessPass;break;case w.AccessPassExpired:e=Z.CT_PWD_STR_Error_AccessPassExpired;break;case w.AccessPassAlreadyUsed:e=Z.CT_PWD_STR_Error_AccessPassAlreadyUsed;break;case w.CertificateValidationBlockedByPolicy:e=Z.STR_CertBaseAuthPolicy_Block;break;case w.InvalidCredentialDueToMfaClassification:case w.ProofupBlockedDueToMfaClassification:e=Z.STR_InvalidCredentialDueToMfaClassification;break;case w.QrPinInvalid:e=Z.CT_STR_QrCodePin_Signin_ErrorBadPin;break;case w.InvalidGrantQrPinChanged:e=Z.CT_STR_QrCodePin_Signin_ErrorExpiredPin;break;default:e=null}else if(Le){var i=V.getResult(n.initialSharedData.otherIdpRedirectUrl,n.initialSharedData.displayName,Le,!1);i.action===U.ShowError&&(e=i.error)}return e?new P(e,t):null},n.fetchSessions_onUpdateUserTiles=function(e,n){setTimeout((function(){An(e,n)}))},n.fetchSessions_onIncrementAsyncTileRequestCount=function(){n.asyncTileRequestCount++},n.fetchSessions_onDecrementAsyncTileRequestCount=function(){n.asyncTileRequestCount--},n.fetchSessions_onExecuteGctResult=function(){g.throwUnhandledExceptionOnRejection(u.all(H).then((function(){var e=V.getResult(n.initialSharedData.otherIdpRedirectUrl,L.htmlUnescape(ge),Le,n.isFidoSupported());switch(e.action){case U.ShowError:n.paginationControlMethods().view_onSwitchView(D.Username,!0);break;case U.SwitchView:n.paginationControlMethods().view_onSwitchView(e.viewId,!0);break;case U.Redirect:n.view_onRedirect({url:e.redirectUrl,eventOptions:{eventId:e.eventId}})}})))},n.paginationControl_onCancel=function(){if(ce&&le){var e=le;me&&(Ae?Ae.username=me:e=B.appendOrReplace(e,"username",me)),n.view_onRedirect(e,Ae)}else ue&&(n.view_onSetPendingRequest(!0),Kn(ue,!0))},n.view_onSubmitReady=function(e){var t=n.paginationControlMethods().getCurrentViewId(),i=n.paginationControlMethods().getSharedData();n.postedLoginStateViewRNGCDefaultType(i.remoteNgcParams.defaultType),n.postedLoginStateViewRNGCEntropy(i.remoteNgcParams.entropy),n.postedLoginStateViewRNGCSLK(i.remoteNgcParams.sessionIdentifier),n.isSignupPost(i.isSignupPost),bn?n.targetCredentialForRecovery(i.targetCredentialForRecovery):n.isRecoveryAttemptPost(i.isRecoveryAttemptPost),t===D.Hip&&(t=D.Password),n.postedLoginStateViewId(t),!K()&&i.flowToken&&K(i.flowToken);var a=(S.UserTracker||{}).destroy;if(a)try{a()}catch(o){}!function(e){try{var n=O.getObject("wlidperf");n.FR="L",n.ST=(new Date).getTime(),O.write("wlidperf",n,!0,!0,!0,e)}catch(t){}}(xe),n.instrumentationMethods().recordSubmit(),n.pageSubmitted(!0),n.forceSubmit(!0),n.isRequestPending(!0),n.showLightboxProgress(!e)},n.view_onRedirect=function(e,t,i,a){var o={postParams:t,isIdpRedirect:i,useViewProgress:a},r=m.logRedirection(e,o);i&&oe&&n.flowToken()&&O.write(oe,n.flowToken(),!se,!1,!1,!1,null,null,!0),t?n.postRedirect({url:r,postParams:t}):Kn(r),n.isRequestPending(!0),n.showLightboxProgress(!a)},n.view_onLoadView=function(e){var a=function(e){0;var n=[D.Password,D.QrCodePin,D.ProofConfirmation,D.OneTimeCode,D.OneTimeCodeRecovery,D.ConfirmSignup,D.ConfirmRecoverUsername,D.LearnMore,D.ResetPasswordSplitter,D.QrCodeScan,D.RemoteNGC,D.PhoneDisambiguation,D.IdpDisambiguation,D.IdpRedirect,D.IdpRedirectSpeedbump,D.ViewAgreement,D.ConfirmSend,D.CredentialPicker,D.Fido,D.FedConflict,D.ProofFedConflict,D.AadFedConflict,D.FedLink,D.RemoteConnectCanaryValidation,D.FetchSessionsProgress,D.Tiles,D.LwaConsent,D.Hip,D.RemoteLoginPolling,D.TenantDisambiguation,D.SearchOrganization,D.AccessPass,D.SignupUsername,D.SignupCredentialPicker,D.LearnMoreOfflineAccount,D.RemoteConnectLocation,D.WebNativeBridge,D.CertificateInterstitialView,D.SignupBlocked,D.VCPresentation];if(!i.utils.arrayFirst(n,(function(n){return e===n})))return null;return new u((function(n){switch(e){case D.Password:t.e(26).then(function(){t(505),n()}.bind(null,t))["catch"](t.oe);break;case D.QrCodePin:t.e(31).then(function(){t(506),n()}.bind(null,t))["catch"](t.oe);break;case D.ProofConfirmation:t.e(29).then(function(){t(507),n()}.bind(null,t))["catch"](t.oe);break;case D.OneTimeCode:case D.OneTimeCodeRecovery:t.e(25).then(function(){t(508),n()}.bind(null,t))["catch"](t.oe);break;case D.ConfirmSignup:t.e(6).then(function(){t(509),n()}.bind(null,t))["catch"](t.oe);break;case D.ConfirmRecoverUsername:t.e(4).then(function(){t(510),n()}.bind(null,t))["catch"](t.oe);break;case D.LearnMore:t.e(22).then(function(){t(511),n()}.bind(null,t))["catch"](t.oe);break;case D.ResetPasswordSplitter:t.e(36).then(function(){t(512),n()}.bind(null,t))["catch"](t.oe);break;case D.QrCodeScan:t.e(32).then(function(){t(513),n()}.bind(null,t))["catch"](t.oe);break;case D.RemoteNGC:t.e(35).then(function(){t(514),n()}.bind(null,t))["catch"](t.oe);break;case D.PhoneDisambiguation:t.e(27).then(function(){t(515),n()}.bind(null,t))["catch"](t.oe);break;case D.IdpDisambiguation:t.e(19).then(function(){t(516),n()}.bind(null,t))["catch"](t.oe);break;case D.IdpRedirect:t.e(20).then(function(){t(517),n()}.bind(null,t))["catch"](t.oe);break;case D.IdpRedirectSpeedbump:t.e(21).then(function(){t(518),n()}.bind(null,t))["catch"](t.oe);break;case D.ViewAgreement:t.e(41).then(function(){t(499),n()}.bind(null,t))["catch"](t.oe);break;case D.ConfirmSend:t.e(5).then(function(){t(519),n()}.bind(null,t))["catch"](t.oe);break;case D.CredentialPicker:case D.SignupCredentialPicker:t.e(7).then(function(){t(520),n()}.bind(null,t))["catch"](t.oe);break;case D.Fido:t.e(18).then(function(){t(521),n()}.bind(null,t))["catch"](t.oe);break;case D.FedConflict:t.e(15).then(function(){t(522),n()}.bind(null,t))["catch"](t.oe);break;case D.ProofFedConflict:t.e(30).then(function(){t(523),n()}.bind(null,t))["catch"](t.oe);break;case D.AadFedConflict:t.e(0).then(function(){t(524),n()}.bind(null,t))["catch"](t.oe);break;case D.FedLink:t.e(16).then(function(){t(525),n()}.bind(null,t))["catch"](t.oe);break;case D.RemoteConnectCanaryValidation:t.e(33).then(function(){t(526),n()}.bind(null,t))["catch"](t.oe);break;case D.RemoteConnectLocation:t.e(34).then(function(){t(527),n()}.bind(null,t))["catch"](t.oe);break;case D.FetchSessionsProgress:t.e(17).then(function(){t(528),n()}.bind(null,t))["catch"](t.oe);break;case D.Tiles:t.e(38).then(function(){t(529),n()}.bind(null,t))["catch"](t.oe);break;case D.LwaConsent:t.e(1).then(function(){t(530),n()}.bind(null,t))["catch"](t.oe);break;case D.Hip:t.e(1).then(function(){t(531),n()}.bind(null,t))["catch"](t.oe);break;case D.RemoteLoginPolling:t.e(10).then(function(){t(532),n()}.bind(null,t))["catch"](t.oe);break;case D.TenantDisambiguation:t.e(14).then(function(){t(533),n()}.bind(null,t))["catch"](t.oe);break;case D.AccessPass:t.e(9).then(function(){t(534),n()}.bind(null,t))["catch"](t.oe);break;case D.SignupUsername:t.e(13).then(function(){t(535),n()}.bind(null,t))["catch"](t.oe);break;case D.SearchOrganization:t.e(11).then(function(){t(536),n()}.bind(null,t))["catch"](t.oe);break;case D.LearnMoreOfflineAccount:t.e(23).then(function(){t(537),n()}.bind(null,t))["catch"](t.oe);break;case D.WebNativeBridge:t.e(43).then(function(){t(538),n()}.bind(null,t))["catch"](t.oe);break;case D.CertificateInterstitialView:t.e(2).then(function(){t(539),n()}.bind(null,t))["catch"](t.oe);break;case D.SignupBlocked:t.e(12).then(function(){t(540),n()}.bind(null,t))["catch"](t.oe);break;case D.VCPresentation:t.e(39).then(function(){t(541),n()}.bind(null,t))["catch"](t.oe)}}))}(e);return a?(n.view_onSetPendingRequest(!0),a.then((function(){n.view_onSetPendingRequest(!1)}))):null},n.view_onShow=function(e,t){mn?Sn&&Qn(n.livePreviewBranding()):Qn(e.dynamicBranding?n.paginationControlMethods().getSharedDataItem("userTenantBranding"):o.loadTenantBranding(we)),t!==D.Username&&n.newSession(null),c.length>0&&(t!==D.Username&&t!==D.Tiles||(t===D.Username&&n.newSession(c[0]),c=[]))},n.view_onRestoreIsRecoveryAttemptPost=function(){bn?n.paginationControlMethods().setSharedDataItem("targetCredentialForRecovery",E.None):n.paginationControlMethods().setSharedDataItem("isRecoveryAttemptPost",!1)},n.view_onUpdateFlowToken=function(e){K(e),n.paginationControlMethods().setSharedDataItem("flowToken",e)},n.view_onUpdateDFPUrl=function(e){Tn&&n.dfpUrl(e)},n.view_onUpdateAvailableCreds=function(e){n.availableCredsWithoutUsername(e),1===e.length&&X(!!e[0].shownOnlyOnPicker)},n.view_onUpdateRemoteNgcParams=function(e,t,i){var a=n.paginationControlMethods().getSharedDataItem("remoteNgcParams");a.sessionIdentifier=e,a.entropy=t,a.defaultType=i},n.view_onSetLightBoxFadeIn=function(e){n.fadeInLightBox(e)},n.view_onSetPendingRequest=function(e){n.isRequestPending(e),n.showLightboxProgress(e)},n.view_onSetLoginPageHiddenState=function(e){n.isLoginPageHidden(e||!1)},n.footer_agreementClick=function(e){n.agreementType=e,n.paginationControlMethods().view_onSwitchView(D.ViewAgreement)},n.closeDebugDetails_onClick=function(){Rn(!1),n.footerMethods()&&n.footerMethods().setDebugDetailsState(!1)},n.toggleDebugDetails_onClick=function(){Rn(!n.showDebugDetails(),!0)},n.setDebugTracing_onClick=function(){n.isDebugTracingEnabled=!n.isDebugTracingEnabled},n.learnMore_onShow=function(){n.ariaHidden(!0),n.wasLearnMoreShown(!0),n.learnMoreMethods().open()},n.learnMore_onHide=function(){n.ariaHidden(!1),n.paginationControlMethods().setDefaultFocus()},n.passwordView_onResetPassword=function(e){Kn(function(e,n,t){if(t)return B.appendOrReplace(e,n,encodeURIComponent(y.trim(t)));return e}(pe,Pe,e)),n.view_onSetPendingRequest(!0)},n.newSession_onClick=function(){var e=n.newSession();if(e.isOtherIdp){var t=y.trim(e.displayName),i=B.appendOrReplace(n.initialSharedData.otherIdpRedirectUrl,"username",encodeURIComponent(t));i=B.appendOrReplace(i,"login_hint",encodeURIComponent(t));var o=Ie?k.clone(Ie):null;o&&(o.username=t),n.view_onRedirect(i,o,!0)}else if(e.isWindowsSso){var r=new l(a);g.throwUnhandledExceptionOnRejection(r.loginWindowsUserAsync(e.ssoLink).then(null,(function(){return null})).then((function(e){e&&n.view_onRedirect(e)})))}},n.newSessionClose_onClick=function(){n.newSession(null)},n.otherSigninOptionsButton_onClick=function(e){"signinOptions"===e.testId?(n.paginationControlMethods().setSharedDataItem("availableCreds",n.availableCredsWithoutUsername()),pn&&n.paginationControlMethods().setSharedDataItem("useCredWithoutUsername",!0),n.paginationControlMethods().view_onSwitchView(D.CredentialPicker)):_n?wn?n.view_onRedirect(e.redirectUrl,e.redirectPostParams):n.view_onRedirect(e.redirectUrl):n.view_onRedirect(e.signInUrl)},n.otherSignupOptionsButton_onClick=function(e){"signupOptions"===e.testId?n.paginationControlMethods().view_onSwitchView(D.SignupCredentialPicker):wn?n.view_onRedirect(e.redirectUrl,e.redirectPostParams):n.view_onRedirect(e.redirectUrl)},n.qrCodePinSigninButton_onClick=function(e){"signinQrCodePin"===e.testId&&n.paginationControlMethods().view_onSwitchView(D.QrCodeScan)},n.qrCodePinHelpButton_onClick=function(){n.view_onShowDialog(s.DialogId.QrCodePinHelp)},n.paginationControl_onAnimationStateChange=function(e,t,i){var a=Nn(),o=Vn(),r=Fn();switch(e){case I.Begin:_n?(a||o)&&i&&n.animate(t?A.SlideOutBack:A.SlideOutNext):a&&i&&n.animate(t?A.SlideOutBack:A.SlideOutNext);break;case I.RenderNewView:z(!1),J(!1),_n&&Q(!1);break;case I.AnimateNewView:a&&(z(!0),n.animate(t?A.SlideInBack:A.SlideInNext)),_n&&o&&(Q(!0),n.animate(t?A.SlideInBack:A.SlideInNext)),r&&(J(!0),n.animate(t?A.SlideInBack:A.SlideInNext));break;case I.End:z(a),_n&&Q(o),J(r),n.animate(A.None)}},n.view_onRegisterDialog=function(e,n){j[e]={templateNodes:n.templateNodes,data:n.data}},n.view_onUnregisterDialog=function(e){delete j[e]},n.view_onShowDialog=function(e){return n.activeDialog(j[e]),setTimeout((function(){n.activeDialog(j[e])}),0),new u((function(e){G={resolve:e}}))},n.dialog_onClose=function(){n.activeDialog(null),n.paginationControlMethods().getCurrentView().viewInterface.setDefaultFocus(),G&&G.resolve()},n.dispose=function(){null},function(){o.createMergedBrandingObservables(n),mn&&(q=C.getInstance(mn)).addListener(In),Tn&&L.addEventListener(S,"message",zn);var t=o.loadTenantBranding(we);(o.createCustomizationLoader(a,t,Y,R.LoginPage),$=L.isSvgImgSupported(),Ye)&&(new d).writeCookie();V=new p(a,F.DisableDesktopSsoPreferredCred|F.DisableAutoSend|F.IsPostRequest);var s,c=he?he.split(","):[],b=L.htmlUnescape(be||me||ge||ve||"");c.sort(),n.prefillNames=n.prefillNames.concat(i.utils.arrayMap(c,L.htmlUnescape)),n.useCssAnimations=qe&&L.isCSSAnimationSupported(),n.ctx(re),n.postUrl((s=de,Te&&i.utils.objectForEach(y.doubleSplit(Te,"&","="),(function(e,n){s=B.addIfNotExist(s,e,n)})),s));var v=L.isFidoSupportedAsync(ze,yn).then((function(e){jn(e,b)}),(function(){jn(!1,b)}));H.push(v),H.push(m.createLoadClientTracingPromise());var h=new r(e);if(Qe){n.view_onSetPendingRequest(!0);var _=h.validateAsync().then(Bn,Ln);H.push(_)}if(!Ze&&te){var x=y.format("[{0}]",te),w=O.getObject("WLOpt"),k=w.act||"";-1===k.indexOf(x)&&(k+=x),w.act=k,O.write("WLOpt",w,!1,!0)}var P=n.asyncInitReady.subscribe((function(e){if(e){var t=new l(a);t.isEnabled()&&(n.asyncTileRequestCount++,g.throwUnhandledExceptionOnRejection(t.pullBrowserSsoCookieAsync().then(null,(function(){return null})).then((function(e){n.asyncTileRequestCount--,(e=e||{}).newSessions?An(f.parseBssoSessions(e.newSessions)):e.redirectUrl?Kn(e.redirectUrl,!0):An([])})))),P.dispose()}}));g.throwUnhandledExceptionOnRejection(u.all(H).then((function(){n.postCanaryValidationAction&&(n.postCanaryValidationAction.action===N.SwitchView&&(n.postCanaryValidationAction.viewId=n.currentViewId),W?n.initialViewId=n.currentViewId=W:n.postCanaryValidationAction.action===N.Redirect&&n.view_onRedirect(n.postCanaryValidationAction.redirectUrl,n.postCanaryValidationAction.redirectPostParams,n.postCanaryValidationAction.isIdpRedirect)),n.view_onSetPendingRequest(!1),m.setPageViewModel(n),n.initializeComplete(!0)})))}()}},function(e,n,t){var i={"./0-small.jpg":76,"./0.jpg":77,"./1-small.jpg":78,"./1.jpg":79,"./2-small.jpg":80,"./2.jpg":81,"./2.svg":82,"./3.jpg":83,"./3.svg":84};function a(e){var n=o(e);return t(n)}function o(e){if(!t.o(i,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=o,e.exports=a,a.id=75},function(e,n,t){e.exports=t.p+"content/images/backgrounds/0-small_380cedf8c7a693dca062a9823f836931.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/0_0758511e5c58ee74c0dce85535012acf.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/1-small_9051dd4c2265a7d1638893551bbfdbea.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/1_8c490b887821dad5a3d0a4c3c23e68eb.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/2-small_2055002f2daae2ed8f69f03944c0e5d9.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/2_6ffe0a92d779c878835b40171ffc2e13.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/2_11d9e3bcdfede9ce5ce5ace2d129f1c4.svg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/3_0e12844fbedda5507717aada81442842.jpg"},function(e,n,t){e.exports=t.p+"content/images/backgrounds/3_14dac81de7cff440337e4719b2fc0585.svg"},function(e,n,t){var i={"./0-small.jpg":86,"./0.jpg":87,"./1-small.jpg":88,"./1.jpg":89,"./10-small.jpg":90,"./10.jpg":91,"./11-small.jpg":92,"./11.jpg":93,"./13-small.jpg":94,"./13.jpg":95,"./14-small.jpg":96,"./14.jpg":97,"./15-small.jpg":98,"./15.jpg":99,"./16-small.jpg":100,"./16.jpg":101,"./17-small.jpg":102,"./17.jpg":103,"./18-small.jpg":104,"./18.jpg":105,"./19-small.jpg":106,"./19.jpg":107,"./2-small.jpg":108,"./2.jpg":109,"./20-small.jpg":110,"./20.jpg":111,"./21-small.jpg":112,"./21.jpg":113,"./22-small.jpg":114,"./22.jpg":115,"./23-small.jpg":116,"./23.jpg":117,"./24-small.jpg":118,"./24.jpg":119,"./25-small.jpg":120,"./25.jpg":121,"./26-small.jpg":122,"./26.jpg":123,"./27-small.jpg":124,"./27.jpg":125,"./28-small.jpg":126,"./28.jpg":127,"./29-small.jpg":128,"./29.jpg":129,"./3-small.jpg":130,"./3.jpg":131,"./30-small.jpg":132,"./30.jpg":133,"./31-small.jpg":134,"./31.jpg":135,"./32-small.jpg":136,"./32.jpg":137,"./33-small.jpg":138,"./33.jpg":139,"./34-small.jpg":140,"./34.jpg":141,"./35-small.jpg":142,"./35.jpg":143,"./36-small.jpg":144,"./36.jpg":145,"./37-small.jpg":146,"./37.jpg":147,"./38-small.jpg":148,"./38.jpg":149,"./39-small.jpg":150,"./39.jpg":151,"./4-small.jpg":152,"./4.jpg":153,"./40-small.jpg":154,"./40.jpg":155,"./41-small.jpg":156,"./41.jpg":157,"./42-small.jpg":158,"./42.jpg":159,"./43-small.jpg":160,"./43.jpg":161,"./44-small.jpg":162,"./44.jpg":163,"./45-small.jpg":164,"./45.jpg":165,"./46-small.jpg":166,"./46.jpg":167,"./47-small.jpg":168,"./47.jpg":169,"./48-small.jpg":170,"./48.jpg":171,"./49-small.jpg":172,"./49.jpg":173,"./5-small.jpg":174,"./5.jpg":175,"./50-small.jpg":176,"./50.jpg":177,"./51-small.jpg":178,"./51.jpg":179,"./52-small.jpg":180,"./52.jpg":181,"./53-small.jpg":182,"./53.jpg":183,"./54-small.jpg":184,"./54.jpg":185,"./55-small.jpg":186,"./55.jpg":187,"./56-small.jpg":188,"./56.jpg":189,"./57-small.jpg":190,"./57.jpg":191,"./58-small.jpg":192,"./58.jpg":193,"./59-small.jpg":194,"./59.jpg":195,"./6-small.jpg":196,"./6.jpg":197,"./60-small.jpg":198,"./60.jpg":199,"./61-small.jpg":200,"./61.jpg":201,"./62-small.jpg":202,"./62.jpg":203,"./63-small.jpg":204,"./63.jpg":205,"./64-small.jpg":206,"./64.jpg":207,"./65-small.jpg":208,"./65.jpg":209,"./66-small.jpg":210,"./66.jpg":211,"./67-small.jpg":212,"./67.jpg":213,"./68-small.jpg":214,"./68.jpg":215,"./69-small.jpg":216,"./69.jpg":217,"./7-small.jpg":218,"./7.jpg":219,"./70-small.jpg":220,"./70.jpg":221,"./71-small.jpg":222,"./71.jpg":223,"./72-small.jpg":224,"./72.jpg":225,"./73-small.jpg":226,"./73.jpg":227,"./74-small.jpg":228,"./74.jpg":229,"./75-small.jpg":230,"./75.jpg":231,"./76-small.jpg":232,"./76.jpg":233,"./77-small.jpg":234,"./77.jpg":235,"./78-small.jpg":236,"./78.jpg":237,"./79-small.jpg":238,"./79.jpg":239,"./8-small.jpg":240,"./8.jpg":241,"./9-small.jpg":242,"./9.jpg":243};function a(e){var n=o(e);return t(n)}function o(e){if(!t.o(i,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=o,e.exports=a,a.id=85},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/0-small_5d4456e6c77ecc776c7720cee0cef2a5.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/0_7fcf2bcda37e36f25865973c74f22a96.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/1-small_39e8b5b6ebf077db6fa4277f43db3bfb.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/1_586654bfc4439d17f33b8b0c0b5cda25.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/10-small_254152bf6053210ee2c66bab72871378.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/10_f30a8d58b4eb9fbeba84308e5baf3f3e.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/11-small_cd72f7e5d54c59759694726861a30a50.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/11_a7524b8738e0ed99a7febda78f103cf3.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/13-small_a6eb2d7a422339edeeac9d2d71954306.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/13_0c0d2a5f3c5e9f7e11f0eb52df83f635.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/14-small_49e2419c2aa8e314e00c9b515d80b87b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/14_19ef2544df978559954b976999f5c4f2.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/15-small_00eef12db8bf39049253d8059df50aba.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/15_81ca72c521ca7243acfbe98f409d2121.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/16-small_2b7ceefb73ce060053e835d6a0ad4c8e.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/16_9c6d0bfb0de9b3db6baff2bef2647d5e.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/17-small_425d73ab282445204ed617ff7a5aa851.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/17_c0767b2b240fe906dfc1425b0cd9714a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/18-small_9bde7ccd7b116e796caf52a162c5536b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/18_c3e14fbf8d731bbef9c70d84e772087a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/19-small_115e402596bc896ac251646d83843940.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/19_3ceac68634542865075613539fc7598b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/2-small_4437ac8ec06a0c35839a168f82a01ad8.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/2_0174ec19f4cbcfb33dabd21d0dc83fd4.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/20-small_8e7dfddb9910379a9eb38d77696343ed.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/20_0a6aabc80662384eb3ac03a2a773e35a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/21-small_648f360fee281b1886f9a6d10ae919c5.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/21_b8956410ca04a509596f3886dd9bc2eb.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/22-small_1857eebddd7e24bb6edfe023facff58c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/22_41d5893d0c9277368f67bdad036a793a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/23-small_057fd08982df21bea3d92f31022c42bc.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/23_1aa83b9ef7cd4250daa5b6560c9b3789.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/24-small_99231374f8d49a32dc9ece30f66c6d0d.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/24_08d78abf593a051422ca94472f764c8c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/25-small_b59a6fe48096a3701481af7c9fcd0020.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/25_22d52af7fcf1098512ad1418ed684797.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/26-small_9d578106328400096a0b056ba58396d9.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/26_581a0884d9c40d2e4ebdcbbad7aa4cdf.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/27-small_ce5eb0a282aa7434f8c029caf72bd891.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/27_460eb6af9d88d4edd18cf36b840c5b8f.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/28-small_d6527ab852f628aca2a13e9c5bf2e612.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/28_6bea45e16fba06af69920550a31b0855.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/29-small_9301ca7573ae678fdc911e560c610d3a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/29_0f2bdb1dec5d555fb4797931e9953912.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/3-small_52983a500cff6b9b638c0f338679d4aa.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/3_3c5b78a61e8d4cff7dd49c68e1488d89.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/30-small_91ebaa63b966ea4907c8c594ad15a898.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/30_0191cc145fa29340c75e649c6e2418ac.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/31-small_9eda353df627ac44dac6766a79e4db11.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/31_3a4b6f777029f1370049d3ff9a95134e.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/32-small_c049ee82c631ebe1490975fcd05c4b3c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/32_bee6b9fa76f4957237ac71baa9e9cd80.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/33-small_380cedf8c7a693dca062a9823f836931.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/33_0758511e5c58ee74c0dce85535012acf.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/34-small_aef9023405c0c213c56769d1d01a81a2.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/34_6243d715d2813f6bb01624233a590107.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/35-small_a8cb2087dbd0bd38cdfe053739c7f0d1.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/35_75de42d7117245312438569f4d8697ae.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/36-small_2d19c7bf9a9e9eeb493042395279b9c7.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/36_52ce6d30d180cb205d3cc8e9db07d36d.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/37-small_16fcbeec959a3ff8622437e2e9e2f94d.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/37_9246170513f917976b99fcaa7a0a082b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/38-small_d750036cf3a6ce6ab787c332c6fb07cb.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/38_061eb332a2535203df16e60359ed4f9a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/39-small_11cd33c0692d7358c9eb42d4f6726820.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/39_2df26c0be76b3a923777b76121448d43.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/4-small_2dbb4ab2440427ea802d21b89f61ee60.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/4_6d6e7191ec15ba6fdc16322d5dce5047.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/40-small_01c35e3806e6731909b11c6f297b0f38.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/40_576eefdeddab31bce15b4eca3f0c9214.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/41-small_accffa3b9c16f6cb82e22b6eb3c1146c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/41_68ed05d009ffad8e4d4ae0004ed4445b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/42-small_81b7fea2e3319ff966e79becd7bc471f.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/42_ce4bf834c819d41ec7b40f73f8d8ea7d.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/43-small_587f1dd89028101ffe198ed7276d4ac8.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/43_3975cb2a5adcc170a4b9c1cdda5c15c0.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/44-small_aef0872bdcd4af34783ae157abf7ff03.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/44_a42ad27f0f4330ebd32e356381e27f96.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/45-small_5d8c11ff20130bce0d101aa48c20ff3d.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/45_af497d2cd04e90e7d8c1f3afa7582aa5.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/46-small_8484f4bd6d6d8e77e963a66014ec2fb1.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/46_7083009d0fac287724255892c8de465a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/47-small_9051dd4c2265a7d1638893551bbfdbea.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/47_8c490b887821dad5a3d0a4c3c23e68eb.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/48-small_cfba65618c6ecb74e7a3e84f65a785ef.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/48_b2861e33a41805b7d6bbed5d3da103c7.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/49-small_2055002f2daae2ed8f69f03944c0e5d9.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/49_6ffe0a92d779c878835b40171ffc2e13.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/5-small_3c2ee0e954b269c527534bfd929ade23.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/5_c68eac4299cf3f5807f0b5c3e153dfe8.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/50-small_7bca530cc88e697e486e074b1bf5fbe6.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/50_3085cc4e48212ba80508d89e4a375fa5.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/51-small_3878aa04ae188bb343c312268bdac818.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/51_63295a11719580c46a2c64d3e807fb3c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/52-small_f21bff12e587ea14b736303bd01b7d98.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/52_a7d00b349e0d828926ad38ae8701f987.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/53-small_13599146e6ea127104726b119d909562.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/53_9d2658335b67a86dc7c32bb88cc70614.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/54-small_185b1ed48f0762ab412c80e5a6619c68.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/54_eb7f0f3350f2b16ac18cbdda1fad6839.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/55-small_9e2f458c4985fb5619b7a7f3175a3f9a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/55_6fae2ef56a16620324e4849e8e5d623a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/56-small_d82390c739bec2c3f68587277a5385e4.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/56_c76bd1a6f84c24f24690cd72d7be2eea.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/57-small_a23115889fdeb57d0a0639226f6a09ec.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/57_cd639d0f6a0124297bde62f2cb0dbc8c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/58-small_88b89b4ef6453e9cdfe485ce70b1a2b8.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/58_9104c93a82f2f5911a35dd28e29b2a3c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/59-small_67fb0648eef2c7d6dc63666e1a3e83d8.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/59_2f94fd546b2a37bdbecf8d82f306847f.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/6-small_48d4dbb57dbe1f73b3c22a0b3dab5f71.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/6_00c6fbca93a96d66b2a86cbbf2cba710.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/60-small_69245258d328bb225eeced459d831f93.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/60_c1b99679fe087e00fe861fdeb08627c9.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/61-small_757f90ffadf80a036dda3bf394904670.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/61_13fe3fcc35fa61db0a9fe8a68dbb2cc6.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/62-small_c6af465b4d81a3d2c45a3d87ac4a8e43.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/62_77659a542cadf70c85bcb2a430cc50a5.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/63-small_7dd8b9a27a3dc83ab961bb30e4463e1a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/63_0b47ebca221588da4b14dc4549a71e02.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/64-small_1808852e76ec90e46db4000c5892d28b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/64_c5b0045f959cb71dd7409aeb46440f4a.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/65-small_5bfbc2032395a0e3aae25d482704b018.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/65_691236e9d2daca22e08e8f1ad965722b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/66-small_5450fadad556161f8d74829ec5978c39.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/66_45562e1e9f34fe6c71981f4fff28ca18.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/67-small_321fc5184e6bf174aa3c73618cf8bd34.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/67_7ed93d7f51ae2a0d60456b38a31cac17.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/68-small_8ae3b94876b9f83d96006967028aae97.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/68_08b26936ef97646b6c9252569e99295b.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/69-small_9969ee59764b981ad7c44b29158735ad.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/69_9731eff061c0513218508860336abdb0.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/7-small_3a814db86e1898ada2246666cf999f8e.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/7_73e6edf4f0233e244b8c1da07f16c651.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/70-small_de560e94966e9e9e634163858272e237.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/70_b17d20c570cd3ae36f1035352a0bcf65.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/71-small_ce86b0e29dbb2e72263f50555c9783e6.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/71_1cf1aaa8a531c92b5a42044e9ff0b811.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/72-small_e41bb90bdcac04cea61c22b5a0c27e19.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/72_d78093235587a7fbf684f3a6cb9ff83c.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/73-small_75d4d21b4ef94e249f4e2effa9d2c46f.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/73_bd92ae7dc2af58d041f517ea3a6d7966.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/74-small_bf71514840b24f27389853e92b1cf806.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/74_6a0f014444a15c937e1c26a72323e59f.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/75-small_51b3a6ed891e8b69efd0a34e117bea3f.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/75_65199ef547273117c7be3efc4fdcd78e.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/76-small_849b8b86adeafeaddb66fc835395498d.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/76_a10096efb457b50ecad5a58a5c8b11b2.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/77-small_c4cfa229d6187e885653f7bc25ecf7f6.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/77_e2fb8f31492b877fc5b3ba83f1270218.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/78-small_0d65374f3b396abdc2d8f8782cd660a2.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/78_6dd318cbb1ecefc6cb8c97e266529d27.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/79-small_3fa37a72c58f18022ace5b0bd80091cb.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/79_caabb3368c08f3da21af981ae6321e51.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/8-small_22c33e4753b3d5f1fd09d1e5228a8a67.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/8_001e44f67ae4fbcd81364756231f6723.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/9-small_16d9cc8c9c9e768d6054097fa97aa6e7.jpg"},function(e,n,t){e.exports=t.p+"content/images/appbackgrounds/9_4c46ab8130e90e773932cc70662485bb.jpg"},function(e,n,t){var i={"./0.png":245,"./1.png":246,"./10.png":247,"./11.png":248,"./12.png":249,"./13.png":250,"./14.png":251,"./15.png":252,"./17.png":253,"./18.png":254,"./19.png":255,"./2.png":256,"./20.png":257,"./21.png":258,"./22.png":259,"./23.png":260,"./24.png":261,"./25.png":262,"./26.png":263,"./27.png":264,"./28.png":265,"./29.png":266,"./3.png":267,"./30.png":268,"./31.png":269,"./32.png":270,"./33.png":271,"./34.png":272,"./35.png":273,"./36.png":274,"./37.png":275,"./38.png":276,"./39.png":277,"./4.png":278,"./40.png":279,"./41.png":280,"./42.png":281,"./43.png":282,"./44.png":283,"./45.png":284,"./46.png":285,"./47.png":286,"./48.png":287,"./49.png":288,"./5.png":289,"./50.png":290,"./51.png":291,"./52.png":292,"./53.png":293,"./54.png":294,"./55.png":295,"./56.png":296,"./57.png":297,"./58.png":298,"./59.png":299,"./6.png":300,"./60.png":301,"./61.png":302,"./62.png":303,"./63.png":304,"./64.png":305,"./65.png":306,"./66.png":307,"./67.png":308,"./68.png":309,"./69.png":310,"./7.png":311,"./70.png":312,"./71.png":313,"./72.png":314,"./73.png":315,"./74.png":316,"./75.png":317,"./76.png":318,"./77.png":319,"./78.png":320,"./8.png":321};function a(e){var n=o(e);return t(n)}function o(e){if(!t.o(i,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=o,e.exports=a,a.id=244},function(e,n,t){e.exports=t.p+"content/images/applogos/0_bff11b327036a43323031c4c5471c360.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/1_96ac8a032139bad4fc93987066a7868a.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/10_e2417f16ca52bacbc310248661eafd1b.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/11_4c43e376f9582e3ed24a55488c374568.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/12_da47db10404a67a8cba2b38cc566e1aa.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/13_e26f753e3e01f7cbf36d679210747f56.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/14_d53f60eb435faf6992e530b6abd3e2b4.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/15_bea00e5df65d5df84916cbd730417b09.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/17_51e2d36bb3f20fbd95d946074449cd54.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/18_60705f776b9c8c5b75d5510c395ba986.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/19_afa34e6c3e3dd8c760d1efc9d084cdb5.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/2_54e378df68a0335a084b194cd1d2c00f.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/20_0b955e62d492e5b809d5e1ee8e0bb78b.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/21_df89d61ea851e26afd3b184174688194.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/22_2f98d06a761b9286bd3022a1d1d3823d.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/23_efd96060931e1822e2dab20baabd76a0.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/24_6edcfa2758c2a36bfb78486bc89f317f.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/25_5af3fa8cae10d62e7a0b169b82840017.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/26_517d267242d9f4332ec35a2fc4a301c2.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/27_2faea9d93785ed08c2de40e9399d4323.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/28_4e06e792dbd4e9775db04915861f651d.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/29_fdef0760cb68c38858b6a0bbe1cb390b.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/3_9ae0ebd77b279d438b54cf4c9150de42.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/30_98bd1fd7c88bad31c5e8f5bdfd6c33f4.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/31_598a4cc806c352fbc694d4f8aeb4b4b7.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/32_8bd1f0ba64d5d9f472124e965d8c3cf0.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/33_7a50918a52ca2cdb8c9adeaa019a5a62.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/34_881bff5dd6477a4af3c4233f02963d9f.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/35_44de4ce23fc094a007d49baf43029863.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/36_f806bf0765fb71c9e2113dc26ab149cf.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/37_6e8e0f96e2c89856c5cc8329a80ba00a.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/38_6629e1a57ee31be8fae6a7aefc66fc82.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/39_963c13e824a30ad00d4f4c2a7aa5d284.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/4_d1058a1004600a4633ad064de2c54e10.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/40_7ef267c8e58102beb3ecd5a1a98d830d.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/41_c82d852bbf1cd4befc16c9fd5c827378.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/42_632fd923e6d43f8acec8db62717c4199.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/43_90ed33369a22437a482a72e16026d2eb.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/44_456282d4ee7717e65374cee6b0b8f18a.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/45_fb686ff689edc6f7225ca334e2992a9c.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/46_0aa8adbace7d73a3c24ef1aa1480ec5f.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/47_0fd270f90f106cd10ae092de86b3c37b.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/48_f359bf7dd5bde79c9f0a3396ca50b677.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/49_96c71fab4d03ad23ff0f5a4f86c2ea04.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/5_da3b47ccdb5bc3ebafb4b79134162949.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/50_76411bd0a088dba2a7f0207144bb61a7.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/51_90ed33369a22437a482a72e16026d2eb.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/52_7b46f1b4626db3e8eb5591a67fb3e1f2.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/53_7a3c80bf9694448bac31a9589d2e9e92.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/54_b6ad512021fb5eb8743feeabe35ce979.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/55_ead0587a6c6aa225410fe888bd9bbe45.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/56_7729d29b2e3f96cf32a2e4c7623688b6.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/57_2bb86bdcf71efa47c67a2adc5fa8c085.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/58_ec233f7ee50d57779a49b7c41291ffb4.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/59_9088a1f7a97752f7119d2c9e944dac4e.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/6_90a03609c7ad30e9313dd6864baa65f4.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/60_7e0ab1be3d3ba2f7b37f86650cfd3b98.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/61_5047eeaaf7769c7427bf077c78593309.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/62_d0161a343bb232c06895f99a601233a4.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/63_9d2d57e5a36eec77aeddb51b68f33816.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/64_bae06b00eaeb0c75f1d54807d1cd3062.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/65_3d0de9c6c38f8c94b77879645f24aafc.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/66_7673c62dd68db005e9e500c5c3a2cd99.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/67_8b464453129d8afe7ffcfe7d317e6c2f.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/68_70690862183cfb3d350612217512d8da.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/69_6adec72f5684339231a50484e119971b.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/7_53d6dd314614c78bdf07806bb009a0dc.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/70_312ea75ebe59a4062ddbd73f18b51cc3.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/71_8340e1b8638554bd041146c9bc1909d0.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/72_478c1f0780a9e6765de262f735efadcf.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/73_ef9a283dfe21abb4c5bc64347702f90b.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/74_c516ae647ad3e18bb1a705a0c01c0110.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/75_8881e946d6b24771cb9c979e18f682cf.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/76_34f637dca8d959b9d1394ceca5a52b29.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/77_34f637dca8d959b9d1394ceca5a52b29.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/78_a924026c6f3a7a296533b33a04fdf1be.png"},function(e,n,t){e.exports=t.p+"content/images/applogos/8_b9f4fbea3839b9d9f4b69f2074518068.png"},function(e,n,t){var i=t(1),a=i.Helper,o=i.Cookies,r=0,s=1;e.exports=function(){var e=r;this.writeCookie=function(){o.write("brcap",e,!0,!0)},a.isOnTouchStartEventSupported()&&(e|=s)}},function(e,n,t){var i=t(10),a=t(1),o=t(5),r=window,s=r.document,c=a.QueryString,d=a.Cookies,l=-**********;function u(e,n,t,i,a){var d="53ee284d-920a-4b59-9d30-a60315b26836",p=e,f=n,g=t,m=i,b=a,v={},h=0,_=null,C=null,S=null,x=null;function w(e){if(e.source===r){var n=e.data,t=n&&n.channel,i=n&&n.responseId,a=n&&n.body,o=a&&a.method;if(t===d&&i&&("CreateProviderAsync"===o||"Response"===o)){p.logMessage("Received message for method "+o);var s=v[i];delete v[i],setTimeout((function(){s(a.response||{})}),0)}}}function y(e,n){return new o((function(t){var i={channel:d,responseId:++h,body:e};n&&(i.extensionId=n),v[i.responseId]=t,p.logMessage("Sending message for method "+(e||{}).method||!1),r.postMessage(i,"*")})).then((function(e){return"Success"===e.status?o.resolve(e.result||{}):o.reject(new u.Error(e.code,e.description,e.ext))}))}function k(){return x||(r.addEventListener&&r.addEventListener("message",w),p.logMessage("Creating ChromeBrowserCore provider"),x=y({method:"CreateProviderAsync",response:{status:"Success"}}).then(P)),x}function P(){for(var e=null,n=_.firstChild;n;)!n.id||null!==e&&"ppnbnpeolgkicgegkbkbjmhlideopiji"!==n.id||(e=n.id),_.removeChild(n),n=_.firstChild;if(!e)throw new u.Error("NoExtension","Extension is not installed.",null);return p.logDataPoint("extension.id",e),p.logMessage("Using Chrome extension with id "+e),e}function T(e){return"OSError"===e.code&&e.externalData&&e.externalData.error===l?(p.logMessage("GetCookies method not found, falling back to GetCookie"),k().then((function(e){return y({method:"GetCookie",uri:C},e)}))):o.reject(e)}function D(e){var n=e.response||[];if(n&&n.length)for(var t=0,i=n.length;t<i;++t){var a=a=n[t].data||"",o=a.indexOf(";");-1!==o&&(a=a.substr(0,o)),n[t].data=a+S}return n}this.getCookiesAsync=function(){return k().then((function(e){return p.logMessage("Pulling SSO cookies"),y({method:"GetCookies",uri:C},e).then(null,T).then(D)}))},function(){if(C=c.appendOrReplace(r.location.href,"sso_nonce",f),b){var e=c.parse(C);e.fragment&&(e.fragment=null,C=c.join(e))}S="; path=/; domain="+g+(m?"":"; secure");(_=s.getElementById("ch-53ee284d-920a-4b59-9d30-a60315b26836"))||((_=s.createElement("div")).id="ch-53ee284d-920a-4b59-9d30-a60315b26836",s.body.appendChild(_))}()}u.Error=function(e,n,t){var a=e,o=n,r=t;this.code=a,this.description=o,this.externalData=r,this.toString=function(){var e="ChromeBrowserCore error "+(a||"")+": "+(o||"");return r&&(e+=" (ext: "+i.stringify(r)+")"),e},this.toCookieString=function(){var e="NA";if(d.isCookieSafeValue(a)){e+="|"+a;var n=r?encodeURIComponent(i.stringify(r)):null;n&&d.isCookieSafeValue(n)&&(e+="|"+n)}return e}},e.exports=u},function(e,n,t){var i=t(1),a=t(5),o=window,r=i.Cookies;e.exports=function(e,n,t,i){var s=e,c=n,d=t,l=i;function u(e,n,t){var i=(new Date).getTime();return function(e,n,t){return new a((function(n,t){o.navigator.msLaunchUri(e,n,t),setTimeout((function(){t("timeout")}),c)})).then((function(){var e=(new Date).getTime()-t;s.logDataPoint("msLaunchUri.success.ms",e),s.logMessage(n+" initiated successfully (took "+e+" ms)")}),(function(e){if("timeout"===e)s.logDataPoint("msLaunchUri.response",l?"timeout":"timeout-continue"),s.logMessage("");else{var i=(new Date).getTime()-t;s.logDataPoint("msLaunchUri.failure.ms",i),s.logMessage(n+" was NOT initiated successfully (took "+i+" ms)")}if("timeout"!==e||l)return a.reject(e||"noHandler")}))}(e,n,i).then((function(){return function(e,n,t){return new a((function(i,a){var o=setInterval((function(){var e=t?null:r.getCookie("ESTSUSERLIST"),a=r.getCookie("ESTSSSO");(e||a)&&(clearInterval(o),s.logDataPoint((e?"ESTSUSERLIST":"ESTSSSO")+".cookie.ms",(new Date).getTime()-n),e?(s.logMessage("Users list cookie detected"),r.remove("ESTSUSERLIST"),i({userList:decodeURIComponent(e).replace(/\+/g," ")})):i({reload:!0}))}),250);setTimeout((function(){clearInterval(o),s.logDataPoint("TB.response.timeout.ms",(new Date).getTime()-n),s.logMessage(e+" timed out."),a("timeout")}),d)}))}(n,i,t)}))}this.pullBrowserSsoCookieAsync=function(e){return u(e,"cookie pull",!1)},this.loginWindowsUserAsync=function(e){return u(e,"Windows user login",!0)}}},function(e,n,t){var i=t(2);e.exports=function(e){this.isCustomStringsLoadComplete=i.pureComputed((function(){return!!e()&&e().strings.isLoadComplete()})),this.customStrings=i.pureComputed((function(){return e()?e().strings():null})),this.isCustomStringsLoadFailure=i.pureComputed((function(){return!!e()&&e().strings.isLoadFailure()}))}},function(e,n,t){var i={"./signin-apple-white.png":327,"./signin-apple-white.svg":328,"./signin-apple.png":329,"./signin-apple.svg":330,"./signin-externalidp.png":331,"./signin-externalidp.svg":332,"./signin-facebook.png":333,"./signin-fluent-facebook.png":334,"./signin-fluent-facebook.svg":335,"./signin-github-white.png":336,"./signin-github-white.svg":337,"./signin-github.png":338,"./signin-github.svg":339,"./signin-google.png":340,"./signin-google.svg":341,"./signin-linkedin-white.png":342,"./signin-linkedin-white.svg":343,"./signin-linkedin.png":344,"./signin-linkedin.svg":345,"./signin-microsoft.png":346,"./signin-microsoft.svg":347,"./signin-options-white.png":348,"./signin-options-white.svg":349,"./signin-options.png":350,"./signin-options.svg":351,"./signin-qrpin-white.png":352,"./signin-qrpin-white.svg":353,"./signin-qrpin.png":354,"./signin-qrpin.svg":355};function a(e){var n=o(e);return t(n)}function o(e){if(!t.o(i,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=o,e.exports=a,a.id=326},function(e,n,t){e.exports=t.p+"content/images/signin-apple-white_8c11f236d94aaf10ea5bbadb60261b5e.png"},function(e,n,t){e.exports=t.p+"content/images/signin-apple-white_9b335c3db49166731203f4a13b54ff0d.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-apple_44a5055eff09458a63298e94a7249a5e.png"},function(e,n,t){e.exports=t.p+"content/images/signin-apple_6da32812e06ab70359ed196b95abee36.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-externalidp_736ef49702713af6b2c9e4fcd801595d.png"},function(e,n,t){e.exports=t.p+"content/images/signin-externalidp_a5d560a525c6bc9f86e64daf138782df.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-facebook_552fdb55ef40219207da91b311c19355.png"},function(e,n,t){e.exports=t.p+"content/images/signin-fluent-facebook_8455cf37a6ca09f8e35b4eb4aa4aa7fb.png"},function(e,n,t){e.exports=t.p+"content/images/signin-fluent-facebook_8c01e9c96a29fa274fca2ca0b49b7036.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-github-white_4c773a3d197b4a6378c1a95429f3c73e.png"},function(e,n,t){e.exports=t.p+"content/images/signin-github-white_9e4e66cecc5e003ce7c5300842f671b0.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-github_4ef82c475d937f4b4517751df28e81f3.png"},function(e,n,t){e.exports=t.p+"content/images/signin-github_4f133e101999f0f9f726427324505c7b.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-google_a158303d43f04806db86ebb01b1591ee.png"},function(e,n,t){e.exports=t.p+"content/images/signin-google_c033a1a3fb4ea8dfe7f1c12958b8249f.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-linkedin-white_a1a2301a9649c6c06ae16e4dc42ef2a9.png"},function(e,n,t){e.exports=t.p+"content/images/signin-linkedin-white_70d8fb9a6b278c30d41a7dd033e08ed0.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-linkedin_21b537bad213a03580f7d13a16309558.png"},function(e,n,t){e.exports=t.p+"content/images/signin-linkedin_6afddacf7df687b5dd3a26cef7b56ecf.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-microsoft_b91bd31143d61e5158be475b1cf7bbf4.png"},function(e,n,t){e.exports=t.p+"content/images/signin-microsoft_c5858686e81e3117a3249335e5dc539b.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-options-white_385f1fb0a28c5a4381ed1b04d364c491.png"},function(e,n,t){e.exports=t.p+"content/images/signin-options-white_c622ccc6d25829f07be905b37a75ba97.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-options_e9cb891bd21aeac4606567621d11b95f.png"},function(e,n,t){e.exports=t.p+"content/images/signin-options_3e3f6b73c3f310c31d2c4d131a8ab8c6.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-qrpin-white_97ee76155bdef68d51f882d111ce1a79.png"},function(e,n,t){e.exports=t.p+"content/images/signin-qrpin-white_33f29ddd025aa33fa298befbb72a24a7.svg"},function(e,n,t){e.exports=t.p+"content/images/signin-qrpin_4a5523d6de79a0f34c943d6029ea5168.png"},function(e,n,t){e.exports=t.p+"content/images/signin-qrpin_ada5adc951d9c983f746704ac9363507.svg"},function(e,n,t){var i=t(10),a=t(1),o=t(3),r=window,s=a.Helper,c=o.Array;function d(e){var n=e||[],t=[],a=!1;function o(e){if(r.opener){var t=i.stringify({messageType:"BrandingLivePreviewConnect",isOpen:e});c.forEach(n,(function(e){r.opener.postMessage(t,e)}))}}function d(e){if(a=e.origin,c.first(n,(function(e){return e===a}))){var a,o;try{o=i.parse(e.data)||{}}catch(r){return}"BrandingLivePreviewUpdate"===o.messageType&&c.forEach(t,(function(e){e(o.tenantBranding||{})}))}}this.addListener=function(e){t.push(e),a||(s.addEventListener(r,"message",d),o(!0),a=!0)},this.removeListener=function(e){c.removeItem(t,e),a&&0===t.length&&(s.removeEventListener(r,"message",d),o(!1),a=!1)}}var l=null;n.getInstance=function(e){return l=l||new d(e)}},function(e,n){var t=window;t.Telemetry=t.Telemetry||{},t.Telemetry.EClientEvent={Account_Signup_SwitchSignupType:1e4,Account_Signup_MemberName_ValidationError:10001,Account_ResetPW_SeeMoreVerificationOptions:11e3,Account_ResetPW_SelectedVerificationOption:11001,Account_ResetPW_OTT_ValidationError:11002,Account_ResetPW_SeeMoreSecondaryAction:11003,Account_Signup_Phone:11004,Account_Signup_EASI:11005,Account_Signup_Live:11006,Account_DeviceFingerPrinting_Iframe_Load:11007,Signin_Email_Phone_Skype:11008,Signin_Submit:11009,Signup_ChinaPIPLConsent_UserAction:11010,Account_Arkose_Iframe_Load:11011,Account_Arkose_Iframe_Load_Flavor:11012,Account_Arkose_Iframe_Solved:11013,Account_Arkose_Iframe_Solved_Flavor:11014,ManageNames_AddPhone_Step:11015,Account_AliasAccrual_Submit:11100,Account_AliasAccrual_Cancel:11101,Account_AliasAccrual_Skip:11102,Account_AliasAccrual_Suggestions:11103,Account_ManageProofsV2_AddPasskey:11200,Account_ManageProofsV2_PasskeyTryAgain:11201,Account_RemovePasskey:11202,Account_SelectAddressableProof:11203,PrefillPasskeyName:11204,Account_JavascriptEncryptor_Invoke:11205,Account_Human_Iframe_Load:11206}},function(e,n,t){(function(e){var t,i,a,o;function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window,o=function(){return function(e){function n(n){for(var t,a,o=n[0],r=n[1],s=0,c=[];s<o.length;s++)a=o[s],Object.prototype.hasOwnProperty.call(i,a)&&i[a]&&c.push(i[a][0]),i[a]=0;for(t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);for(d&&d(n);c.length;)c.shift()()}var t={},i={1:0,0:0};function a(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,a),i.l=!0,i.exports}a.e=function(e){var n=[],t=i[e];if(0!==t)if(t)n.push(t[2]);else{var o=new Promise((function(n,a){t=i[e]=[n,a]}));n.push(t[2]=o);var r,s=document.createElement("script");s.charset="utf-8",s.timeout=120,a.nc&&s.setAttribute("nonce",a.nc),s.src=a.p+"oneDs_f2e0f4a029670f10d892.js";var c=new Error;r=function(n){s.onerror=s.onload=null,clearTimeout(d);var t=i[e];if(0!==t){if(t){var a=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;c.message="Loading chunk "+e+" failed.\n("+a+": "+o+")",c.name="ChunkLoadError",c.type=a,c.request=o,t[1](c)}i[e]=void 0}};var d=setTimeout((function(){r({type:"timeout",target:s})}),12e4);s.onerror=s.onload=r,document.head.appendChild(s)}return Promise.all(n)},a.m=e,a.c=t,a.d=function(e,n,t){a.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,n){if(1&n&&(e=a(e)),8&n)return e;if(4&n&&"object"==r(e)&&e&&e.__esModule)return e;var t=Object.create(null);if(a.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)a.d(t,i,function(n){return e[n]}.bind(null,i));return t},a.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(n,"a",n),n},a.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},a.p="",a.oe=function(e){throw console.error(e),e};var o=window.telemetry_webpackJsonp=window.telemetry_webpackJsonp||[],s=o.push.bind(o);o.push=n,o=o.slice();for(var c=0;c<o.length;c++)n(o[c]);var d=s;return a(a.s=7)}([function(e,n){e.exports={ReportEventIdAttr:"data-report-event",ReportEventValueAttr:"data-report-value",ReportEventTriggerAttr:"data-report-trigger",ReportEventValueAttrBinding:"attr:",ReportEventHandlerAttachedAttr:"data-report-attached",DataViewId:"data-viewid",NonIndexedDataPointKey:"Data",Click:"click",Dblclick:"dblclick",Keypress:"keypress",Cut:"cut",Copy:"copy",Paste:"paste",Change:"change",Focus:"focus",Scroll:"scroll",Submit:"submit",Reset:"reset",Input:"input",A:"a",Radio:"radio",Checkbox:"checkbox",Button:"button",ExceptionData:"ExceptionData",PageActionData:"PageActionData",ExceptionsTablesuffix:"Exceptions",PageActtionsTablesuffix:"UserActions"}},function(e,n){var t;!function(e){e.AddListener=function(e,n,t){if(e&&"function"==typeof e.addEventListener)e.addEventListener(n,t);else{if(!e||!e.attachEvent)throw"Browser supports neither addEventListener nor attachEvent";e.attachEvent("on"+n,t)}},e.GenerateGUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var n=Math.floor(Math.random()*Math.floor(16))+0;return"y"===e&&(n&=11),n.toString(16)}))},Object.keys||(Object.keys=function(){"use strict";var e=Object.prototype.hasOwnProperty,n=!{toString:null}.propertyIsEnumerable("toString"),t=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],i=t.length;return function(a){if("function"!=typeof a&&("object"!=r(a)||null===a))throw new TypeError("Object.keys called on non-object");var o,s,c=[];for(o in a)e.call(a,o)&&c.push(o);if(n)for(s=0;s<i;s++)e.call(a,t[s])&&c.push(t[s]);return c}}()),Array.prototype.indexOf||(Array.prototype.indexOf=function(e,n){if(null==this)throw new TypeError("'this' is null or not defined");var t=this.length>>>0;for(n=+n||0,Math.abs(n)===1/0&&(n=0),n<0&&(n+=t)<0&&(n=0);n<t;n++)if(this[n]===e)return n;return-1})}(t||(t={})),n.AddListener=t.AddListener,n.GenerateGUID=t.GenerateGUID},function(e,n,t){var i,a=this&&this.__assign||function(){return(a=Object.assign||function(e){for(var n,t=1,i=arguments.length;t<i;t++)for(var a in n=arguments[t])Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a]);return e}).apply(this,arguments)},o=window.ServerData||{},r=window.document.querySelector("script[nonce]"),s=r?r.nonce||r.getAttribute("nonce"):void 0;t.nc=s,t.p=(o.urlCdn||o.a)+"content/js/",function(e){var n=t(0),i=t(1),o=window,r=function(){function e(e){this._preSendHandlers=[],this._eventProvider=e}return e.prototype.initialize=function(e){return this._eventApiConfig=e,this._eventDict={},this._eventProvider.initialize(e)},e.prototype.set=function(e,n,t){void 0===t&&(t="data"),void 0===this._eventDict[t]&&(this._eventDict[t]={}),null!=n&&(this._eventDict[t][e]=n)},e.prototype.get=function(e,n){return void 0===n&&(n="data"),this._eventDict[n][e]},e.prototype.hasEvents=function(){return this._numEvents()>0},e.prototype.hasEvent=function(e,n){return void 0===n&&(n="data"),void 0!==this._eventDict[n][e]},e.prototype.clear=function(){this._eventDict={}},e.prototype.post=function(e,n){for(var t=0,i=this._preSendHandlers;t<i.length;t++)(0,i[t])(n);if(this.hasEvents()){var a=[];if(e){var o=this._eventDict.data;delete this._eventDict.data,a=[o,this._eventDict.cloud,this._eventDict.app],this.validKeysPresent(a)&&this._eventProvider.post({name:e,data:o,ext:this._eventDict})}else a=[this._eventDict.cloud,this._eventDict.app],this.validKeysPresent(a)&&this._eventProvider.post(this._eventDict);this.clear()}},e.prototype.validKeysPresent=function(e){for(var n=/^\w+$/,t=0;t<e.length;t++)for(var i in e[t])if(e[t].hasOwnProperty(i)&&!n.test(i))return!1;return!0},e.prototype.addPreSendHandler=function(e,n){this._preSendHandlers.push((function(t){e(n,t)}))},e.prototype._numEvents=function(){return Object.keys(this._eventDict).length},e}();e.EventApi=r;var c=function(){function e(){this._iduxProviderUrl="https://js.monitor.azure.com/scripts/c/ms.analytics-web-2.min.js",this._isAppInsightsLoaded=!1,this._pendingEventQueue=[]}return e.prototype.initialize=function(e){var n=this,a=this;return this._args=e,o.Telemetry.appInsights?(a._isAppInsightsLoaded=!0,!0):(this._args.providerUrl?a._checkIfOneDsScriptExists(this._args.providerUrl)?a._initializeAppInsights(new o.oneDS.AppInsightsCore,new o.oneDS.PostChannel,new o.oneDS.PropertiesPlugin,new o.oneDS.ApplicationInsights):i.AddListener(o,"load",(function(){var e=o.document.createElement("script");e.src=n._args.providerUrl||n._iduxProviderUrl,e.type="text/javascript",s&&e.setAttribute("nonce",s),i.AddListener(e,"load",(function(){a._initializeAppInsights(new o.oneDS.AppInsightsCore,new o.oneDS.PostChannel,new o.oneDS.PropertiesPlugin,new o.oneDS.ApplicationInsights)})),o.document.body.appendChild(e)})):t.e(2).then(function(){var e=t(3),n=t(4),i=t(6),o=t(5);a._initializeAppInsights(new e.AppInsightsCore,new n.PostChannel,new o.PropertiesPlugin,new i.ApplicationInsights)}.bind(null,t))["catch"](t.oe),!0)},e.prototype.post=function(e){this._isAppInsightsLoaded?this._postToAppInsights([e]):this._pendingEventQueue.push(e)},e.prototype._postToAppInsights=function(e){for(var n=0,t=e;n<t.length;n++){var i=t[n];this._isAppInsightsManager(o.Telemetry.appInsights)?(this._appInsights||(this._appInsights=o.Telemetry.appInsights.newInst(this._args.appInsightsConfig.instrumentationKey,[],[])),this._appInsights.track(i)):o.Telemetry.appInsights.track(i)}},e.prototype._initializeAppInsights=function(e,n,t,i){o.Telemetry.appInsights||(this._args.appInsightObject?o.Telemetry.appInsights=this._args.appInsightObject:o.Telemetry.appInsights=this._createAppInsightsCore(e,n,t,i)),this._isAppInsightsLoaded=!0,this._pendingEventQueue.length>0&&this._postToAppInsights(this._pendingEventQueue)},e.prototype._checkIfOneDsScriptExists=function(e){for(var n=document.getElementsByTagName("script"),t=0;t<n.length;t++)if(n[t].src===e)return!0;return!1},e.prototype._isAppInsightsManager=function(e){return"function"==typeof e.newInst},e.prototype._createAppInsightsCore=function(e,t,i,r){var s=this,c=null;if(!0===this._args.autoCaptureEvents){var d=a(a({instrumentationKey:this._args.appInsightsConfig.instrumentationKey},this._args.endpointUrl&&{endpointUrl:this._args.endpointUrl}),{extensions:[i],channelConfiguration:{eventsLimitInMem:50},propertyConfiguration:{hashIdentifiers:!0},extensionConfig:[],webAnalyticsConfiguration:{autoCapture:{scroll:!1,pageView:!1,onLoad:!1,onUnload:!1,click:this._args.autoCaptureClicks,resize:!1,jsError:this._args.autoCaptureJsErrors}}});r.initialize(d,[]),r.addTelemetryInitializer((function(e){var t=i.getPropertiesContext(),a="";!o.ServerData||e.baseType!==n.ExceptionData&&e.baseType!==n.PageActionData||(t.cloud.role=o.ServerData.serverDetails.dc,t.cloud.roleInstance=o.ServerData.serverDetails.ri,t.cloud.roleVer=o.ServerData.serverDetails.ver,t.app.ver=o.ServerData.serverDetails.ver,t.app.id=o.ServerData.clientEvents.appId,t.app.env=o.ServerData.environment,t.app.sesId=o.ServerData.correlationId),e.baseType===n.ExceptionData?(a=s._args.defaultEventName+n.ExceptionsTablesuffix,e.name=a):e.baseType===n.PageActionData&&(a=s._args.defaultEventName+n.PageActtionsTablesuffix,e.name=a)})),c=r}else{var l=a(a({instrumentationKey:this._args.appInsightsConfig.instrumentationKey},this._args.endpointUrl&&{endpointUrl:this._args.endpointUrl}),{extensions:[t,i],extensionConfig:[]});l.extensionConfig[t.identifier]={eventsLimitInMem:50},this._args.appInsightsConfig&&(l.extensionConfig=this._args.appInsightsConfig),l.extensionConfig[i.identifier]={hashIdentifiers:!0},e.initialize(l,[]),c=e}return c},e}();e.OneDSEventProvider=c;var d=function(){function e(){}return e.prototype.initialize=function(){return!0},e.prototype.post=function(){},e}();e.EmptyEventProvider=d}(i||(i={})),n.EventApi=i.EventApi,n.OneDSEventProvider=i.OneDSEventProvider,n.EmptyEventProvider=i.EmptyEventProvider},,,,,function(e,n,t){var i;!function(e){var n=t(1),i=t(2),a=i.EventApi,o=i.EmptyEventProvider,s=i.OneDSEventProvider,c=window,d=t(0),l=function(){function e(e){var n=this;if(this._nonIndexedDataPoint={},this._enabled=!0,this._clientEventsConfig=e||{},this._enabled=this._clientEventsConfig.telemetryEnabled,this._enabled){if(this._clientEventsConfig.useOneDSEventApi?this._eventApi=new a(new s):this._eventApi=new a(new o),this._serverPageID=this._clientEventsConfig.hostPageID?this._clientEventsConfig.hostPageID.toString():"",this._serviceID=this._clientEventsConfig.serviceID?this._clientEventsConfig.serviceID.toString():"",this._eventApi.initialize(this._clientEventsConfig),this.addPreSendHandler((function(e){n._setPerRequestDataPoints(e)})),this._clientEventsConfig.autoPost){var t=this;setInterval((function(){(t.hasPageEvents()||t._eventApi.hasEvents())&&t.post(!1)}),this._clientEventsConfig.flush)}this._addUnloadHandlers()}}return e.prototype.get=function(e,n,t){if(void 0===n&&(n="data"),void 0===t&&(t=!1),this._enabled)return t?this._eventApi.get(e,n):this._nonIndexedDataPoint[e]},e.prototype.set=function(e,n,t,i){void 0===t&&(t=!1),this._enabled&&(t?this._eventApi.set(e,n,i):this._nonIndexedDataPoint[e]=n)},e.prototype.append=function(e,n,t){var i;void 0===t&&(t=!1),this._enabled&&((i=t?this._eventApi.get(e):this._nonIndexedDataPoint[e])?i instanceof Array||(i=[i]):i=[],i.push(n),t?this._eventApi.set(e,i):this._nonIndexedDataPoint[e]=i)},e.prototype.appendClientEvent=function(e,n,t){if(this._enabled){this._isNumeric(e)&&(e=this._getNumericValue(e));var i="number"==typeof e?e:c.Telemetry.EClientEvent[e];this.append("ClientEvents",{ID:i,EventTime:this._getCurrentTime(),Value:n,DataViewID:t})}},e.prototype.addPreSendHandler=function(e,n){this._enabled&&this._eventApi.addPreSendHandler((function(n,t){e(t&&t.isViewTransition,n)}),n)},e.prototype.post=function(e,n){if(this._enabled){n=n||this._clientEventsConfig.defaultEventName;var t=Object.keys(this._nonIndexedDataPoint).length;(t>0&&e||t>=this._clientEventsConfig.minEvents)&&(this._eventApi.set(d.NonIndexedDataPointKey,JSON.stringify(this._nonIndexedDataPoint)),this._nonIndexedDataPoint={}),this._eventApi.hasEvents()&&this._eventApi.post(n,{isViewTransition:e})}},e.prototype.postPageView=function(e,n){this._enabled&&(n=n||this._clientEventsConfig.defaultEventName,this.set("ViewID",e,!0),this._eventApi.post(n,!1))},e.prototype.applyClientEventBindings=function(e){if(this._enabled)for(var t=this,i=e.querySelectorAll("["+d.ReportEventIdAttr+"]"),a=e.getAttribute(d.DataViewId)?e.getAttribute(d.DataViewId):"",o=function(e){var o=i[e];if(o.getAttribute(d.ReportEventHandlerAttachedAttr))return{value:void 0};var r=o.getAttribute(d.ReportEventIdAttr),s=o.getAttribute(d.ReportEventValueAttr),c=o.getAttribute(d.ReportEventTriggerAttr);if(s){if(0===s.indexOf(d.ReportEventValueAttrBinding)){var l=s.slice(d.ReportEventValueAttrBinding.length).trim();s=o.getAttribute(l)}}else s=t._inferClientEventValue(o);var u=[d.Click,d.Dblclick,d.Keypress,d.Cut,d.Copy,d.Paste,d.Change,d.Focus,d.Scroll,d.Submit,d.Reset],p=c&&c.split(",")||[d.Click];if(p.length>0)for(var f=function(e){-1!==p.indexOf(u[e])&&(n.AddListener(o,u[e],(function(){s||(s=u[e]),t.appendClientEvent(r,s,a)})),o.setAttribute(d.ReportEventHandlerAttachedAttr,"1"))},g=0;g<u.length;g++)f(g)},s=0;s<i.length;s++){var c=o(s);if("object"==r(c))return c.value}},e.prototype._getCurrentTime=function(){return Date.now?Date.now():(new Date).getTime()},e.prototype.hasPageEvents=function(){return Object.keys(this._nonIndexedDataPoint).length>0},e.prototype._addUnloadHandlers=function(){var e=this,t=function(){e.post(!0)};n.AddListener(c.document,"visibilitychange",(function(){"hidden"===c.document.visibilityState&&t()})),n.AddListener(c,"pagehide",t)},e.prototype._inferClientEventValue=function(e){var n;if(e)switch(e.tagName.toLowerCase()){case d.Input:e instanceof HTMLInputElement?e.type!==d.Radio&&e.type!==d.Checkbox||(n=e.checked?"checked":"unchecked"):e instanceof HTMLButtonElement&&(n=e.type===d.Button?"clicked":e.getAttribute(d.ReportEventIdAttr));break;case d.A:e instanceof HTMLAnchorElement&&(n="clicked");break;default:n=e.getAttribute(d.ReportEventIdAttr)}return n},e.prototype._setPerRequestDataPoints=function(e){this.set("ServerPageID",this._serverPageID,!0,"data"),this.set("PageName",this._clientEventsConfig.pageName,!0,"data"),this.set("ServiceID",this._serviceID,!0,"data"),this.set("CorrelationId",this._getCorrelationID(),!0,"data"),this.set("id",this._clientEventsConfig.appId,!0,"app"),this.set("ver",this._clientEventsConfig.serverDetails.version,!0,"app"),this.set("name",this._clientEventsConfig.defaultEventName,!0,"app"),this.set("sesId",this._getCorrelationID(),!0,"app"),this.set("userId","p: "+this._clientEventsConfig.actorID,!0,"app"),this.set("env",this._clientEventsConfig.environment,!0,"app"),this.set("role",this._getCloudrole(),!0,"cloud"),this.set("roleInstance",this._clientEventsConfig.serverDetails.roleInstance,!0,"cloud"),this.set("roleVer",this._clientEventsConfig.serverDetails.version,!0,"cloud")},e.prototype._getCloudrole=function(){return this._clientEventsConfig.serverDetails.datacenter||"-"},e.prototype._getCorrelationID=function(){var e=this._clientEventsConfig.correlationID;return e||(e=n.GenerateGUID(),this._clientEventsConfig.correlationID=e),e},e.prototype._isNumeric=function(e){return!isNaN(e)},e.prototype._getNumericValue=function(e){return Number(e)},e}();e.TelemetryHelper=l}(i||(i={})),n.TelemetryHelper=i.TelemetryHelper}])},"object"==r(n)&&"object"==r(e)?e.exports=o():(i=[],(a="function"==typeof(t=o)?t.apply(n,i):t)===undefined||(e.exports=a))}).call(this,t(55)(e))},function(e,n,t){e.exports="\x3c!-- "+(t(360),t(367),t(56),t(57),t(16),t(58),t(66),t(484),t(495),t(497),' --\x3e\n\n\x3c!--  --\x3e\n\n<div data-bind="if: activeDialog">\n    <div data-bind="component: { name: \'dialog-control\',\n        params: {\n            templateNodes: activeDialog().templateNodes,\n            data: activeDialog().data },\n        event: {\n            close: dialog_onClose } }">\n    </div>\n</div>\n\n<form name="f1" id="i0281" novalidate="novalidate" spellcheck="false"\n    method="post" target="_top" autocomplete="off"\n    data-bind="visible: !isLoginPageHidden(), autoSubmit: forceSubmit, attr: { action: postUrl }, ariaHidden: !!activeDialog(), css: { \'provide-min-height\': svr.fUseMinHeight }">\n    \x3c!-- ko withProperties: { \'$loginPage\': $data } --\x3e\n    <div class="login-paginated-page" data-bind="component: { name: \'master-page\',\n        publicMethods: masterPageMethods,\n        params: {\n            serverData: svr,\n            showButtons: svr.fShowButtons,\n            showFooterLinks: true,\n            useWizardBehavior: svr.fUseWizardBehavior,\n            handleWizardButtons: false,\n            password: password,\n            hideFromAria: ariaHidden },\n        event: {\n            footerAgreementClick: footer_agreementClick } }">\n\n        \x3c!-- ko if: svr.fShowCookieBanner --\x3e\n        \x3c!-- ko component: "cookie-banner-control" --\x3e\x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        <div class="lightbox-cover" data-bind="css: { \'disable-lightbox\': svr.fAllowGrayOutLightBox && showLightboxProgress() }"></div>\n\n        \x3c!-- ko if: showLightboxProgress --\x3e\n        <div id="progressBar" class="progress" role="progressbar" data-bind="component: \'marching-ants-control\', ariaLabel: str[\'WF_STR_ProgressText\']"></div>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: loadBannerLogo --\x3e\n        <div data-bind="component: { name: \'logo-control\',\n            params: {\n                isChinaDc: svr.fIsChinaDc,\n                bannerLogoUrl: bannerLogoUrl() } }">\n        </div>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: svr.strLWADisclaimerMsg && paginationControlHelper.showLwaDisclaimer() --\x3e\n        <div id="LWADisclaimer" class="text-caption" data-bind="text: svr.strLWADisclaimerMsg"></div>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: asyncInitReady --\x3e\n        <div role="main" data-bind="component: { name: \'pagination-control\',\n            publicMethods: paginationControlMethods,\n            params: {\n                enableCssAnimation: svr.fEnableCssAnimation,\n                disableAnimationIfAnimationEndUnsupported: svr.fDisableAnimationIfAnimationEndUnsupported,\n                initialViewId: initialViewId,\n                currentViewId: currentViewId,\n                initialSharedData: initialSharedData,\n                initialError: $loginPage.getServerError() },\n            event: {\n                cancel: paginationControl_onCancel,\n                load: paginationControlHelper.onLoad,\n                unload: paginationControlHelper.onUnload,\n                loadView: view_onLoadView,\n                showView: view_onShow,\n                setLightBoxFadeIn: view_onSetLightBoxFadeIn,\n                animationStateChange: paginationControl_onAnimationStateChange } }">\n\n            <div data-viewid="')+t(0).PaginatedState.Username+'" data-showFedCredButton="true" data-bind="pageViewComponent: { name: \'login-paginated-username-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    prefillNames: $loginPage.prefillNames,\n                    flowToken: sharedData.flowToken,\n                    availableSignupCreds: sharedData.availableSignupCreds,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure(),\n                    userIdLabel: $loginPage.userIdLabel,\n                    cantAccessYourAccountText: $loginPage.cantAccessYourAccountText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    redirect: $loginPage.view_onRedirect,\n                    updateDFPUrl: $loginPage.view_onUpdateDFPUrl,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog,\n                    updateAvailableCredsWithoutUsername: $loginPage.view_onUpdateAvailableCreds,\n                    agreementClick: $loginPage.footer_agreementClick } }">\n            </div>\n\n            \x3c!-- ko ifnot: svr.fUsePromotedFedCredTypesArray --\x3e\n            <div data-viewid="'+t(0).PaginatedState.SignupUsername+'" data-bind="pageViewComponent: { name: \'login-signup-username-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    flowToken: sharedData.flowToken,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure() },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            \x3c!-- ko if: svr.fUsePromotedFedCredTypesArray --\x3e\n            <div data-viewid="'+t(0).PaginatedState.SignupUsername+'" data-showSignupFedCredButton="true" data-bind="pageViewComponent: { name: \'login-signup-username-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    flowToken: sharedData.flowToken,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure() },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            <div data-viewid="'+t(0).PaginatedState.Password+'" data-showIdentityBanner="true" data-dynamicBranding="true" data-bind="pageViewComponent: { name: \'login-paginated-password-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    hipRequiredForUsername: sharedData.hipRequiredForUsername,\n                    passwordBrowserPrefill: sharedData.passwordBrowserPrefill,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    showCredViewBrandingDesc: sharedData.showCredViewBrandingDesc,\n                    flowToken: sharedData.flowToken,\n                    defaultKmsiValue: svr.iDefaultLoginOptions === '+t(7).LoginOption.RememberPWD+',\n                    userTenantBranding: sharedData.userTenantBranding,\n                    sessions: sharedData.sessions,\n                    callMetadata: sharedData.callMetadata,\n                    supportsNativeCredentialRecovery: sharedData.supportsNativeCredentialRecovery,\n                    forgotPasswordText: $loginPage.forgotPasswordText,\n                    accessRecoveryLink: $loginPage.accessRecoveryLink,\n                    boilerPlateText: $loginPage.boilerPlateText,\n                    hideAccountResetCredentials: $loginPage.hideAccountResetCredentials },\n                event: {\n                    restoreIsRecoveryAttemptPost: $loginPage.view_onRestoreIsRecoveryAttemptPost,\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    redirect: $loginPage.view_onRedirect,\n                    resetPassword: $loginPage.passwordView_onResetPassword,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            \x3c!-- ko if: svr.fIsQrCodePinSupported --\x3e\n            <div data-viewid="'+t(0).PaginatedState.QrCodePin+'" data-showIdentityBanner="true" data-dynamicBranding="true" data-bind="pageViewComponent: { name: \'login-paginated-qr-code-pin-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    qrCodeData: sharedData.qrCodeData,\n                    userTenantBranding: sharedData.userTenantBranding,\n                    boilerPlateText: $loginPage.boilerPlateText },\n                event: {\n                    submitReady: $loginPage.view_onSubmitReady,\n                    setBackButtonState: view_onSetIdentityBackButtonState } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            <div data-viewid="'+t(0).PaginatedState.AccessPass+'" data-showIdentityBanner="true" data-dynamicBranding="true" data-bind="pageViewComponent: { name: \'login-access-pass-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    unsafe_username: sharedData.username,\n                    unsafe_displayName: sharedData.displayName,\n                    availableCreds: sharedData.availableCreds,\n                    flowToken: sharedData.flowToken,\n                    isTapRestrictedWsi: sharedData.isTapRestrictedWsi },\n                event: {\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.SearchOrganization+'" data-showQrCodeSignInButton="true" data-bind="pageViewComponent: { name: \'search-organization-view\',\n                params: {\n                    serverData: svr },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ProofConfirmation+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-proof-confirmation-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    currentCred: sharedData.otcCredential,\n                    flowToken: sharedData.flowToken },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.OneTimeCode+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-paginated-otc-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    otcParams: sharedData.otcParams,\n                    preferredCredential: sharedData.preferredCredential,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    showCredViewBrandingDesc: sharedData.showCredViewBrandingDesc,\n                    currentCred: sharedData.otcCredential,\n                    proofConfirmation: sharedData.proofConfirmation,\n                    defaultKmsiValue: svr.iDefaultLoginOptions === '+t(7).LoginOption.RememberPWD+',\n                    flowToken: sharedData.flowToken,\n                    isSignup: sharedData.isSignupPost,\n                    customStrings: $loginPage.stringCustomizationObservables.customStrings(),\n                    isCustomizationFailure: $loginPage.stringCustomizationObservables.isCustomStringsLoadFailure() },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.OneTimeCodeRecovery+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-paginated-otc-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    otcParams: sharedData.recoveryCredentialsData.otcParams,\n                    preferredCredential: sharedData.recoveryCredentialsData.preferredCredential,\n                    availableCreds: sharedData.recoveryCredentialsData.availableRecoveryCreds,\n                    currentCred: sharedData.recoveryCredentialsData.otcCredential,\n                    proofConfirmation: sharedData.proofConfirmation,\n                    flowToken: sharedData.flowToken,\n                    isNativeRecovery: true },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.RemoteNGC+'" data-dynamicBranding="true" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-remote-ngc-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    remoteNgcParams: sharedData.remoteNgcParams,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    showCredViewBrandingDesc: sharedData.showCredViewBrandingDesc,\n                    defaultKmsiValue: svr.iDefaultLoginOptions === '+t(7).LoginOption.RememberPWD+',\n                    sessions: sharedData.sessions,\n                    flowToken: sharedData.flowToken,\n                    idpRedirectUrl: sharedData.idpRedirectUrl },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    updateRemoteNgcParams: $loginPage.view_onUpdateRemoteNgcParams,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    setBackButtonState: view_onSetIdentityBackButtonState } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.RemoteConnectCanaryValidation+'" data-dynamicBranding="true" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'remote-connect-canary-validation-view\',\n                params: {\n                    serverData: svr,\n                    postCanaryValidationAction: $loginPage.postCanaryValidationAction },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setBackButtonState: view_onSetIdentityBackButtonState } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.PhoneDisambiguation+'" data-bind="pageViewComponent: { name: \'login-phone-disambiguation-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    location: sharedData.location,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    flowToken: sharedData.flowToken },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.LwaConsent+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-paginated-lwa-consent-view\',\n                params: {\n                    serverData: svr,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName },\n                event: {\n                    submitReady: $loginPage.view_onSubmitReady } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.IdpDisambiguation+'" data-bind="pageViewComponent: { name: \'login-idp-disambiguation-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName,\n                    hasInitialError: sharedData.hasIdpDisambigError,\n                    sessions: sharedData.sessions,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    idpRedirectUrl: sharedData.idpRedirectUrl,\n                    idpRedirectPostParams: sharedData.idpRedirectPostParams,\n                    preferredCredential: sharedData.preferredCredential,\n                    desktopSsoExecuted: sharedData.desktopSsoExecuted,\n                    desktopSsoEnabled: sharedData.desktopSsoEnabled },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.IdpRedirect+'" data-bind="pageViewComponent: { name: \'login-idp-redirect-view\',\n                params: {\n                    serverData: svr,\n                    idpRedirectUrl: sharedData.idpRedirectUrl,\n                    idpRedirectPostParams: sharedData.idpRedirectPostParams,\n                    idpRedirectProvider: sharedData.idpRedirectProvider },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.IdpRedirectSpeedbump+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-idp-redirect-speedbump-view\',\n                params: {\n                    serverData: svr,\n                    username: sharedData.username,\n                    idpRedirectUrl: sharedData.idpRedirectUrl,\n                    idpRedirectPostParams: sharedData.idpRedirectPostParams,\n                    idpRedirectProvider: sharedData.idpRedirectProvider,\n                    isInitialView: isInitialState,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    showCredViewBrandingDesc: sharedData.showCredViewBrandingDesc,\n                    flowToken: sharedData.flowToken },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.Tiles+'" data-bind="pageViewComponent: { name: \'tiles-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    sessions: sharedData.sessions,\n                    flowToken: sharedData.flowToken,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    isTileRequestPending: $loginPage.asyncTileRequestCount > 0,\n                    availableSignupCreds: sharedData.availableSignupCreds },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ViewAgreement+'" data-wide="true" data-hideLogo="true" data-hideLwaDisclaimer="true" data-hidePageLevelTitleAndDesc="true"\n                data-bind="pageViewComponent: { name: \'login-view-agreement-view\',\n                    params: {\n                        serverData: svr,\n                        agreementType: $loginPage.agreementType } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.LearnMoreOfflineAccount+'" data-wide="true" data-hideLogo="true" data-hideLwaDisclaimer="true" data-hidePageLevelTitleAndDesc="true"\n                data-bind="pageViewComponent: { name: \'login-view-learn-more-offline-account-view\',\n                    params: {\n                        serverData: svr } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.WebNativeBridge+'" data-hideLogo="true" data-hideLwaDisclaimer="true" data-hidePageLevelTitleAndDesc="true"\n                data-bind="pageViewComponent: { name: \'web-native-bridge-view\',\n                    params: {\n                        serverData: svr,\n                        request: sharedData.request,\n                        flowToken: sharedData.flowToken,\n                        reprocessGct: true },\n                    event: {\n                        executeGctResult: $loginPage.fetchSessions_onExecuteGctResult,\n                        redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.SeeHowDataIsManaged+'" data-hidePageLevelTitleAndDesc="true"\n                data-bind="pageViewComponent: { name: \'see-how-data-is-managed-view\',\n                    params: {\n                        serverData: svr } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ConfirmSend+'" data-showIdentityBanner="true" data-dynamicBranding="true" data-bind="pageViewComponent: { name: \'login-confirm-send-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    preferredCredential: sharedData.preferredCredential,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    currentCred: sharedData.otcCredential,\n                    sessions: sharedData.sessions,\n                    flowToken: sharedData.flowToken },\n                event: {\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    redirect: $loginPage.view_onRedirect,\n                    setBackButtonState: view_onSetIdentityBackButtonState } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.RemoteLoginPolling+'" data-hideLwaDisclaimer="true" data-bind="pageViewComponent: { name: \'login-remote-login-polling-view\',\n                params: {\n                    serverData: svr,\n                    autoSendOtcRequest: isInitialState,\n                    userCode: sharedData.remoteLoginUserCode,\n                    deviceCode: sharedData.remoteLoginDeviceCode },\n                event: {\n                    submitReady: $loginPage.view_onSubmitReady } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.LearnMore+'" data-hideLogo="true" data-hideLwaDisclaimer="true"\n                data-bind="pageViewComponent: { name: \'login-learn-more-view\',\n                    params: {\n                        serverData: svr,\n                        command: \'loginlearnmore\' } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ResetPasswordSplitter+'" data-bind="pageViewComponent: { name: \'login-reset-password-splitter-view\',\n                params: {\n                    serverData: svr },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            \x3c!-- ko if: svr.fIsQrCodePinSupported --\x3e\n            <div data-viewid="'+t(0).PaginatedState.QrCodeScan+'" data-bind="pageViewComponent: { name: \'login-qr-code-scan-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    username: sharedData.username,\n                    preferredCredential: sharedData.preferredCredential,\n                    availableCreds: sharedData.availableCreds,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    flowToken: sharedData.flowToken,\n                    qrCodePinParams: sharedData.qrCodePinParams },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            <div data-viewid="'+t(0).PaginatedState.FetchSessionsProgress+"\" data-bind=\"pageViewComponent: { name: 'fetch-sessions-progress-view',\n                params: {\n                    serverData: svr,\n                    sessionPullType: $loginPage.sessionPullType,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    unsafe_desktopSsoDomainToUse: sharedData.unsafe_desktopSsoDomainToUse,\n                    preferredCredential: sharedData.preferredCredential,\n                    isPushNotification: sharedData.remoteNgcParams.defaultType === "+t(0).RemoteNgcType.PushNotification+' },\n                event: {\n                    updateUserTiles: $loginPage.fetchSessions_onUpdateUserTiles,\n                    executeGctResult: $loginPage.fetchSessions_onExecuteGctResult,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.Fido+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-fido-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    allowList: sharedData.fidoParams.allowList,\n                    challenge: sharedData.flowToken,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    flowToken: sharedData.flowToken,\n                    sessions: sharedData.sessions,\n                    hasMsftAuthAppPasskey: sharedData.fidoParams.hasMsftAuthAppPasskey,\n                    hasMsftAndroidAuthAppPasskey: sharedData.fidoParams.hasMsftAndroidAuthAppPasskey,\n                    hasOnlyMsftAndroidAuthAppPasskey: sharedData.fidoParams.hasOnlyMsftAndroidAuthAppPasskey\n                    },\n                event: {\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    redirect: $loginPage.view_onRedirect,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    setLoginPageHiddenState: $loginPage.view_onSetLoginPageHiddenState} }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.CredentialPicker+'" data-bind="pageViewComponent: { name: \'login-credential-picker-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    isUserKnown: !!sharedData.displayName,\n                    availableCreds: sharedData.availableCreds,\n                    evictedCreds: sharedData.evictedCreds,\n                    useEvictedCredentials: sharedData.useEvictedCredentials,\n                    flowToken: sharedData.flowToken,\n                    undirectedRecoveryContinuationToken: sharedData.undirectedRecoveryContinuationToken,\n                    undirectedRecoveryUrl: sharedData.undirectedRecoveryUrl },\n                event: {\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    redirect: $loginPage.view_onRedirect,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest,\n                    registerDialog: $loginPage.view_onRegisterDialog,\n                    unregisterDialog: $loginPage.view_onUnregisterDialog,\n                    showDialog: $loginPage.view_onShowDialog } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ConfirmSignup+'" data-bind="pageViewComponent: { name: \'login-confirm-signup-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.CertificateInterstitialView+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-certificate-interstitial-view\',\n                params: {\n                    serverData: svr,\n                    availableCreds: sharedData.availableCreds,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ConfirmRecoverUsername+'" data-bind="pageViewComponent: { name: \'login-confirm-recover-username-view\',\n                params: {\n                    serverData: svr,\n                    serverError: initialError,\n                    isInitialView: isInitialState,\n                    displayName: sharedData.displayName },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            \x3c!-- ko if: svr.fShowUserAlreadyExistErrorHandling || svr.fBlockOnAppleEmailClaimError --\x3e\n            <div data-viewid="'+t(0).PaginatedState.SignupBlocked+'" data-bind="pageViewComponent: { name: \'login-signup-blocked-view\',\n                params: {\n                    serverData: svr,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    flowToken: sharedData.flowToken,\n                    username: sharedData.username },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            <div data-viewid="'+t(0).PaginatedState.FedConflict+'" data-bind="pageViewComponent: { name: \'login-fed-conflict-view\',\n                params: {\n                    serverData: svr },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.ProofFedConflict+'" data-bind="pageViewComponent: { name: \'login-proof-fed-conflict-view\',\n                params: {\n                    serverData: svr },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.AadFedConflict+'" data-bind="pageViewComponent: { name: \'login-aad-fed-conflict-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.FedLink+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-fed-link-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    otherIdpRedirectUrl: sharedData.otherIdpRedirectUrl,\n                    flowToken: sharedData.flowToken },\n                event: {\n                    redirect: $loginPage.view_onRedirect,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.TenantDisambiguation+'" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-tenant-disambiguation-view\',\n                params: {\n                    serverData: svr,\n                    unsafe_username: sharedData.username,\n                    unsafe_displayName: sharedData.displayName,\n                    tenantList: sharedData.tenantList },\n                event: {\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    submitReady: $loginPage.view_onSubmitReady } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.SignupCredentialPicker+'" data-bind="pageViewComponent: { name: \'login-credential-picker-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    isSignup: true,\n                    availableCreds: sharedData.availableSignupCreds },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.RecoveryCredentialPicker+'" data-bind="pageViewComponent: { name: \'login-credential-picker-view\',\n                params: {\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    availableCreds: sharedData.recoveryCredentialsData.availableRecoveryCreds },\n                event: {\n                    redirect: $loginPage.view_onRedirect } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.RemoteConnectLocation+'" data-bind="pageViewComponent: { name: \'login-remote-connect-location-view\',\n                params: {\n                    serverData: svr } }">\n            </div>\n\n            <div data-viewid="'+t(0).PaginatedState.MoreInfo+"\" data-showIdentityBanner=\"true\" data-bind=\"pageViewComponent: { name: 'more-info-view',\n                params: {\n                    serverData: svr,\n                    moreInfoTitle: str['CT_WSI_WebSignIn_RestrictedToTapHeader'],\n                    moreInfo: str['CT_WSI_WebSignIn_RestrictedToTapError'],\n                    showErrorText: true,\n                    isInitialView: isInitialState } }\">\n            </div>\n\n            \x3c!-- ko if: svr.fIsVerifiableCredentialsSupportEnabled --\x3e\n            <div data-viewid=\""+t(0).PaginatedState.VCPresentation+'" data-dynamicBranding="true" data-showIdentityBanner="true" data-bind="pageViewComponent: { name: \'login-verifiable-credential-presentation-view\',\n                params: {\n                    availableCreds: sharedData.availableCreds,\n                    serverData: svr,\n                    isInitialView: isInitialState,\n                    username: sharedData.username,\n                    displayName: sharedData.displayName,\n                    flowToken: sharedData.flowToken },\n                event: {\n                    updateFlowToken: $loginPage.view_onUpdateFlowToken,\n                    setBackButtonState: view_onSetIdentityBackButtonState,\n                    submitReady: $loginPage.view_onSubmitReady,\n                    setPendingRequest: $loginPage.view_onSetPendingRequest }}">\n            </div>\n            \x3c!-- /ko --\x3e\n        </div>\n        \x3c!-- /ko --\x3e\n\n        <input type="hidden" name="ps" data-bind="value: postedLoginStateViewId" />\n        <input type="hidden" name="psRNGCDefaultType" data-bind="value: postedLoginStateViewRNGCDefaultType" />\n        <input type="hidden" name="psRNGCEntropy" data-bind="value: postedLoginStateViewRNGCEntropy" />\n        <input type="hidden" name="psRNGCSLK" data-bind="value: postedLoginStateViewRNGCSLK" />\n        \x3c!-- ko if: svr.sCanaryTokenName && !svr.fSkipRenderingNewCanaryToken --\x3e\n        <input type="hidden" data-bind="attr: { name: svr.sCanaryTokenName }, value: svr.canary" />\n        \x3c!-- /ko --\x3e\n        \x3c!-- ko if: !svr.sCanaryTokenName || svr.fSkipRenderingNewCanaryToken --\x3e\n        <input type="hidden" name="canary" data-bind="value: svr.canary" />\n        \x3c!-- /ko --\x3e\n        <input type="hidden" name="ctx" data-bind="value: ctx" />\n        <input type="hidden" name="hpgrequestid" data-bind="value: svr.sessionId" />\n        <input type="hidden" id="i0327" data-bind="attr: { name: svr.sFTName }, value: flowToken" />\n        <input type="hidden" name="PPSX" data-bind="value: svr.sRandomBlob" />\n        <input type="hidden" name="NewUser" value="1" />\n        <input type="hidden" name="FoundMSAs" data-bind="value: svr.sFoundMSAs" />\n        <input type="hidden" name="fspost" data-bind="value: svr.fPOST_ForceSignin ? 1 : 0" />\n        <input type="hidden" name="i21" data-bind="value: wasLearnMoreShown() ? 1 : 0" />\n        <input type="hidden" name="CookieDisclosure" data-bind="value: svr.fShowCookieBanner ? 1 : 0" />\n        <input type="hidden" name="IsFidoSupported" data-bind="value: isFidoSupported() ? 1 : 0" />\n        <input type="hidden" name="isSignupPost" data-bind="value: isSignupPost() ? 1 : 0" />\n        \x3c!-- ko ifnot: svr.fShouldSupportTargetCredentialForRecovery --\x3e\n        <input type="hidden" name="isRecoveryAttemptPost" data-bind="value: isRecoveryAttemptPost() ? 1 : 0" />\n        \x3c!-- /ko --\x3e\n        \x3c!-- ko if: svr.fEnableDFPIntegration --\x3e\n        <input type="hidden" name="DfpArtifact" data-bind="value: dfpResult()" />\n        \x3c!-- /ko --\x3e\n        \x3c!-- ko if: svr.fShouldSupportTargetCredentialForRecovery && targetCredentialForRecovery() --\x3e\n        <input type="hidden" name="targetCredentialForRecovery" data-bind="value: targetCredentialForRecovery()" />\n        \x3c!-- /ko --\x3e\n        <div data-bind="component: { name: \'instrumentation-control\',\n            publicMethods: instrumentationMethods,\n            params: { serverData: svr } }">\n        </div>\n    </div>\n    \x3c!-- /ko --\x3e\n</form>\n\n<form data-bind="postRedirectForm: postRedirect"></form>\n\n\x3c!-- ko if: svr.urlCBPartnerPreload --\x3e\n<div id="idPartnerPL" data-bind="injectIframe: { url: svr.urlCBPartnerPreload }"></div>\n\x3c!-- /ko --\x3e\n\n\x3c!-- ko if: svr.fEnableDFPIntegration && dfpUrl() --\x3e\n<div id="idDFPDeviceFingerprinting" data-bind="injectDfpIframe: { url: dfpUrl() }"></div>\n\x3c!-- /ko --\x3e'},function(e,n,t){var i=t(2),a=t(1),o=window;i.components.register("cookie-banner-control",{template:t(361),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents)})},function(e,n,t){e.exports="\x3c!-- "+(t(6),' --\x3e\n\n<div id="msccBanner" class="cc-banner" role="alert">\n    <div class="cc-container">\n        \x3c!-- ko component: \'accessible-image-control\' --\x3e\n        <img class="cc-icon cc-v-center" role="presentation" pngSrc="')+t(363)+'" svgSrc="'+t(364)+'" data-bind="imgSrc" />\n        <img class="cc-icon cc-v-center" role="presentation" pngSrc="'+t(365)+'" svgSrc="'+t(366)+"\" data-bind=\"imgSrc\" />\n        \x3c!-- /ko --\x3e\n\n        <span class=\"cc-v-center cc-text\" data-bind=\"\n            htmlWithBindings: html['CT_STR_CookieBanner_Text'],\n            childBindings: {\n                'msccLearnMore': {\n                    href: svr.urlFooterPrivacy,\n                    ariaLabel: str['CT_STR_CookieBanner_Link_AriaLabel'],\n                    css: { 'cc-link cc-v-center float-right': true } } }\"></span>\n    </div>\n</div>"},function(e,n){e.exports="\x3c!-- ko if: (isHighContrastBlackTheme || hasDarkBackground || svr.fHasBackgroundColor) && !isHighContrastWhiteTheme --\x3e\n\x3c!-- ko template: { nodes: [lightImageNode], data: $parent } --\x3e\x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e\n\x3c!-- ko if: (isHighContrastWhiteTheme || (!hasDarkBackground && !svr.fHasBackgroundColor)) && !isHighContrastBlackTheme --\x3e\n\x3c!-- ko template: { nodes: [darkImageNode], data: $parent } --\x3e\x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e"},function(e,n,t){e.exports=t.p+"content/images/info_white_dfa67fc9bb86d25ec9024cbcc464f6ab.png"},function(e,n,t){e.exports=t.p+"content/images/info_white_997279b4e1ae2d0de0573ab66a1a37b2.svg"},function(e,n,t){e.exports=t.p+"content/images/info_04af79aaa0bb09fe91bef6f2e01e5763.png"},function(e,n,t){e.exports=t.p+"content/images/info_993395b9ddf90c917b2e968779362df6.svg"},function(e,n,t){var i=t(2),a=t(0),o=t(1),r=t(4),s=window,c=o.Helper,d=a.KeyCode;function l(e){var n=this,t=e.data,i=e.templateNodes;function a(e){"Escape"!==(e=e||s.event).code&&e.keyCode!==d.Escape||n.onClose()}n.data=t,n.templateNodes=i,n.onClose=r.create(),n.dispose=function(){c.removeEventListener(document.body,"keydown",a)},n.overlay_onClick=function(){return!0},n.primaryButton_onClick=function(){n.onClose()},c.addEventListener(document.body,"keydown",a)}i.components.register("dialog-control",{viewModel:l,template:t(368),synchronous:!s.ServerData.iMaxStackForKnockoutAsyncComponents||o.Helper.isStackSizeGreaterThan(s.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=l},function(e,n,t){e.exports="\x3c!-- "+(t(13),' --\x3e\n\n<div class="dialog-outer" data-bind="click: overlay_onClick">\n    <div class="dialog-middle">\n        <div class="dialog-inner win-flyout">\n                <div class="dialog-content" role="dialog" data-bind="\n                    attr: { \'aria-labelledby\': data.labelledBy },\n                    ariaDescribedBy: data.describedBy">\n\n                    \x3c!-- ko template: { nodes: templateNodes, data: data } --\x3e\x3c!-- /ko --\x3e\n\n                    <div class="position-buttons">\n                        <div class="row">\n                            <div data-bind="component: { name: \'footer-buttons-field\',\n                                params: {\n                                    serverData: svr,\n                                    primaryButtonId: \'dialogCloseButton\',\n                                    primaryButtonText: str[\'CT_STR_Dialog_CloseButton\'],\n                                    focusOnPrimaryButton: true,\n                                    primaryButtonPreventTabbing: data.primaryButtonPreventTabbing || { direction: \'both\' },\n                                    removeBottomMargin: true,\n                                    isSecondaryButtonVisible: false },\n                                event: {\n                                    primaryButtonClick: primaryButton_onClick } }">\n                            </div>\n                        </div>\n                    </div>\n                </div>\n        </div>\n    </div>\n</div>')},function(e,n){e.exports='<div class="col-xs-24 no-padding-left-right button-container" data-bind="\n    visible: isPrimaryButtonVisible() || isSecondaryButtonVisible(),\n    css: { \'no-margin-bottom\': removeBottomMargin },\n    externalCss: { \'button-field-container\': true }">\n\n    \x3c!-- ko if: isSecondaryButtonVisible --\x3e\n    <div data-bind="css: { \'inline-block\': true }, externalCss: { \'button-item\': true }">\n        <input type="button" id="idBtn_Back" class="win-button button-secondary" data-bind="\n            attr: { \'id\': secondaryButtonId || \'idBtn_Back\' },\n            externalCss: {\n                \'button\': true,\n                \'secondary\': true },\n            value: secondaryButtonText() || str[\'CT_HRD_STR_Splitter_Back\'],\n            ariaDescribedBy: secondaryButtonDescribedBy,\n            hasFocus: focusOnSecondaryButton,\n            click: secondaryButton_onClick,\n            clickBubble: !svr.fEnableLivePreview,\n            enable: isSecondaryButtonEnabled" />\n    </div>\n    \x3c!-- /ko --\x3e\n\n    <div data-bind="css: { \'inline-block\': isPrimaryButtonVisible }, externalCss: { \'button-item\': true }">\n        \x3c!-- type="submit" is needed in-addition to \'type\' in primaryButtonAttributes observable to support IE8 --\x3e\n        \x3c!-- ko ifnot: svr.fConsentButtonIdViaName --\x3e\n        <input type="submit" id="idSIButton9" class="win-button button_primary"\n            data-report-event="Signin_Submit" data-report-trigger="click" data-report-value="Submit"\n            data-bind="\n                attr: primaryButtonAttributes,\n                css: { \'high-contrast-overrides\': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    \'button\': true,\n                    \'primary\': true },\n                value: primaryButtonText() || str[\'CT_PWD_STR_SignIn_Button_Next\'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing" />\n        \x3c!-- /ko --\x3e\n        \x3c!-- ko if: svr.fConsentButtonIdViaName --\x3e\n        <input type="submit"\n            class="win-button button_primary"\n            data-report-event="Signin_Submit"\n            data-report-trigger="click"\n            data-report-value="Submit"\n            data-bind="\n                attr: primaryButtonAttributes,\n                css: { \'high-contrast-overrides\': svr.fUseHighContrastDetectionMode },\n                externalCss: {\n                    \'button\': true,\n                    \'primary\': true },\n                value: primaryButtonText() || str[\'CT_PWD_STR_SignIn_Button_Next\'],\n                hasFocus: focusOnPrimaryButton,\n                click: svr.fEnableLivePreview ?  function() { } : primaryButton_onClick,\n                clickBubble: !svr.fEnableLivePreview,\n                enable: isPrimaryButtonEnabled,\n                visible: isPrimaryButtonVisible,\n                preventTabbing: primaryButtonPreventTabbing" />\n        \x3c!-- /ko --\x3e\n    </div>\n</div>'},function(e,n,t){var i=t(15),a=window;function o(e,n){return n&&n.domainLookupStart&&n.domainLookupEnd&&"DNS"===e?n.domainLookupEnd-n.domainLookupStart:n&&n.connectStart&&n.connectEnd&&"TCP"===e?n.connectEnd-n.connectStart:null}function r(e){return e.duration>0?e.duration<50:null}function s(e){return"subdocument"===e}function c(e){return"resource"===e}function d(e){return"document"===e}function l(e){return"document"===e?document.location.hostname:function(e){if(new RegExp("^(?:[a-z]+:)?//","i").test(e))return!0;return!1}(e)?function(e){if(e){var n=document.createElement("a");return n.href=e,n.hostname}return""}(e):document.location.hostname}function u(e){var n={},t=function(e,n){if(!e||!n)return null;if(n<e)return 0;return n-e}(function(e){var n=e.responseStart;e&&(n=e.responseStart);if(!n){var t=function(e){if(e&&e.fetchStart>0&&e.fetchStart<Number.MAX_VALUE)return e.fetchStart;return null}(e);if(t>0)return t}return n}(e),e.responseEnd),i=e.transferSize;return!0!==r(e)&&(i>0&&t>0?(n.TransferSize=i,n.TransferTime=t):(n.TransferSize=null,n.TransferTime=null)),n}n.SendTelemetryPerfData=function(e,n){var t=i.getInstance(a.ServerData),p={},f={};if(t&&(f=t.get("pltMetrics")||{}),p.apiTimingInfo=f.apiTimingInfo||[],p.isPlt1=function(e){var n=null;if(!e)return null;for(var t=0;t<e.length;t++){var i=e[t];if(c(i.entryType)&&!s(i.initiatorType)&&!d(i.name)){var a=r(i);if(!0===a)n=!0;else if(!1===a){n=!1;break}}}return n}(e.entries),p.plt=function(e){if(!e)return 0;var n=e.customLoadEventEnd,t=!0;(!n||e.loadEventEnd&&n<e.loadEventEnd)&&(n=e.loadEventEnd);return function(e,n,t){if(e&&n>0){var i=function(e,n){if(e.fetchStart>0&&n&&e.fetchStart<Number.MAX_VALUE)return e.fetchStart;if(e.navigationStart>0)return e.navigationStart;return null}(e,t);if(i&&n>=i)return n-i}return 0}(e,n,t)}(e.timing),p.timing=e.timing,p.pltOverallTransferBucket=function(e){if(!e||0===e.length)return null;return function(e){if(!e)return null;var n=12.5,t=n,i=0;for(;e>=n&&i<20;){i++;var a=n;n+=t,t=a}return i}(function(e){var n=0,t=0,i=function(e){var n={},t=[];if(!e)return t;for(var i=0;i<e.length;i++){var a=l(e[i].name)||"";n[a]||""===a||(n[a]=u(e[i]))}for(var o in n)n.hasOwnProperty(o)&&t.push(n[o]);return t}(e);if(i.length>0)for(var a=0;a<i.length;a++)i[a].TransferSize>0&&i[a].TransferTime>0&&(n+=i[a].TransferSize,t+=i[a].TransferTime);if(t>0)return function(e,n){if(e>0&&n>0){return 1e3*(e/n)/1024}return null}(n,t);return null}(e))}(e.entries),p.dns=o("DNS",e.timing),p.tcp=o("TCP",e.timing),p.pageSource=n,!t)throw"PltMetrics failed to post";t.set("pltMetrics",p)}},function(e,n){e.exports='\n<input type="hidden" name="i19" data-bind="value: timeOnPage" />'},function(e,n,t){e.exports="\x3c!-- "+(t(6),' --\x3e\n\n\x3c!-- ko if: bannerLogoUrl --\x3e\n    \x3c!-- ko if: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n    <img id="bannerLogo" data-bind="addEventHandlers, attr: { src: bannerLogoUrl, alt: str[\'STR_Banner_Logo_AltText\'] }, externalCss: { \'banner-logo\': true }" />\n    \x3c!-- /ko --\x3e\n    \x3c!-- ko if: !svr.fTenantBrandingCdnAddEventHandlers --\x3e\n    <img id="bannerLogo" data-bind="attr: { src: bannerLogoUrl, alt: str[\'STR_Banner_Logo_AltText\'] }, externalCss: { \'banner-logo\': true }" />\n    \x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e\n\n\x3c!-- ko if: !bannerLogoUrl && !isChinaDc && !isCiamUserFlowUx --\x3e\n    \x3c!-- ko component: \'accessible-image-control\' --\x3e\n    <img class="logo" role="img" pngSrc="')+t(373)+'" svgSrc="'+t(374)+'" data-bind="imgSrc, attr: { alt: str[\'MOBILE_STR_Footer_Microsoft\'] }" />\n    <img class="logo" role="img" pngSrc="'+t(375)+'" svgSrc="'+t(376)+'" data-bind="imgSrc, attr: { alt: str[\'MOBILE_STR_Footer_Microsoft\'] }" />\n    \x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e\n\n\x3c!-- ko if: !bannerLogoUrl && isCiamUserFlowUx && bannerLogoText --\x3e\n<div id="bannerLogoText" class="banner-logo-text" data-bind="text: bannerLogoText"></div>\n\x3c!-- /ko --\x3e'},function(e,n,t){e.exports=t.p+"content/images/microsoft_logo_white_b71098d9cfa668f68191671a1727b9e9.png"},function(e,n,t){e.exports=t.p+"content/images/microsoft_logo_white_f024dc0422bf3c64a9cb9605a867b798.svg"},function(e,n,t){e.exports=t.p+"content/images/microsoft_logo_ea19b2112f4dfd8e90b4505ef7dcb4f9.png"},function(e,n,t){e.exports=t.p+"content/images/microsoft_logo_564db913a7fa0ca42727161c6d031bef.svg"},function(e,n,t){e.exports="\x3c!-- "+(t(6),' --\x3e\n\n\x3c!-- ko if: svr.fSupportWindowsStyles --\x3e\n    <div class="subline inc"></div>\n    <div class="subline dec"></div>\n\x3c!-- /ko --\x3e\n\x3c!-- ko ifnot: svr.fSupportWindowsStyles --\x3e\n    \x3c!-- ko if: useCssAnimation --\x3e\n    <div></div><div></div><div></div><div></div><div></div>\n    \x3c!-- /ko --\x3e\n    \x3c!-- ko ifnot: useCssAnimation --\x3e\n        \x3c!-- ko component: \'accessible-image-control\' --\x3e\n        <img role="presentation" src="')+t(378)+'" />\n        <img role="presentation" src="'+t(379)+'" />\n        \x3c!-- /ko --\x3e\n    \x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e'},function(e,n,t){e.exports=t.p+"content/images/marching_ants_white_8257b0707cbe1d0bd2661b80068676fe.gif"},function(e,n,t){e.exports=t.p+"content/images/marching_ants_986f40b5a9dc7d39ef8396797f61b323.gif"},function(e,n,t){var i={"./AppCentipede_Beam.png":381,"./AppCentipede_Beam.svg":382,"./AppCentipede_Beam_white.png":383,"./AppCentipede_Beam_white.svg":384,"./AppCentipede_Bing.png":385,"./AppCentipede_Bing.svg":386,"./AppCentipede_Bing_white.png":387,"./AppCentipede_Bing_white.svg":388,"./AppCentipede_Cortana.png":389,"./AppCentipede_Cortana.svg":390,"./AppCentipede_Cortana_white.png":391,"./AppCentipede_Cortana_white.svg":392,"./AppCentipede_Excel.png":393,"./AppCentipede_Excel.svg":394,"./AppCentipede_Excel_white.png":395,"./AppCentipede_Excel_white.svg":396,"./AppCentipede_Health.png":397,"./AppCentipede_Health.svg":398,"./AppCentipede_Health_white.png":399,"./AppCentipede_Health_white.svg":400,"./AppCentipede_MSN.png":401,"./AppCentipede_MSN.svg":402,"./AppCentipede_MSN_white.png":403,"./AppCentipede_MSN_white.svg":404,"./AppCentipede_Microsoft.png":405,"./AppCentipede_Microsoft.svg":406,"./AppCentipede_Microsoft_white.png":407,"./AppCentipede_Microsoft_white.svg":408,"./AppCentipede_MileIQ.png":409,"./AppCentipede_MileIQ.svg":410,"./AppCentipede_MileIQ_white.png":411,"./AppCentipede_MileIQ_white.svg":412,"./AppCentipede_Minecraft.png":413,"./AppCentipede_Minecraft.svg":414,"./AppCentipede_Minecraft_white.png":415,"./AppCentipede_Minecraft_white.svg":416,"./AppCentipede_Office.png":417,"./AppCentipede_Office.svg":418,"./AppCentipede_Office_white.png":419,"./AppCentipede_Office_white.svg":420,"./AppCentipede_OneDrive.svg":421,"./AppCentipede_OneDrive_white.svg":422,"./AppCentipede_Onedrive.png":423,"./AppCentipede_Onedrive_white.png":424,"./AppCentipede_Outlook.png":425,"./AppCentipede_Outlook.svg":426,"./AppCentipede_Outlook_white.png":427,"./AppCentipede_Outlook_white.svg":428,"./AppCentipede_PowerPoint.png":429,"./AppCentipede_PowerPoint.svg":430,"./AppCentipede_PowerPoint_white.png":431,"./AppCentipede_PowerPoint_white.svg":432,"./AppCentipede_Skype.png":433,"./AppCentipede_Skype.svg":434,"./AppCentipede_SkypeDialer.png":435,"./AppCentipede_SkypeDialer.svg":436,"./AppCentipede_SkypeDialer_white.png":437,"./AppCentipede_SkypeDialer_white.svg":438,"./AppCentipede_Skype_white.png":439,"./AppCentipede_Skype_white.svg":440,"./AppCentipede_Word.png":441,"./AppCentipede_Word.svg":442,"./AppCentipede_Word_white.png":443,"./AppCentipede_Word_white.svg":444,"./AppCentipede_Xbox.png":445,"./AppCentipede_Xbox.svg":446,"./AppCentipede_Xbox_white.png":447,"./AppCentipede_Xbox_white.svg":448};function a(e){var n=o(e);return t(n)}function o(e){if(!t.o(i,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=o,e.exports=a,a.id=380},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_beam_6480ab9f07799b0087086392b2c2132b.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_beam_db428c0efe494476a50358f59de72d00.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_beam_white_2739aa83905a861727d26fa8f6401c10.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_beam_white_31b6739ece1da056e24332ec40282885.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_bing_de0949215133bcbd99d0151ec3152dd3.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_bing_e60284baea3f4ad80cb69c7b8bcff2ca.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_bing_white_1744b7da24377c04cc991c305e8c2050.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_bing_white_ef209fd1cccbabd253115348febaf98f.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_cortana_cab3431d6b69c3d725fa7e31aaea678d.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_cortana_f09e94a3276d91d4c79b9873cef34b11.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_cortana_white_1272d4be85f12a748bfc24de5f47c32f.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_cortana_white_e366a0344c983a6ed654386534ab81d0.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_excel_6966adc795ed71833d99e2216d4e46e2.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_excel_353c85098e98e1057521dc6fe5d70d1c.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_excel_white_e295b930a32ac0b2be7bfed679ac8452.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_excel_white_b9229e387c9282a577eb5600a895d504.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_health_cafb5330709130150e8a6c1633bebc36.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_health_55d03bf64395784ace46b90356ce1df1.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_health_white_fe75ef9aaa3dd956b1f9a2d4cba33c24.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_health_white_01e7ad14f679d29353712ed4c2407300.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_msn_939e2c45eee250ffd6615804d85b0614.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_msn_63ce2d326a43455f8548795de5844fb7.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_msn_white_676fee51d2bd254584f73e6db0ff22bc.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_msn_white_e44b41007105e24f308ab70c882ce529.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_microsoft_0e3e4bffb28c6dfb20470d2d8fbfa5c4.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_microsoft_46f7c723cc3b758905a95d0b2598f99f.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_microsoft_white_596315b39d39ab1d48205d659fc45957.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_microsoft_white_f958bc6056dc560ebbe6b380bff3983a.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_mileiq_02070aef77d88b2ed2b34e6d7c3cc0d3.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_mileiq_d6c33a42a05b1f10d2f8307713fbb15e.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_mileiq_white_81a587a8af0f713ad8061e9319e2952b.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_mileiq_white_348323851b3f87a2c1f32f197c52072f.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_minecraft_fd019657d89397f98f3d4ddfd2bdace5.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_minecraft_08438125e3d4b839ae6ab5fb0f95a68f.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_minecraft_white_acea0b1e7f4a2c25f6cba5ecbc26f2e5.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_minecraft_white_05483ba9660327ab9caa68d0cbc92092.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_office_03fb8e0ad4992f9a124782ca6a64e77d.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_office_2e97325e1829058b420fadda52b7844e.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_office_white_be251fe6ccc55da17b44f07177ec4ff9.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_office_white_0bf60bbb022d9ed93095be0cca7ee40a.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_onedrive_3de88cae13106daa1a2e5a6c8932e011.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_onedrive_white_f2319c44f7dd2342c72cc63fd5b9663e.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_onedrive_c6b29811e4ffed286e121e395dc82195.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_onedrive_white_ed083beb66a404e7e096b506ad19f356.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_outlook_d32f80969310bc924eaaf4239a960ad9.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_outlook_77253e9fcc3ee9bfe0688d63025315bc.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_outlook_white_ba06eaf8561ffe8a21326fe6d58f953b.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_outlook_white_6beab1dec976905f07eb13aac7561666.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_powerpoint_fbd1d8eba10152e7180e87e4524d7e11.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_powerpoint_9bbbad1f64860e5ffd33a1b7ea735ad3.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_powerpoint_white_d42df324d7ae0f5cd666ea83b94078da.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_powerpoint_white_ede18787cd7ee71a1ddaf874ef38f4c1.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skype_4e39845dacf64ef2d02d6ed2428e4237.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skype_2ec6111de856694b50bda798abede78f.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skypedialer_f47bacf464b9a2faa407c20f3ce5ec17.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skypedialer_1fbb2af9ebf82c70c70796fdda4597dc.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skypedialer_white_4a09c0798c9d4a4d42dea3c7f487c42b.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skypedialer_white_dfdba6b99e0fb4b5bf71423c0d4c7a7e.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skype_white_fa6ae89d92407498478dd9f4e98b6d23.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_skype_white_a8ef854d33fd75a90465a7056ef5747d.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_word_a8af42542634596a9480d6418829067a.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_word_5d417dcd978efa4eed1560d5b5b8c70c.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_word_white_e90fafa4bd9ec5c80f1c932b1bf84fb2.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_word_white_e7dcf5cbfb117e77edfa5ddf44249d7e.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_xbox_50a197f53286dec7fcce1e0c12120b4e.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_xbox_4ac5e6c33849d1a5347608d4ca673ffe.svg"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_xbox_white_cd9b74027d4362910042c5035b22b96b.png"},function(e,n,t){e.exports=t.p+"content/images/appcentipede/appcentipede_xbox_white_2c9ffed0cc780dc32c32160b183ee4af.svg"},function(e,n,t){e.exports="\x3c!-- "+(t(450)," --\x3e\n\n\x3c!-- ko ifnot: useLayoutTemplates --\x3e\n    \x3c!-- ko template: { nodes: $componentTemplateNodes, data: $parent } --\x3e\x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e\n\n\x3c!-- ko if: useLayoutTemplates --\x3e\n    \x3c!-- ko withProperties: { '$page': $parent } --\x3e\n        \x3c!-- ko if: isLightboxTemplate() --\x3e\n        <div id=\"lightboxTemplateContainer\" data-bind=\"component: { name: 'lightbox-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }\">\n        </div>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: isVerticalSplitTemplate() && isTemplateLoaded() --\x3e\n        <div id=\"verticalSplitTemplateContainer\" data-bind=\"component: { name: 'vertical-split-template', params: { serverData: svr, showHeader: $page.showHeader(), headerLogo: $page.headerLogo() } }, css: { 'provide-min-height': svr.fUseMinHeight }\">\n        </div>\n        \x3c!-- /ko --\x3e\n    \x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e")},function(e,n,t){var i=t(2),a=t(1),o=window;function r(e){var n=e.serverData,t=e.showHeader,i=e.headerLogo,a=n.fHideHeader;this.showHeader=t&&!a,this.headerLogo=i}i.components.register("lightbox-template",{viewModel:r,template:t(451),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=r},function(e,n,t){e.exports="\x3c!-- "+(t(59),t(60),t(61),t(30),' --\x3e\n\n<div id="lightboxBackgroundContainer" data-bind="css: { \'provide-min-height\': svr.fUsePlaywrightMinHeight },\n    component: { name: \'background-image-control\',\n        publicMethods: $page.backgroundControlMethods,\n        event: { load: $page.backgroundImageControl_onLoad } }">\n</div>\n\n\x3c!-- ko if: svr.iBannerEnvironment --\x3e\n<div class="env-banner" data-bind="component: { name: \'environment-banner-control\' }"></div>\n\x3c!-- /ko --\x3e\n\n\x3c!-- ko withProperties: { \'$masterPageContext\': $parentContext } --\x3e\n<div class="outer" data-bind="css: { \'app\': $page.backgroundLogoUrl }">\n    \x3c!-- ko if: showHeader --\x3e\n    <div class="template-section">\n        <div class="template-header-container">\n            <div id="header" data-bind="externalCss: { \'header\': true }">\n                <img id="headerLogo" role="img" data-bind="\n                    attr: {\n                        src: headerLogo,\n                        alt: str[\'STR_Templates_Header_Logo_AltText\'] },\n                    externalCss: { \'header-logo\': true }" />\n            </div>\n        </div>\n    </div>\n    \x3c!-- /ko --\x3e\n\n    <div class="template-section main-section">\n        <div data-bind="externalCss: { \'middle\': true }">\n            <div class="full-height" data-bind="component: { name: \'content-control\', params: { serverData: svr, isVerticalSplitTemplate: $page.isVerticalSplitTemplate(), hasHeader: showHeader } }"></div>\n        </div>\n    </div>\n\n    \x3c!-- ko if: $page.paginationControlHelper.showFooterControl --\x3e\n    <div id="footer" role="contentinfo" data-bind="\n        externalCss: {\n            \'footer\': true,\n            \'has-background\': !$page.useDefaultBackground() && $page.showFooter(),\n            \'background-always-visible\': $page.backgroundLogoUrl }">\n\n        <div data-bind="component: { name: \'footer-control\',\n            publicMethods: $page.footerMethods,\n            params: {\n                serverData: svr,\n                useDefaultBackground: $page.useDefaultBackground(),\n                hasDarkBackground: $page.backgroundLogoUrl(),\n                showLinks: true,\n                showFooter: $page.showFooter(),\n                hideTOU: $page.hideTOU(),\n                termsText: $page.termsText(),\n                termsLink: $page.termsLink(),\n                hidePrivacy: $page.hidePrivacy(),\n                privacyText: $page.privacyText(),\n                privacyLink: $page.privacyLink() },\n            event: {\n                agreementClick: $page.footer_agreementClick,\n                showDebugDetails: $page.toggleDebugDetails_onClick } }">\n        </div>\n    </div>\n    \x3c!-- /ko --\x3e\n</div>\n\x3c!-- /ko --\x3e')},function(e,n){e.exports='<div class="background-image-holder" role="presentation" data-bind="css: { app: isAppBranding }, style: { background: backgroundStyle }">\n    \x3c!-- ko if: smallImageUrl --\x3e\n    <div class="background-image-small" data-bind="backgroundImage: smallImageUrl()"></div>\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko if: backgroundImageUrl --\x3e\n    <div id="backgroundImage" role="img" data-bind="backgroundImage: backgroundImageUrl(), externalCss: { \'background-image\': true }, ariaLabel: str[\'STR_Background_Image_AltText\']"></div>\n        \x3c!-- ko if: useImageMask --\x3e\n        <div data-bind="externalCss: { \'background-overlay\': true }"></div>\n        \x3c!-- /ko --\x3e\n    \x3c!-- /ko --\x3e\n</div>'},function(e,n){e.exports="<div class=\"env-banner-inner\" role=\"alert\" aria-atomic=\"true\" data-bind=\"\n    htmlWithBindings: html['CT_STR_EnvironmentBanner_Text'],\n    childBindings: {\n        'envBannerNoLink': {\n            css: { 'env-banner-text': true }\n        },\n        'envBannerLink': {\n            href: svr.urlEnvBannerLink,\n            css: { 'env-banner-link': true }\n        } }\">\n</div>"},function(e,n,t){e.exports="\x3c!-- "+(t(455),t(32),t(6),t(467),t(469),' --\x3e\n\n\x3c!-- ko withProperties: { \'$content\': $data } --\x3e\n<div class= "flex-column">\n    \x3c!-- ko if: $page.paginationControlHelper.showBackgroundLogoHolder --\x3e\n    <div class="background-logo-holder">\n        <img class="background-logo" role="presentation" data-bind="attr: { src: $page.backgroundLogoUrl }" />\n    </div>\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko if: $page.paginationControlHelper.showPageLevelTitleControl --\x3e\n    <div data-bind="component: \'page-level-title-control\'"></div>\n    \x3c!-- /ko --\x3e\n\n    <div class="win-scroll">\n        <div id="lightbox" data-bind="\n            animationEnd: $page.paginationControlHelper.animationEnd,\n            externalCss: { \'sign-in-box\': true },\n            css: {\n                \'inner\':  $content.isVerticalSplitTemplate,\n                \'vertical-split-content\': $content.isVerticalSplitTemplate,\n                \'app\': $page.backgroundLogoUrl,\n                \'wide\': $page.paginationControlHelper.useWiderWidth,\n                \'fade-in-lightbox\': $page.fadeInLightBox,\n                \'has-popup\': $page.showFedCredAndNewSession && ($page.showFedCredButtons() || $page.newSession()),\n                \'transparent-lightbox\': $page.backgroundControlMethods() && $page.backgroundControlMethods().useTransparentLightBox,\n                \'lightbox-bottom-margin-debug\': $page.showDebugDetails,\n                \'has-header\': $content.hasHeader }">\n\n            \x3c!-- ko template: { nodes: $masterPageContext.$componentTemplateNodes, data: $page } --\x3e\x3c!-- /ko --\x3e\n        </div>\n\n        \x3c!-- ko if: $page.showFedCredAndNewSession --\x3e\n        \x3c!-- ko ifnot: svr.fUsePromotedFedCredTypesArray --\x3e\n        \x3c!-- ko if: $page.showFedCredButtons --\x3e\n        <div data-bind="css: { \'app\': $page.backgroundLogoUrl }, externalCss: { \'promoted-fed-cred-box\': true }">\n            <div class="promoted-fed-cred-content" data-bind="css: {\n                \'animate\': $page.useCssAnimations && $page.animate(),\n                \'slide-out-next\': $page.animate.isSlideOutNext,\n                \'slide-in-next\': $page.animate.isSlideInNext,\n                \'slide-out-back\': $page.animate.isSlideOutBack,\n                \'slide-in-back\': $page.animate.isSlideInBack,\n                \'app\': $page.backgroundLogoUrl }">\n\n                \x3c!-- ko foreach: $page.otherSigninOptions --\x3e\n                <div class="row tile">\n                    <div class="table" role="button" tabindex="0" data-bind="\n                        css: { \'list-item\': svr.fSupportWindowsStyles },\n                        pressEnter: $page.otherSigninOptionsButton_onClick,\n                        click: $page.otherSigninOptionsButton_onClick,\n                        ariaLabel: $data.text">\n\n                        <div class="table-row">\n                            <div class="table-cell tile-img medium">\n                                \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                                    \x3c!-- ko if: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                                    <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.lightIconUrl }" />\n                                    <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.darkIconUrl }" />\n                                    \x3c!-- /ko --\x3e\n                                    \x3c!-- ko ifnot: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                                    <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.lightIconUrl }" />\n                                    <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.darkIconUrl }" />\n                                    \x3c!-- /ko --\x3e\n                                \x3c!-- /ko --\x3e\n                            </div>\n                            <div class="table-cell text-left" data-bind="css: { \'content\': !svr.fSupportWindowsStyles }">\n                                <div data-bind="\n                                    text: $data.text,\n                                    attr: { \'data-test-id\': $data.testId }"></div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                \x3c!-- /ko --\x3e\n            </div>\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: svr.fUsePromotedFedCredTypesArray --\x3e\n        \x3c!-- ko if: $page.showFedCredButtons --\x3e\n        <div data-bind="component: { name: \'fed-cred-buttons-control\',\n            params: {\n                serverData: svr,\n                fedCredOptions: $page.otherSigninOptions },\n            event: {\n                fedCredButtonClick: $page.otherSigninOptionsButton_onClick } }">\n        </div>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: $page.showSignupFedCredButtons --\x3e\n        <div data-bind="component: { name: \'fed-cred-buttons-control\',\n            params: {\n                serverData: svr,\n                fedCredOptions: $page.otherSignupOptions },\n            event: {\n                fedCredButtonClick: $page.otherSignupOptionsButton_onClick } }">\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: svr.fShowQrCodePinOption --\x3e\n        \x3c!-- ko if: $page.showQrCodeSignInButton --\x3e\n        <div data-bind="component: { name: \'fed-cred-buttons-control\',\n            params: {\n                serverData: svr,\n                fedCredOptions: [$page.qrCodeSigninOption] },\n            event: {\n                fedCredButtonClick: $page.qrCodePinSigninButton_onClick,\n                fedCredHelpButtonClick: $page.qrCodePinHelpButton_onClick } }">\n        </div>\n        <div data-bind="component: { name: \'qr-code-pin-help-dialog-content-control\',\n            event: {\n                registerDialog: $page.view_onRegisterDialog,\n                unregisterDialog: $page.view_onUnregisterDialog } }">\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: $page.newSession --\x3e\n        <div class="new-session-popup-v2sso" role="alert" data-bind="css: { \'app\': $page.backgroundLogoUrl }">\n            <div class="form-group" id="NewSessionTitle" data-bind="text: str[\'TILE_STR_AsyncTileText_Title\']"></div>\n\n            <div class="row tile">\n                <div class="table" id="newSessionLink" data-bind="\n                    css: { \'list-item\': svr.fSupportWindowsStyles },\n                    click: $page.newSession_onClick,\n                    ariaDescribedBy: \'NewSessionTitle\'" tabindex="0">\n                    <div class="table-row">\n                        <div class="table-cell tile-img">\n                            <img class="tile-img" role="presentation" pngSrc="')+t(39)+'"\n                                svgSrc="'+t(40)+'" data-bind="imgSrc" />\n                        </div>\n                        <div class="table-cell text-left" data-bind="css: { \'content\': !svr.fSupportWindowsStyles }">\n                            <div data-bind="text: $page.newSessionInfo().unsafe_newSessionFullName"></div>\n\n                            \x3c!-- ko if: $page.newSessionInfo().unsafe_newSessionDisplayName --\x3e\n                            <div><small data-bind="text: $page.newSessionInfo().unsafe_newSessionDisplayName"></small></div>\n                            \x3c!-- /ko --\x3e\n\n                            <div><small data-bind="text: str[\'TILE_STR_Active\']"></small></div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: $page.showDebugDetails --\x3e\n        <div data-bind="component: { name: \'debug-details-control\',\n            publicMethods: $page.debugDetailsMethods,\n            params: {\n                serverData: svr,\n                debugDetails: $page.debugDetails,\n                useWiderWidth: $page.paginationControlHelper.useWiderWidth,\n                isDebugTracingEnabled: $page.isDebugTracingEnabled },\n            event: {\n                closeBanner: $page.closeDebugDetails_onClick,\n                setDebugTracing: $page.setDebugTracing_onClick } }">\n\n            \x3c!-- ko if: $page.showConditionalAccessDebugDetails --\x3e\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_AppName\'],\n                childBindings: {\n                    \'caAppName\': { css: {\'bold\': true} } }">\n            </div>\n\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_AppId\'],\n                childBindings: {\n                    \'caAppId\': { css: {\'bold\': true} } }">\n            </div>\n\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_IpAddress\'],\n                childBindings: {\n                    \'caIpAddress\': { css: {\'bold\': true} } }">\n            </div>\n\n            \x3c!-- ko if: svr.sIpAddressFromApp --\x3e\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_IpAddressFromApp\'],\n                childBindings: {\n                    \'caIpAddressFromApp\': { css: {\'bold\': true} } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_DeviceId\'],\n                childBindings: {\n                    \'caDeviceId\': { css: {\'bold\': true} } }">\n            </div>\n\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_DevicePlatform\'],\n                childBindings: {\n                    \'caDevicePlatform\': { css: {\'bold\': true} } }">\n            </div>\n\n            <div data-bind="\n                htmlWithBindings: html[\'CT_STR_DeviceState\'],\n                childBindings: {\n                    \'caDeviceState\': { css: {\'bold\': true} } }">\n            </div>\n            \x3c!-- /ko --\x3e\n\n            \x3c!-- ko if: $page.paginationControlHelper.showErrorPageDebugDetails --\x3e\n            \x3c!-- ko if: svr.strAppDisplayName --\x3e\n            <div>\n                <span class="bold">App name: </span>\n                <span data-bind="text: svr.strAppDisplayName"></span>\n            </div>\n            \x3c!-- /ko --\x3e\n            \x3c!-- ko if: svr.strDevicePlatformForUI --\x3e\n            <div>\n                <span class="bold">Device platform: </span>\n                <span data-bind="text: svr.strDevicePlatformForUI"></span>\n            </div>\n            \x3c!-- /ko --\x3e\n            \x3c!-- ko if: svr.strDeviceState --\x3e\n            <div>\n                <span class="bold">Device state: </span>\n                <span data-bind="text: svr.strDeviceState"></span>\n            </div>\n            \x3c!-- /ko --\x3e\n            \x3c!-- ko if: svr.strClientIP --\x3e\n            <div>\n                <span class="bold">IP address: </span>\n                <span data-bind="text: svr.strClientIP"></span>\n            </div>\n            \x3c!-- /ko --\x3e\n            \x3c!-- ko if: svr.strCurrentUser --\x3e\n            <div>\n                <span class="bold">Signed in as: </span>\n                <span data-bind="text: svr.strCurrentUser"></span>\n            </div>\n            \x3c!-- /ko --\x3e\n            \x3c!-- /ko --\x3e\n\n        </div>\n        \x3c!-- /ko --\x3e\n    </div>\n</div>\n\x3c!-- /ko --\x3e'},function(e,n,t){var i=t(2),a=t(1),o=window;i.components.register("page-level-title-control",{template:t(456),synchronous:!o.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(o.ServerData.iMaxStackForKnockoutAsyncComponents)})},function(e,n){e.exports='<div class="win-content-header">\n    <div class="win-body-header">\n        <h1 id="winBodyHeader" data-bind="text: str[\'CT_STR_Page_Title\']"></h1>\n    </div>\n    <div id="winBodySubHeader" role="heading" aria-level="2" class="win-body-sub-header" data-bind="text: str[\'CT_STR_Page_SubTitle\']"></div>\n</div>'},function(e,n){n.applyExtensions=function(e){e.bindingHandlers.copySource={init:function(n,t,i,a,o){var r=e.unwrap(t()),s=document.getElementById(r);s||((s=document.createElement("textarea")).id=r,document.body.appendChild(s),e.bindingHandlers.moveOffScreen.update(s,(function(){return!0}))),e.bindingEvent.subscribe(n,"descendantsComplete",(function(){s.value=n.innerText}));var c=e.bindingEvent.startPossiblyAsyncContentBinding(n,o);return e.applyBindingsToDescendants(c,n),{controlsDescendantBindings:!0}}},e.bindingHandlers.clickToCopy={init:function(n,t,i,a,o){var r=e.unwrap(t()),s=r.textareaId,c=r.showNotification;e.bindingHandlers.click.init(n,(function(){return function(){!function(e,n,t){var i=document.getElementById(e);if(!i)return;i.select(),document.execCommand("copy"),t.focus(),function(e){e()&&e(!1);setTimeout((function(){e(!0)}),0)}(n)}(s,c,n)}}),i,a,o)}}}},function(e,n,t){e.exports="\x3c!-- "+(t(16),t(6),' --\x3e\n\n\x3c!-- ko if: showBanner --\x3e\n<div id="debugDetailsBanner" class="debug-details-banner table" data-bind="css: { \'wide\': useWiderWidth }">\n    <div class="table-row">\n        <div class="table-cell">\n            \x3c!-- ko if: svr.fShowCopyDebugDetailsLink --\x3e\n            <div class="debug-details-header">\n                <div id="debugDetailsHeader" class="bold debug-details-heading-text" role="heading" aria-level="2" data-bind="text: str[\'STR_Error_Details_Title\']"></div>\n                <div data-bind="text: str[\'STR_Error_Details_Instruction\']"></div>\n                <a id="idCopyToClipboard" href="#" role="button" data-bind="\n                    clickToCopy: { textareaId: \'debugDetailsText\', showNotification: showDebugDetailsCopyMessage },\n                    text: str[\'STR_Error_Details_CopyLink\'],\n                    ariaDescribedBy: \'debugDetailsHeader\',\n                    ariaLabel: str[\'STR_Error_Details_CopyLink\'],\n                    hasFocus: isFocusActivated"></a>\n                <span id="debugDetailsCopyMessage" class="debug-details-notification fade-in-lightbox" aria-live="assertive" data-bind="visible: showDebugDetailsCopyMessage">\n\n                    \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                    <img role="presentation" pngSrc="')+t(459)+'" svgSrc="'+t(460)+'" data-bind="imgSrc" />\n                    <img role="presentation" pngSrc="'+t(461)+'" svgSrc="'+t(462)+'" data-bind="imgSrc" />\n                    \x3c!-- /ko --\x3e\n\n                    <span role="alert" data-bind="\n                        text: showDebugDetailsCopyMessage() ? str[\'STR_Error_Details_Notification\'] : null,\n                        ariaLabel: str[\'STR_Error_Details_Notification_ScreenReader\']"></span>\n                </span>\n            </div>\n            \x3c!-- /ko --\x3e\n\n            <div class="override-ltr" data-bind="copySource: \'debugDetailsText\'">\n                \x3c!-- ko if: debugDetails.errorCode --\x3e\n                <div>\n                    <span class="bold">Error Code: </span>\n                    <span data-bind="text: debugDetails.errorCode"></span>\n                </div>\n                \x3c!-- /ko --\x3e\n                \x3c!-- ko if: svr.sessionId --\x3e\n                <div>\n                    <span class="bold">Request Id: </span>\n                    <span data-bind="text: svr.sessionId"></span>\n                </div>\n                \x3c!-- /ko --\x3e\n                <div>\n                    <span class="bold">Correlation Id: </span>\n                    <span data-bind="text: svr.correlationId"></span>\n                </div>\n                <div>\n                    <span class="bold">Timestamp: </span>\n                    <span data-bind="text: debugDetails.timestamp"></span>\n                </div>\n                \x3c!-- ko if: svr.strServiceExceptionMessage --\x3e\n                <div>\n                    <span class="bold">Message: </span>\n                    <span data-bind="text: unsafe_exceptionMessage"></span>\n                </div>\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko template: { nodes: $componentTemplateNodes, data: $data } --\x3e\x3c!-- /ko --\x3e\n            </div>\n\n            \x3c!-- ko if: svr.urlSetDebugMode --\x3e\n            <div class="debug-trace-section">\n                <div>\n                    <span class="bold" data-bind="text: str[\'STR_Error_Details_Debug_Mode\']"></span>\n                    <a id="setDebugMode" role="button" data-bind="\n                        href: svr.urlSetDebugMode,\n                        text: isDebugTracingEnabled() ? str[\'STR_Error_Details_Debug_Mode_Disable\'] : str[\'STR_Error_Details_Debug_Mode_Enable\'],\n                        ariaLabel: isDebugTracingEnabled() ? str[\'STR_Error_Details_Debug_Mode_Disable_AriaLabel\'] : str[\'STR_Error_Details_Debug_Mode_Enable_AriaLabel\'],\n                        click: setDebugMode_onClick,\n                        hasFocus: isFocusActivated() && !svr.fShowCopyDebugDetailsLink"></a>\n                </div>\n                \x3c!-- ko if: sending --\x3e\n                <div class="progress-container-tile">\n                    <div class="progress" role="progressbar" data-bind="component: \'marching-ants-control\', ariaLabel: str[\'WF_STR_ProgressText\']"></div>\n                </div>\n                \x3c!-- /ko --\x3e\n                \x3c!-- ko if: debugModeError --\x3e\n                <div role="alert" data-bind="text: debugModeError, externalCss: { \'error\': true }"></div>\n                \x3c!-- /ko --\x3e\n                <div data-bind="text: str[\'STR_Error_Details_Debug_Mode_Desc\']"></div>\n            </div>\n            \x3c!-- /ko --\x3e\n        </div>\n        <div>\n            <a id="errorBannerCloseLink" role="button" href="#" data-bind="\n                click: hideBanner_onClick,\n                ariaLabel: str[\'CT_STR_Error_Details_Close_AltText\'],\n                hasFocus: isFocusActivated() && !svr.fShowCopyDebugDetailsLink && !svr.urlSetDebugMode">\n                \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                <img role="presentation" pngSrc="'+t(463)+'" svgSrc="'+t(464)+'" data-bind="imgSrc, attr: { alt: str[\'CT_STR_Error_Details_Close_AltText\'] }" />\n                <img role="presentation" pngSrc="'+t(465)+'" svgSrc="'+t(466)+'" data-bind="imgSrc, attr: { alt: str[\'CT_STR_Error_Details_Close_AltText\'] }" />\n                \x3c!-- /ko --\x3e\n            </a>\n        </div>\n    </div>\n</div>\n\x3c!-- /ko --\x3e'},function(e,n,t){e.exports=t.p+"content/images/check_small_white_7e9965afff0c89a10fc1c0e70ae2a544.png"},function(e,n,t){e.exports=t.p+"content/images/check_small_white_8962e719a088b7250ec88d8762ad1746.svg"},function(e,n,t){e.exports=t.p+"content/images/check_small_490de8de96812fad01b54b4209f42df4.png"},function(e,n,t){e.exports=t.p+"content/images/check_small_48540c930333871c385fcba2c659ccdb.svg"},function(e,n,t){e.exports=t.p+"content/images/close_white_78e256a7634d924b17cd1f4e94acc4c3.png"},function(e,n,t){e.exports=t.p+"content/images/close_white_cabd4c7fb677d6094db07f0e34de4027.svg"},function(e,n,t){e.exports=t.p+"content/images/close_a1b192d1e73683f78bc1a7454617cca4.png"},function(e,n,t){e.exports=t.p+"content/images/close_790189870c9543725dc3f5a15fb25e46.svg"},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=window;function s(e){var n=this,t=e.fedCredOptions;n.onFedCredButtonClick=o.create(),n.onFedCredHelpButtonClick=o.create(),n.fedCredOptions=t||[],n.fedCredButton_onClick=function(e){n.onFedCredButtonClick(e)},n.fedCredHelpButton_onClick=function(e){n.onFedCredHelpButtonClick(e)}}i.components.register("fed-cred-buttons-control",{viewModel:s,template:t(468),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){e.exports="\x3c!-- "+(t(6),' --\x3e\n\n\x3c!-- ko withProperties: { \'$fedCredButtonsControl\': $data } --\x3e\n<div data-bind="css: { \'app\': $page.backgroundLogoUrl }, externalCss: { \'promoted-fed-cred-box\': true }">\n    <div class="promoted-fed-cred-content" data-bind="css: {\n        \'animate\': $page.useCssAnimations && $page.animate(),\n        \'slide-out-next\': $page.animate.isSlideOutNext,\n        \'slide-in-next\': $page.animate.isSlideInNext,\n        \'slide-out-back\': $page.animate.isSlideOutBack,\n        \'slide-in-back\': $page.animate.isSlideInBack,\n        \'app\': $page.backgroundLogoUrl }">\n\n        \x3c!-- ko ifnot: svr.fIsQrCodePinSupported --\x3e\n        \x3c!-- ko foreach: $fedCredButtonsControl.fedCredOptions --\x3e\n        <div class="row tile">\n            <div class="table" role="button" tabindex="0" data-bind="\n                css: { \'list-item\': svr.fSupportWindowsStyles },\n                pressEnter: $fedCredButtonsControl.fedCredButton_onClick,\n                click: $fedCredButtonsControl.fedCredButton_onClick,\n                ariaLabel: $data.text">\n\n                <div class="table-row">\n                    <div class="table-cell tile-img medium">\n                        \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                            \x3c!-- ko if: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                            <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.lightIconUrl }" />\n                            <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.darkIconUrl }" />\n                            \x3c!-- /ko --\x3e\n                            \x3c!-- ko ifnot: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                            <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.lightIconUrl }" />\n                            <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.darkIconUrl }" />\n                            \x3c!-- /ko --\x3e\n                        \x3c!-- /ko --\x3e\n                    </div>\n                    <div class="table-cell text-left" data-bind="css: { \'content\': !svr.fSupportWindowsStyles }">\n                        <div data-bind="\n                            text: $data.text,\n                            attr: { \'data-test-id\': $data.testId }"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: svr.fEnableQrCodeA11YFixes --\x3e\n        \x3c!-- ko if: svr.fIsQrCodePinSupported --\x3e\n        \x3c!-- ko foreach: $fedCredButtonsControl.fedCredOptions --\x3e\n        <div class="tile-container" data-bind="css: { \'binaryChoice list\': svr.fSupportWindowsStyles }">\n            <div class="row tile">\n                <div class="table" role="button" tabindex="0" data-bind="\n                    css: { \'list-item\': svr.fSupportWindowsStyles },\n                    pressEnter: $fedCredButtonsControl.fedCredButton_onClick,\n                    click: $fedCredButtonsControl.fedCredButton_onClick,\n                    ariaLabel: $data.text + \' \' + $data.helpText">\n\n                    <div class="table-row">\n                        <div class="table-cell tile-img medium">\n                            \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                                \x3c!-- ko if: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                                <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.lightIconUrl }" />\n                                <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.darkIconUrl }" />\n                                \x3c!-- /ko --\x3e\n                                \x3c!-- ko ifnot: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                                <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.lightIconUrl }" />\n                                <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.darkIconUrl }" />\n                                \x3c!-- /ko --\x3e\n                            \x3c!-- /ko --\x3e\n                        </div>\n                        <div class="table-cell text-left" data-bind="css: { \'content\': !svr.fSupportWindowsStyles }">\n                            \x3c!-- ko if: $data.showPreviewBadge --\x3e\n                            <span data-bind="text: $data.text, attr: { \'data-test-id\': $data.testId }"></span>\n                            <sup class="rounded-pill" data-bind="text: str[\'CT_STR_QrCodeScan_Button_PreviewBadge_Label\']"></sup>\n                            \x3c!-- /ko --\x3e\n                            \x3c!-- ko ifnot: $data.showPreviewBadge --\x3e\n                            <div data-bind="text: $data.text, attr: { \'data-test-id\': $data.testId }"></div>\n                            \x3c!-- /ko --\x3e\n\n                            \x3c!-- ko if: $data.showHelpIcon --\x3e\n                            <div><small data-bind="text: $data.helpText"></small></div>\n                            \x3c!-- /ko --\x3e\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \x3c!-- ko if: $data.showHelpIcon --\x3e\n            <div class="row tile">\n                <div class="help-button" role="button" tabindex="0" data-bind="\n                    click: $fedCredButtonsControl.fedCredHelpButton_onClick,\n                    pressEnter: $fedCredButtonsControl.fedCredHelpButton_onClick,\n                    ariaLabel: str[\'CT_STR_QrCodePin_Signin_HelpDescription\']">\n\n                    \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                    <img role="presentation" pngSrc="')+t(19)+'" svgSrc="'+t(20)+'" data-bind="imgSrc" />\n                    <img role="presentation" pngSrc="'+t(21)+'" svgSrc="'+t(22)+'" data-bind="imgSrc" />\n                    \x3c!-- /ko --\x3e\n                </div>\n            </div>\n            \x3c!-- /ko --\x3e\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko ifnot: svr.fEnableQrCodeA11YFixes --\x3e\n        \x3c!-- ko if: svr.fIsQrCodePinSupported --\x3e\n        \x3c!-- ko foreach: $fedCredButtonsControl.fedCredOptions --\x3e\n        <div class="tile-container" data-bind="css: { \'binaryChoice list\': svr.fSupportWindowsStyles }">\n            <div class="row tile">\n                <div class="table" role="button" tabindex="0" data-bind="\n                    css: { \'list-item\': svr.fSupportWindowsStyles },\n                    pressEnter: $fedCredButtonsControl.fedCredButton_onClick,\n                    click: $fedCredButtonsControl.fedCredButton_onClick,\n                    ariaLabel: $data.text">\n\n                    <div class="table-row">\n                        <div class="table-cell tile-img medium">\n                            \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                                \x3c!-- ko if: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                                <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.lightIconUrl }" />\n                                <img class="tile-img medium" role="presentation" data-bind="addEventHandlers, attr: { src: $data.darkIconUrl }" />\n                                \x3c!-- /ko --\x3e\n                                \x3c!-- ko ifnot: svr.fTenantBrandingCdnAddEventHandlers --\x3e\n                                <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.lightIconUrl }" />\n                                <img class="tile-img medium" role="presentation" data-bind="attr: { src: $data.darkIconUrl }" />\n                                \x3c!-- /ko --\x3e\n                            \x3c!-- /ko --\x3e\n                        </div>\n                        <div class="table-cell text-left" data-bind="css: { \'content\': !svr.fSupportWindowsStyles }">\n                            \x3c!-- ko if: $data.showPreviewBadge --\x3e\n                            <span data-bind="text: $data.text, attr: { \'data-test-id\': $data.testId }"></span>\n                            <sup class="rounded-pill" data-bind="text: str[\'CT_STR_QrCodeScan_Button_PreviewBadge_Label\']"></sup>\n                            \x3c!-- /ko --\x3e\n                            \x3c!-- ko ifnot: $data.showPreviewBadge --\x3e\n                            <div data-bind="text: $data.text, attr: { \'data-test-id\': $data.testId }"></div>\n                            \x3c!-- /ko --\x3e\n\n                            \x3c!-- ko if: $data.showHelpIcon --\x3e\n                            <div><small data-bind="text: $data.helpText"></small></div>\n                            \x3c!-- /ko --\x3e\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \x3c!-- ko if: $data.showHelpIcon --\x3e\n            <div class="row tile">\n                <div class="help-button" role="button" tabindex="0" data-bind="\n                    click: $fedCredButtonsControl.fedCredHelpButton_onClick,\n                    pressEnter: $fedCredButtonsControl.fedCredHelpButton_onClick,\n                    ariaLabel: $data.text">\n\n                    \x3c!-- ko component: \'accessible-image-control\' --\x3e\n                    <img role="presentation" pngSrc="'+t(19)+'" svgSrc="'+t(20)+'" data-bind="imgSrc" />\n                    <img role="presentation" pngSrc="'+t(21)+'" svgSrc="'+t(22)+'" data-bind="imgSrc" />\n                    \x3c!-- /ko --\x3e\n                </div>\n            </div>\n            \x3c!-- /ko --\x3e\n        </div>\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n    </div>\n</div>\n\x3c!-- /ko --\x3e'},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=window;function s(){this.onRegisterDialog=o.create(),this.onUnregisterDialog=o.create()}i.components.register("qr-code-pin-help-dialog-content-control",{viewModel:s,template:t(470),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=s},function(e,n,t){e.exports="\x3c!-- "+(t(6),t(29)," --\x3e\n\n<div data-bind=\"component: { name: 'dialog-content-control',\n    params: {\n        dialogId: ")+t(0).DialogId.QrCodePinHelp+',\n        data: {\n            labelledBy: \'qrPinDialogTitle\',\n            describedBy: \'qrPinDialogDesc\' } },\n    event: {\n        registerDialog: onRegisterDialog,\n        unregisterDialog: onUnregisterDialog } }">\n\n    <div id="qrPinDialogTitle" class="row" role="heading" aria-level="1" data-bind="externalCss: { \'title\': true }">\n        \x3c!-- ko component: \'accessible-image-control\' --\x3e\n        <img role="presentation" pngSrc="'+t(62)+'" svgSrc="'+t(63)+'" data-bind="imgSrc" />\n        <img role="presentation" pngSrc="'+t(64)+'" svgSrc="'+t(65)+'" data-bind="imgSrc" />\n        \x3c!-- /ko --\x3e\n\n        <span data-bind="text: str[\'CT_STR_QrCodePin_Signin_ButtonText\']"></span>\n        <sup class="rounded-pill" data-bind="text: str[\'CT_STR_QrCodeScan_Button_PreviewBadge_Label\']"></sup>\n    </div>\n\n    <div id="qrPinDialogDesc" class="row text-body" data-bind="text: str[\'CT_STR_QrCodePin_InfoDialog_PrivacyMessage\']"></div>\n</div>'},function(e,n){e.exports="\x3c!-- ko if: !hideFooter && (showLinks || impressumLink || showIcpLicense) --\x3e\n<div id=\"footerLinks\" class=\"footerNode text-secondary\" data-bind=\"externalCss: { 'footer-links': true }\">\n\n    \x3c!-- ko if: showFooter --\x3e\n        \x3c!-- ko if: !hideTOU --\x3e\n        <a id=\"ftrTerms\" data-bind=\"\n            text: termsText,\n            href: termsLink,\n            click: termsLink_onClick,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }\"></a>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: !hidePrivacy --\x3e\n        <a id=\"ftrPrivacy\" data-bind=\"\n            text: privacyText,\n            href: privacyLink,\n            click: privacyLink_onClick,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }\"></a>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: impressumLink --\x3e\n        <a id=\"ftrImpressum\" data-bind=\"\n            text: str['WF_STR_Footer_LinkDisclaimer_Text'],\n            href: impressumLink,\n            click: impressumLink_onClick,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }\"></a>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: a11yConformeLink --\x3e\n        <a id=\"ftrA11yConforme\" data-bind=\"\n            text: str['WF_STR_Footer_LinkA11yConforme_Text'],\n            href: a11yConformeLink,\n            click: a11yConformeLink_onClick,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }\"></a>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: showIcpLicense --\x3e\n        <a id=\"ftrIcp\" href=\"#\" target=\"_blank\" data-bind=\"\n            text: str['STR_Footer_IcpLicense_Text'],\n            href: icpLink,\n            externalCss: {\n                'footer-content': true,\n                'footer-item': true,\n                'has-background': !useDefaultBackground,\n                'background-always-visible': hasDarkBackground }\"></a>\n        \x3c!-- /ko --\x3e\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- Set attr binding before hasFocusEx to prevent Narrator from losing focus --\x3e\n    <a id=\"moreOptions\" href=\"#\" role=\"button\" data-bind=\"\n        click: moreInfo_onClick,\n        ariaLabel: str['CT_STR_More_Options_Ellipsis_AriaLabel'],\n        attr: { 'aria-expanded': showDebugDetails().toString() },\n        hasFocusEx: focusMoreInfo(),\n        externalCss: {\n            'footer-content': true,\n            'footer-item': true,\n            'debug-item': true,\n            'has-background': !useDefaultBackground,\n            'background-always-visible': hasDarkBackground }\">...</a>\n</div>\n\x3c!-- /ko --\x3e\n\n\x3c!-- ko if: svr.fShowLegalMessagingInline && showLinks --\x3e\n    \x3c!-- ko if: svr.fOobeDisplayServicesConsent || svr.fOobeDisplayUnifiedConsent --\x3e\n    <div class=\"text-caption\" data-bind=\"\n        htmlWithBindings: html['CT_STR_Inline_Legal_Services_Message'],\n        childBindings: {\n            'ftrServices': {\n                click: services_onClick },\n            'ftrServicesInformation': {\n                click: services_onClick } }\"></div>\n    \x3c!-- /ko --\x3e\n    <div class=\"text-caption\" data-bind=\"\n        htmlWithBindings: html['CT_STR_Inline_Legal_Message'],\n        childBindings: {\n            'ftrNext': {\n                css: 'bold' },\n            'ftrTerms': {\n                href: termsLink,\n                click: termsLink_onClick },\n            'ftrPrivacy': {\n                href: privacyLink,\n                click: privacyLink_onClick } }\"></div>\n\x3c!-- /ko --\x3e"},function(e,n,t){var i=t(3),a=t(1),o=window,r=i.Object,s=a.Helper,c=s.history;e.exports=function(e,n,t){var i=this,a=!1,d=e,l=n,u=t,p=[null],f=0;function g(e){if(e&&"undefined"!=typeof e.state&&null!==e.state){var n=e.state;n<f?d():l(),f=n,u(i.getState())}}i.dispose=function(){a&&s.removeEventListener(o,"popstate",g)},i.pushState=function(e){f++,p.splice(f,p.length-f,r.clone(e)),a&&c.pushState(f,"")},i.replaceState=function(e){p[f]=r.clone(e)},i.goBack=function(){f>0&&(a?o.history.back():g({state:f-1}))},i.getState=function(){return f>p.length?f=p.length-1:f<0&&(f=0),null===p[f]?null:r.clone(p[f])},(a=s.isHistorySupported())&&(c.replaceState(f,""),s.addEventListener(o,"popstate",g))}},function(e,n,t){e.exports="\x3c!-- "+(t(474)," --\x3e\n\n<div data-bind=\"css: { 'zero-opacity': hidePaginatedView() }\">\n    \x3c!-- ko if: showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username) --\x3e\n    <div data-bind=\"css: {\n        'animate': animate() && animate.animateBanner(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }\">\n\n        <div data-bind=\"component: { name: 'identity-banner-control',\n            params: {\n                userTileUrl: svr.urlProfilePhoto,\n                displayName: sharedData.displayName || svr.sPOST_Username,\n                isBackButtonVisible: isBackButtonVisible(),\n                focusOnBackButton: isBackButtonFocused(),\n                backButtonDescribedBy: backButtonDescribedBy() },\n            event: {\n                backButtonClick: identityBanner_onBackButtonClick } }\">\n        </div>\n    </div>\n    \x3c!-- /ko --\x3e\n\n    <div class=\"pagination-view\" data-bind=\"css: {\n        'has-identity-banner': showIdentityBanner() && (sharedData.displayName || svr.sPOST_Username),\n        'zero-opacity': hidePaginatedView.hideSubView(),\n        'animate': animate(),\n        'slide-out-next': animate.isSlideOutNext(),\n        'slide-in-next': animate.isSlideInNext(),\n        'slide-out-back': animate.isSlideOutBack(),\n        'slide-in-back': animate.isSlideInBack() }\">\n\n        \x3c!-- ko foreach: views --\x3e\n            \x3c!-- ko if: $parent.currentViewIndex() === $index() --\x3e\n                \x3c!-- ko template: { nodes: [$data], data: $parent } --\x3e\x3c!-- /ko --\x3e\n            \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n    </div>\n</div>")},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=window,s=a.Helper;i.components.register("identity-banner-control",{viewModel:function(e){var n=this,t=e.displayName,i=e.isBackButtonVisible,a=e.backButtonId,r=e.backButtonDescribedBy,c=e.focusOnBackButton||!1;n.onBackButtonClick=o.create(),n.unsafe_displayName=null,n.isBackButtonVisible=i,n.backButtonId=a,n.backButtonDescribedBy=r,n.focusOnBackButton=c,n.showLogo=e.showLogo||!1,n.bannerLogoUrl=e.bannerLogoUrl||"",n.backButton_onClick=function(){n.onBackButtonClick()},n.unsafe_displayName=s.htmlUnescape(t)},template:t(475),synchronous:!r.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(r.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0})},function(e,n,t){e.exports="\x3c!-- "+(t(6),' --\x3e\n\n<div class="identityBanner">\n    \x3c!-- ko if: isBackButtonVisible --\x3e\n    <button type="button" class="backButton" data-bind="\n        attr: { \'id\': backButtonId || \'idBtn_Back\' },\n        ariaLabel: str[\'CT_HRD_STR_Splitter_Back\'],\n        ariaDescribedBy: backButtonDescribedBy,\n        click: backButton_onClick,\n        hasFocus: focusOnBackButton">\n        \x3c!-- ko ifnot: svr.fIsRTLMarket --\x3e\n            \x3c!-- ko component: \'accessible-image-control\' --\x3e\n            <img role="presentation" pngSrc="')+t(476)+'"\n                svgSrc="'+t(477)+'" data-bind="imgSrc" />\n            <img role="presentation" pngSrc="'+t(478)+'"\n                svgSrc="'+t(479)+'" data-bind="imgSrc" />\n            \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: svr.fIsRTLMarket --\x3e\n            \x3c!-- ko component: \'accessible-image-control\' --\x3e\n            <img role="presentation" pngSrc="'+t(480)+'"\n                svgSrc="'+t(481)+'" data-bind="imgSrc" />\n            <img role="presentation" pngSrc="'+t(482)+'"\n                svgSrc="'+t(483)+'" data-bind="imgSrc" />\n            \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n    </button>\n    \x3c!-- /ko --\x3e\n\n    <div id="displayName" class="identity" data-bind="text: unsafe_displayName, attr: { \'title\': unsafe_displayName }"></div>\n</div>'},function(e,n,t){e.exports=t.p+"content/images/arrow_left_white_3007c55710b3b57c1417b83180a50dbf.png"},function(e,n,t){e.exports=t.p+"content/images/arrow_left_white_b8d123feb637875a2545c3cd3b241c60.svg"},function(e,n,t){e.exports=t.p+"content/images/arrow_left_d1229aefd268f350621d48f094122f69.png"},function(e,n,t){e.exports=t.p+"content/images/arrow_left_43280e0ba671a1d8b5e34f1931c4fe4b.svg"},function(e,n,t){e.exports=t.p+"content/images/arrow_right_white_102656e2f3f2b89b0e6e5c3d9d0f24f0.png"},function(e,n,t){e.exports=t.p+"content/images/arrow_right_white_f1c38c69f591d5868143f2df867ac032.svg"},function(e,n,t){e.exports=t.p+"content/images/arrow_right_4414e80d72df57a93999f9a0da2b489a.png"},function(e,n,t){e.exports=t.p+"content/images/arrow_right_e5c5f012788e131326af53682ee17b31.svg"},function(e,n,t){var i=t(2),a=t(0),o=t(14),r=t(1),s=t(3),c=t(25),d=t(5),l=t(9),u=t(4),p=t(42),f=t(12),g=t(36),m=t(7),b=t(34),v=t(17),h=t(24),_=t(18),C=t(11).getInstance(window.ServerData),S=t(8),x=t(27),w=t(15);var y=window,k=a.PaginatedState,P=a.CredentialType,T=a.ApiErrorCodes,D=r.Helper,E=r.Cookies,I=r.QueryString,A=s.String,R=m.AllowedIdentitiesType,L=c.GctResultAction;function B(e){var n=this,r=e.serverData,s=e.serverError,b=e.isInitialView,y=e.displayName,B=e.prefillNames,O=e.flowToken,U=e.otherIdpRedirectUrl,F=e.availableSignupCreds||[],N=e.customStrings,M=e.isCustomizationFailure,V=e.userIdLabel,j=e.cantAccessYourAccountText,H=e.hideAccountResetCredentials,W=e.accessRecoveryLink,$=e.boilerPlateText,G=r.str,q=r.fAllowPhoneSignIn||r.fAllowPhoneInput,K=r.fAllowSkypeNameLogin,z=r.fCheckWindowsUsernameFormat,Q=r.urlResetPassword,J=r.urlGetOneTimeCode,X=r.urlSkipZtd,Y=r.sCtx,Z=r.staticTenantBranding,ee=r.iAllowedIdentities,ne=r.sRemoteConnectAppName,te=r.sRemoteClientIp,ie=r.sRemoteAppLocation,ae=r.remoteLoginConfig,oe=r.userRoutingCookieConfig,re=r.fAllowCancel,se=!!r.fIsFidoSupported,ce=r.fCheckApiCanary,de=r.urlLinkedInFed,le=r.urlGitHubFed,ue=r.urlGoogleFed,pe=r.urlFacebookFed,fe=r.urlOtherIdpSignup,ge=(r.fIsTokenBroker,r.fPreferAssociate,r.sZtdTenantName),me=r.iPromotedFedCredType,be=r.arrPromotedFedCredTypes||[],ve=!!r.fShowSignInWithGitHubOnlyOnCredPicker,he=!!r.fShowSignInWithFidoOnUsernameView&&!r.fIsPasskeySupportEnabled,_e=!!r.fShowOfflineAccountLearnMore,Ce=r.sUnauthSessionID,Se=r.iRequestLCID,xe=(r.fHideOfflineAccountNewTitleString,r.oGetCredTypeResult),we=r.urlSignUp,ye=r.urlAadSignup,ke=r.urlForgotUsername,Pe=r.sSiteId,Te=r.sClientId,De=r.sForwardedClientId,Ee=r.sNoPaBubbleVersion,Ie=r.fShowRemoteConnectLocationPage,Ae=r.fOfflineAccountVisible,Re=r.fIsRestrictedWsi,Le=r.fShowSignInOptionsAsButton,Be=r.fShowForgotUsernameLink,Oe=r.fEnableUserStateFix,Ue=(r.iMsaServerLottieId,r.urlOidcDiscoveryEndpointFormat),Fe=(r.fOobeDisplayServicesConsent,r.fOobeDisplayUnifiedConsent,r.showCantAccessAccountLink),Ne=!!r.fEnableShowPickerCredObservable,Me=r.fUsePromotedFedCredTypesArray,Ve=r.arrExternalTrustedRealmFederatedIdps||[],je=r.fEnableLivePreview,He=r.fUseRemoteConnectDescriptionStrings,We=r.fSamlAndOidcCompatibleUx,$e=r.fUseWebviewFidoCustomProtocol,Ge=r.fEnableDFPIntegration,qe=r.sFidoChallenge,Ke=r.urlPostAad,ze=r.urlPostMsa,Qe=r.urlFidoLogin,Je=r.sessionId,Xe=r.canary,Ye=r.sCanaryTokenName,Ze=r.sFTName,en=r.fEnablePasskeyAutofillUI,nn=w.getInstance(r),tn=r.fAllowUserNameSignIn,an=r.arrSignInIdentifierConfigs,on=!!J,rn={},sn=null,cn=!1,dn=null,ln={},un={},pn=i.observable(!1),fn=i.observable(O).extend({flowTokenUpdate:r});function gn(e,n){return(on?function(e){return new d((function(n,t){vn(!0);var i={OriginalRequest:Y,FlowToken:fn()};e&&(i.Channel=e),new f({checkApiCanary:ce}).Json({url:J,eventId:S.EventIds.Api_GetOneTimeCode},i,n,t,a.DefaultRequestTimeout)}))}(e):function(e){return new d((function(n,t){vn(!0);var i={purpose:e,flowToken:fn(),unauthSessionId:Ce,lcid:Se,siteId:Pe,clientId:Te,forwardedClientId:De,noPaBubbleVersion:Ee,successCallback:n,failureCallback:t};new h(i).sendRequest()}))}(n)).then(mn,bn)}function mn(e){return vn(!1),!on&&e.getResponseJson&&(e=e.getResponseJson()),{success:!0,userCode:e.UserCode,deviceCode:e.DeviceCode||e.SessionLookupKey}}function bn(e){if(vn(!1),on&&e&&e.error)switch(e.error.code){case T.AuthFailure:n.usernameTextbox.error.setNonBlockingError(G.CT_PWD_STR_Error_FlowTokenExpired);break;default:n.usernameTextbox.error.setNonBlockingError(G.CT_PWD_STR_Error_GetOneTimeCodeError)}else n.usernameTextbox.error.setNonBlockingError(G.CT_PWD_STR_Error_GetOneTimeCodeError);return n.setDefaultFocus(),{success:!1}}function vn(e){n.isRequestPending(e),n.onSetPendingRequest(e)}function hn(e){n.onRedirect({url:e.redirectUrl,eventOptions:{eventId:e.eventId}},e.redirectPostParams,e.isIdpRedirect)}function _n(e){return!(n.allowedUserNameSignInRegexes.length>0)||n.allowedUserNameSignInRegexes.some((function(n){return n.test(e)}))}function Cn(e){if(n.hasFido(e),Me){var t=be.indexOf(P.LinkedIn)>=0,a=be.indexOf(P.GitHub)>=0,o=be.indexOf(P.Google)>=0,r=be.indexOf(P.Facebook)>=0;n.availableCredsWithoutUsername([].concat(!ne&&n.allowRemoteLogin?{credType:P.RemoteLogin}:[],e?{credType:P.Fido}:[],U&&fe?{credType:P.OtherMicrosoftIdpFederation,redirectUrl:U}:[],!de||fe||t?[]:{credType:P.LinkedIn,redirectUrl:de},!le||fe||a?[]:{credType:P.GitHub,redirectUrl:le,shownOnlyOnPicker:ve},ue&&!o?{credType:P.Google,redirectUrl:ue}:[],pe&&!r?{credType:P.Facebook,redirectUrl:pe}:[],Ae?{credType:P.OfflineAccount}:[])),We||i.utils.arrayForEach(Ve,(function(e){var t=e.IdpType;be.indexOf(t)<0&&n.availableCredsWithoutUsername().push({credType:t,redirectUrl:e.IdpSignInUrl,isExternalFederatedIdp:!0})}))}else n.availableCredsWithoutUsername([].concat(!ne&&n.allowRemoteLogin?{credType:P.RemoteLogin}:[],e?{credType:P.Fido}:[],U&&fe?{credType:P.OtherMicrosoftIdpFederation,redirectUrl:U}:[],de&&!fe&&me!==P.LinkedIn?{credType:P.LinkedIn,redirectUrl:de}:[],le&&!fe&&me!==P.GitHub?{credType:P.GitHub,redirectUrl:le,shownOnlyOnPicker:ve}:[],ue?{credType:P.Google,redirectUrl:ue}:[],pe?{credType:P.Facebook,redirectUrl:pe}:[],Ae?{credType:P.OfflineAccount}:[]));We&&i.utils.arrayForEach(Ve,(function(e){e.Promoted||n.availableCredsWithoutUsername().push({credType:e.IdpType,redirectUrl:e.IdpSignInUrl,redirectPostParams:e.SignInPostParams,displayName:e.DisplayName,isExternalFederatedIdp:!0})})),n.onUpdateAvailableCredsWithoutUsername(n.availableCredsWithoutUsername()),e&&(n.fidoLinkText(G.CT_PWD_STR_SwitchToFidoCrossPlatform_Link),l.throwUnhandledExceptionOnRejection(_.isPlatformAuthenticatorAvailable($e).then(null,(function(){return!1})).then((function(e){e&&(n.fidoLinkText(G.CT_PWD_STR_SwitchToFido_Link),n.isPlatformAuthenticatorAvailable(!0))}))))}function Sn(){_.isConditionalMediationAvailable().then((function(e){var t;e&&(t=A.extractDomainFromUrl(Qe).toLowerCase(),_.getAssertion(qe,null,t,!0,en).then((function(e){nn&&(nn.set("PasskeyAutofillUIShown"),nn.post(!1,"PasskeyAutofillUIShown"));var t=e.response,i={id:e.id,clientDataJSON:x.arrayBufferToBase64UrlString(t.clientDataJSON),authenticatorData:x.arrayBufferToBase64UrlString(t.authenticatorData),signature:x.arrayBufferToBase64UrlString(t.signature),userHandle:x.arrayBufferToBase64UrlString(t.userHandle)};if(i.userHandle){var a=_.getIdpFromUserHandle(t.userHandle);if(ee!==R.Both&&a!==ee)return void(a===R.MsaOnly?n.usernameTextbox.error.setNonBlockingError(G.CT_PWD_STR_Error_UsernameNotExist_ConsumerEmail):n.usernameTextbox.error.setNonBlockingError(G.CT_FIDO_STR_Error_Unknown));if(n.fidoAssertion=JSON.stringify(i),a===R.MsaOnly&&ze)n.postUrl=ze;else{if(a!==R.AadOnly||!Ke)return void n.usernameTextbox.error.setNonBlockingError(G.CT_FIDO_STR_Error_Unknown);n.postUrl=Ke}_.postFidoAssertionToIdp(vn,n.fidoAssertion,null,Je,Y,Ye,Xe,Ze,fn(),n.postUrl,n.onRedirect)}}))["catch"]((function(e){nn&&(nn.set("PasskeyAutofillUIError",e,!0),nn.post(!1,"PasskeyAutofilllUIError"))})))}))}n.onSwitchView=u.create(),n.onRedirect=u.create(),n.onUpdateDFPUrl=u.create(),n.onSetPendingRequest=u.create(),n.onRegisterDialog=u.create(),n.onUnregisterDialog=u.create(),n.onShowDialog=u.create(C.getDefaultEventTracingOptions(m.ClientTracingEventIds.ComponentEvent_LoginPaginatedUsernameView_onShowDialog,!0)),n.onAgreementClick=u.create(C.getDefaultEventTracingOptions(m.ClientTracingEventIds.ComponentEvent_LoginPaginatedUsernameView_onAgreementClick,!0)),n.onUpdateAvailableCredsWithoutUsername=u.create(),n.onRestoreIsRecoveryAttemptPost=u.create(),n.usernameTextbox=new g(p.errorComputed((function(){if(!pn()){var e=s||null;return s=null,e}return function(){var e=n.usernameTextbox.value();if(tn){if(z&&e&&e.indexOf("\\")>0)return G.CT_PWD_STR_Error_InvalidUsername_WindowsFormat;if(!e||!(A.isEmailAddress(e)||q&&A.isPhoneNumber(e)||K&&A.isSkypeName(e)||tn&&_n(e)))return G.CT_PWD_STR_Error_InvalidUsername;if(!(A.isEmailAddress(e)||K&&A.isSkypeName(e))&&q&&A.isPhoneNumber(e)&&!e.match(a.Regex.PhoneNumberValidation))return G.CT_PWD_STR_Error_InvalidPhoneFormatting;if(!A.isEmailAddress(e)&&tn&&!_n(e))return G.STR_SSSU_SignIn_InvalidUsername||G.CT_PWD_STR_Error_InvalidUsername}else{if(z&&e&&e.indexOf("\\")>0)return G.CT_PWD_STR_Error_InvalidUsername_WindowsFormat;if(!e||!(A.isEmailAddress(e)||q&&A.isPhoneNumber(e)||K&&A.isSkypeName(e)))return G.CT_PWD_STR_Error_InvalidUsername;if(!A.isEmailAddress(e)&&(!K||!A.isSkypeName(e))&&A.isPhoneNumber(e)&&!e.match(a.Regex.PhoneNumberValidation))return G.CT_PWD_STR_Error_InvalidPhoneFormatting}return null}()}))),n.usernameTextbox.value.extend({logValue:C.getPIITextBoxPropertyLogOption(n,{eventId:m.ClientTracingEventIds.PropertyValue_LoginPaginatedUsernameView_Username})}),n.usernameTextbox.error.extend({logValue:C.getPropertyLogOption(n,{eventId:m.ClientTracingEventIds.PropertyValue_LoginPaginatedUsernameView_ClientError})}),n.passwordBrowserPrefill=i.observable(),n.prefillNames=i.observableArray(),n.isRequestPending=i.observable(!1),n.isBackButtonVisible=i.observable(!1),n.isSecondaryButtonVisible=i.observable(!1),n.secondaryButtonText=i.observable(),n.showTileLogo=i.observable(!1),n.isPlatformAuthenticatorAvailable=i.observable(!1),n.fidoLinkText=i.observable(),n.hasFido=i.observable(!1),n.availableCredsWithoutUsername=i.observableArray([]),n.customTitle=i.observable(null),n.customDescription=i.observable(null),n.tenantBranding=null,n.isInitialView=b,n.pageDescription=null,n.unsafe_pageTitle=null,n.unsafe_subHeader=null,n.showFidoLinkInline=he,n.hideCantAccessYourAccount=!0,n.unsafe_cantAccessYourAccountText=G.WF_STR_CantAccessAccount_Text,n.userIdLabel=null,n.cantAccessYourAccountText=null,n.accessRecoveryLink=null,n.boilerPlateText=null,n.fidoAssertion=null,n.postUrl=null,n.allowedUserNameSignInRegexes=[],n.showCredPicker=!!Ne&&i.observable(!1),C.attachViewLoadClientTracingOptions(n,{eventId:m.ClientTracingEventIds.Event_LoginPaginatedUsernameView_onLoad}),n.isOfflineAccountVisible=Ae,n.allowRemoteLogin=!1,n.saveSharedData=function(e){Oe&&e.useCredWithoutUsername&&(cn=!0,delete e.useCredWithoutUsername);var t=rn.result,a=cn?"":n.usernameTextbox.value();e.flowToken=fn(),e.username=A.cleanseUsername(a),e.displayName=a,e.passwordBrowserPrefill=n.passwordBrowserPrefill(),e.remoteLoginUserCode=t?t.userCode:null,e.remoteLoginDeviceCode=t?t.deviceCode:null,e.proofConfirmation="",e.useEvictedCredentials=!1,e.showCredViewBrandingDesc=!1,e.isSignupPost=!1,cn&&(e.availableCreds=n.availableCredsWithoutUsername(),Oe&&e.fidoParams&&(e.fidoParams.allowList=null)),i.utils.extend(e,ln)},n.getState=function(){return{unsafe_displayName:n.usernameTextbox.value(),gctRequestHelperState:dn.getState()}},n.restoreState=function(e){e&&(dn.restoreState(e.gctRequestHelperState),n.usernameTextbox.value(e.unsafe_displayName))},n.setDefaultFocus=function(){n.usernameTextbox.focused(!0)},n.primaryButton_onClick=function(){if(pn(!0),n.usernameTextbox.error.isBlocking())n.setDefaultFocus();else{var e,t=n.usernameTextbox.value();e=t,oe&&E.isCookieSafeValue(e)&&E.writeWithExpiration(oe.name,e,oe.secure,E.getPersistDate(),oe.domain,oe.path),vn(!0),l.throwUnhandledExceptionOnRejection(dn.sendAsync(U,t,fn()).then((function(e){switch(vn(!1),e.flowToken&&fn(e.flowToken),Ge&&e.sharedData&&e.sharedData.urlDeviceFingerprinting&&n.onUpdateDFPUrl(e.sharedData.urlDeviceFingerprinting),e.action){case L.ShowError:n.usernameTextbox.error.setError(e.error,e.isBlockingError),un=i.utils.extend(e.sharedData,e.viewParams||{}),n.setDefaultFocus();break;case L.SwitchView:ln=i.utils.extend(e.sharedData,e.viewParams||{}),e.viewId===k.RemoteLoginPolling?n.remoteLogin_onClick():n.onSwitchView(e.viewId);break;case L.Redirect:hn(e)}})))}},n.secondaryButton_onClick=function(){_e?n.onSwitchView(k.LearnMoreOfflineAccount):n.onSwitchView(k.Previous)},n.signup_onClick=function(){we?hn(dn.getSignupRedirectGctResult(n.usernameTextbox.value())):n.onSwitchView(F.length>0?k.SignupCredentialPicker:k.SignupUsername)},n.aadSignup_onClick=function(){n.onRedirect({url:I.appendOrReplace(ye,"email",encodeURIComponent(n.usernameTextbox.value())),eventOptions:{eventId:S.EventIds.Redirect_AADSignUpPage}})},n.otherIdpLogin_onClick=function(){hn(dn.getOtherIdpRedirectGctResult(U,n.usernameTextbox.value()))},n.sendOtcLink_onClick=function(){l.throwUnhandledExceptionOnRejection(gn(v.Channel.EmailAddress).then((function(e){e.success&&(ln=un,n.onSwitchView(k.OneTimeCode))})))},n.recoverUsername_onClick=function(){var e=I.appendOrReplace(ke,"mn",encodeURIComponent(n.usernameTextbox.value()));n.onRedirect({url:e,eventOptions:{eventId:S.EventIds.Redirect_MSAUserRecoveryPage}})},n.skip_onClick=function(){null},n.skipZtd_onClick=function(){n.onRedirect({url:X,eventOptions:{eventId:S.EventIds.Redirect_SkipZeroTouch}})},n.privacy_onClick=function(){n.onSwitchView(k.ViewAgreement)},n.tou_onClick=function(){n.onSwitchView(k.TermsOfUse)},n.services_onClick=function(){n.onSwitchView(k.SeeHowDataIsManaged)},n.footer_agreementClick=function(e){n.onAgreementClick(e)},n.remoteLogin_onClick=function(){l.throwUnhandledExceptionOnRejection(gn(null,v.Purpose.XboxRemoteConnect).then((function(e){e.success&&(rn.result=e,n.onSwitchView(k.RemoteLoginPolling))})))},n.learnMore_onClick=function(){n.onSwitchView(k.LearnMore)},n.cantAccessAccount_onClick=function(){ee===R.Both?n.onSwitchView(k.ResetPasswordSplitter):n.onRedirect({url:Q,eventOptions:{eventId:S.EventIds.Redirect_ResetPasswordPage}})},n.switchToFidoCredLink_onClick=function(){Oe?n.noUsernameCredSwitchLink_onSwitchView(k.Fido):n.onSwitchView(k.Fido)},n.noUsernameCredSwitchLink_onSwitchView=function(e){switch(cn=!0,e){case k.RemoteLoginPolling:n.remoteLogin_onClick();break;default:n.onSwitchView(e)}},n.tileLogo_onLoad=function(){n.showTileLogo(!0)},function(){n.allowRemoteLogin=ae&&ae.allowRemoteLogin,dn=new c(r),xe&&xe.Username&&dn.cacheResponse(xe.Username,xe),Ne?l.throwUnhandledExceptionOnRejection(D.isFidoSupportedAsync(se,$e).then(Cn,(function(){Cn(!1)})).then((function(){n.showCredPicker((n.availableCredsWithoutUsername().length>0||Ue||Be||Ae)&&!Le&&!Re)}))):(l.throwUnhandledExceptionOnRejection(D.isFidoSupportedAsync(se,$e).then(Cn,(function(){Cn(!1)}))),n.showCredPicker=(n.availableCredsWithoutUsername()>0||Ue||Be||Ae)&&!Le&&!Re);var e,s=o.loadTenantBranding(Z);if(s&&s.UserIdLabel&&(s.unsafe_userIdLabel=D.htmlUnescape(s.UserIdLabel)),n.tenantBranding=s,!M&&N&&t.e(37).then(function(){var e=t(501);n.customTitle(e.customStringResolver(N,"SignIn_Title")),n.customDescription(e.customStringResolver(N,"SignIn_Description"))}.bind(null,t))["catch"](t.oe),ge){var d=D.htmlUnescape(ge);n.unsafe_pageTitle=A.format(G.CT_Win10_STR_Username_Title_WithOrgDomain,d),n.unsafe_subHeader=A.format(G.CT_Win10_STR_Username_SubHeader_WithOrgDomain,d)}else n.unsafe_pageTitle=G.WF_STR_HeaderDefault_Title_With_Brand,n.unsafe_subHeader=G.CT_Win10_STR_SignInPage_UsernameLabel;G.WF_STR_Default_Desc&&!Ie&&(n.pageDescription=He?A.format(G.WF_STR_Default_Desc,te||ie):A.format(G.WF_STR_Default_Desc,ne,te||ie)),y?n.usernameTextbox.value(D.htmlUnescape(y)):B&&0!==B.length&&(n.usernameTextbox.value(B[0]),B.length>1&&(i.utils.arrayForEach(B,(function(e){n.prefillNames.push({text:e,value:e})})),n.prefillNames.push({text:G.CT_WPIL_STR_Android_UseDifferentAddress,value:null}),e=n.usernameTextbox.value.subscribe((function(t){null===t&&(n.prefillNames.removeAll(),e.dispose(),pn(!1),n.usernameTextbox.value(""),n.usernameTextbox.focused(!0))})))),n.usernameTextbox.value.subscribe((function(e){e&&(sn?e.toLowerCase()!==sn.toLowerCase()&&n.passwordBrowserPrefill(null):sn=e)})),tn&&an&&an.length>0&&i.utils.arrayForEach(an,(function(e){e.enabled&&e.useRegexForSignIn&&e.validationRegex&&(e.type!==a.SignInIdentifierTypes.CustomUsername&&e.type!==a.SignInIdentifierTypes.Username||n.allowedUserNameSignInRegexes.push(new RegExp(e.validationRegex.trim())))})),en&&Sn(),b?re&&(n.isBackButtonVisible(!0),n.isSecondaryButtonVisible(!0)):(n.isBackButtonVisible(!0),n.isSecondaryButtonVisible(!0));var u=o.getLayoutTemplateConfig(s);n.hideCantAccessYourAccount=!Fe||u.hideAccountResetCredentials,s.CantAccessYourAccountText&&(n.unsafe_cantAccessYourAccountText=D.htmlUnescape(s.CantAccessYourAccountText)),n.accessRecoveryLink=s.AccessRecoveryLink,je&&(n.hideCantAccessYourAccount=H,n.accessRecoveryLink=W,n.userIdLabel=D.htmlUnescape(V()),n.cantAccessYourAccountText=D.htmlUnescape(j()),n.boilerPlateText=D.htmlUnescape($())),Re&&(n.hideCantAccessYourAccount=!0,n.showFidoLinkInline=!1),n.onRestoreIsRecoveryAttemptPost()}()}b.applyExtenders(i),i.components.register("login-paginated-username-view",{viewModel:B,template:t(489),synchronous:!y.ServerData.iMaxStackForKnockoutAsyncComponents||r.Helper.isStackSizeGreaterThan(y.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=B},function(e,n,t){var i={"./msaServerSignInLottie_1.json":486,"./msaServerSignInLottie_2.json":487,"./msaServerSignInLottie_3.json":488};function a(e){var n=o(e);return t(n)}function o(e){if(!t.o(i,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=o,e.exports=a,a.id=485},function(e,n,t){e.exports=t.p+"content/images/msaserversigninlottie_1_52461801bec60a67f8c681a65c7e78b3.json"},function(e,n,t){e.exports=t.p+"content/images/msaserversigninlottie_2_e7e9733ff45e1d0759c5ffcefea32c6d.json"},function(e,n,t){e.exports=t.p+"content/images/msaserversigninlottie_3_31f7275620c961f494e4cfc6553d1ad7.json"},function(e,n,t){e.exports="\x3c!-- "+(t(30),t(37),t(13),t(35),t(38),t(33),' --\x3e\n\n<div data-bind="component: { name: \'header-control\',\n    params: {\n        serverData: svr,\n        title: customTitle() || str[\'WF_STR_HeaderDefault_Title\'],\n        headerDescription: customDescription() } }">\n</div>\n\n\x3c!-- ko if: pageDescription && !svr.fHideLoginDesc --\x3e\n<div class="row text-body no-margin-top">\n    <div id="loginDescription" data-bind="\n        htmlWithBindings: pageDescription,\n        childBindings: {\n            \'appName\': { css: { \'bold\': true } },\n            \'ipAddress\': { css: { \'bold\': true } },\n            \'location\': { css: { \'bold\': true } },\n            \'learnMoreLink\': {\n                click: learnMore_onClick,\n                ariaLabel: str[\'MOBILE_STR_SignIn_MSAcctHelpHeading\'],\n                visible: !svr.fHideLearnMoreLink } }">\n    </div>\n</div>\n\x3c!-- /ko --\x3e\n\n<div class="row">\n    \x3c!-- ko if: svr.fEnableAriaLiveUpdates --\x3e\n      \x3c!-- ko if: usernameTextbox.error --\x3e\n      <div role="alert"\n          aria-live="assertive"\n          aria-atomic="true"\n          aria-relevant="all"\n          class="col-md-24"\n          id="usernameError" data-bind="\n          externalCss: { \'error\': true },\n          htmlWithBindings: usernameTextbox.error,\n          childBindings: {\n              \'idA_PWD_SignUp\': { href: svr.urlSignUp, click: signup_onClick },\n              \'aadSignup\': { href: svr.urlAadSignup, click: aadSignup_onClick },\n              \'aadSelfSignup\': { click: signup_onClick },\n              \'otherIdpLogin\': { href: svr.urlGoToAADError, click: otherIdpLogin_onClick },\n              \'sendOtcLink\': { click: sendOtcLink_onClick },\n              \'recoverUsername\': { href: svr.urlForgotUsername, click: recoverUsername_onClick } }">\n      </div>\n      \x3c!-- /ko --\x3e\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko ifnot: svr.fEnableAriaLiveUpdates --\x3e\n    <div role="alert" aria-live="assertive">\n        \x3c!-- ko if: usernameTextbox.error --\x3e\n        <div class="col-md-24" id="usernameError" data-bind="\n            externalCss: { \'error\': true },\n            htmlWithBindings: usernameTextbox.error,\n            childBindings: {\n                \'idA_PWD_SignUp\': { href: svr.urlSignUp, click: signup_onClick },\n                \'aadSignup\': { href: svr.urlAadSignup, click: aadSignup_onClick },\n                \'aadSelfSignup\': { click: signup_onClick },\n                \'otherIdpLogin\': { href: svr.urlGoToAADError, click: otherIdpLogin_onClick },\n                \'sendOtcLink\': { click: sendOtcLink_onClick },\n                \'recoverUsername\': { href: svr.urlForgotUsername, click: recoverUsername_onClick } }">\n        </div>\n        \x3c!-- /ko --\x3e\n    </div>\n    \x3c!-- /ko --\x3e\n\n    <div class="form-group col-md-24">\n        \x3c!-- ko if: prefillNames().length > 1 --\x3e\n        <select id="usernames" class="win-dropdown form-control"\n            data-bind="options: prefillNames, optionsText: \'text\', optionsValue: \'value\', value: usernameTextbox.value, externalCss: { \'has-error\': usernameTextbox.error }">\n        </select>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko ifnot: prefillNames().length > 1 --\x3e\n        <div class="placeholderContainer" data-bind="component: { name: \'placeholder-textbox-field\',\n            publicMethods: usernameTextbox.placeholderTextboxMethods,\n            params: {\n                serverData: svr,\n                hintText: svr.fEnableLivePreview ? userIdLabel : tenantBranding.unsafe_userIdLabel || str[\'STR_SSSU_Username_Hint\'] || str[\'CT_PWD_STR_Email_Example\'],\n                hintCss: \'placeholder\' + (!svr.fAllowPhoneSignIn ? \' ltr_override\' : \'\') },\n            event: {\n                updateFocus: usernameTextbox.textbox_onUpdateFocus } }">\n\n            <input type="email" name="loginfmt" id="i0116" maxlength="113" class="form-control ltr_override" aria-required="true"\n                data-report-event="Signin_Email_Phone_Skype" data-report-trigger="click" data-report-value="Email_Phone_Skype_Entry"\n                data-bind="\n                    attr: { lang: svr.fApplyAsciiRegexOnInput ? null : \'en\',\n                    autocomplete: svr.fEnablePasskeyAutofillUI ? \'username webauthn\' : \'username\' },\n                    externalCss: {\n                        \'input\': true,\n                        \'text-box\': true,\n                        \'has-error\': usernameTextbox.error },\n                    ariaLabel: tenantBranding.unsafe_userIdLabel || str[\'CT_PWD_STR_Username_AriaLabel\'],\n                    ariaDescribedBy:\n                     svr.fWin10HostAccessibilityFix ?\n                        \'winBodyHeader winBodySubHeader loginHeader\' + (pageDescription && !svr.fHideLoginDesc ? \' loginDescription usernameError\' : \' usernameError\') :\n                        \'loginHeader\' + (pageDescription && !svr.fHideLoginDesc ? \' loginDescription usernameError\' : \' usernameError\'),\n                    textInput: usernameTextbox.value,\n                    hasFocusEx: usernameTextbox.focused,\n                    placeholder: $placeholderText"\n                     />\n\n            <input name="passwd" type="password" id="i0118" data-bind="moveOffScreen, textInput: passwordBrowserPrefill" />\n\n        </div>\n        \x3c!-- /ko --\x3e\n    </div>\n</div>\n\n<div data-bind="css: { \'position-buttons\': !tenantBranding.BoilerPlateText && !boilerPlateText }, externalCss: { \'password-reset-links-container\': true }">\n    <div class="row">\n        <div class="col-md-24">\n            <div class="text-13">\n                \x3c!-- ko if: svr.fCBShowSignUp && !svr.fDoIfExists && !svr.fCheckProofForAliases --\x3e\n                \x3c!-- ko if: svr.fIsSelfServiceSignupUxEnabled --\x3e\n                <div class="form-group">\n                    <a id="signup" data-bind="\n                        text: str[\'STR_SSSU_Signup_Link\'],\n                        href: svr.urlSignUp || \'#\',\n                        click: signup_onClick"></a>\n                </div>\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko ifnot: svr.fIsSelfServiceSignupUxEnabled --\x3e\n                <div class="form-group" data-bind="\n                    htmlWithBindings: html[\'WF_STR_SignUpLink_Text\'],\n                    childBindings: {\n                        \'signup\': {\n                            href: svr.urlSignUp || \'#\',\n                            ariaLabel: svr.urlSignUp ? str[\'WF_STR_SignupLink_AriaLabel_Text\'] : str[\'WF_STR_SignupLink_AriaLabel_Generic_Text\'],\n                            click: signup_onClick } }">\n                </div>\n                \x3c!-- /ko --\x3e\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko ifnot: hideCantAccessYourAccount --\x3e\n                <div class="form-group">\n                    <a id="cantAccessAccount" name="cannotAccessAccount" data-bind="\n                        text: svr.fEnableLivePreview ? cantAccessYourAccountText : unsafe_cantAccessYourAccountText,\n                        click: accessRecoveryLink ? null : cantAccessAccount_onClick,\n                        attr: { target: accessRecoveryLink && \'_blank\' },\n                        href: accessRecoveryLink || \'#\'"></a>\n                </div>\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko if: showFidoLinkInline && hasFido() && (availableCredsWithoutUsername().length >= 2 || svr.fShowForgotUsernameLink || isOfflineAccountVisible) --\x3e\n                <div class="form-group">\n                    <a id="idA_PWD_SwitchToFido" name="switchToFido" href="#" data-bind="\n                        text: fidoLinkText,\n                        click: switchToFidoCredLink_onClick"></a>\n\n                    \x3c!-- ko component: { name: \'fido-help-button-control\',\n                        params: {\n                            isPlatformAuthenticatorAvailable: isPlatformAuthenticatorAvailable() },\n                        event: {\n                            registerDialog: onRegisterDialog,\n                            unregisterDialog: onUnregisterDialog,\n                            showDialog: onShowDialog } } --\x3e\n                    \x3c!-- /ko --\x3e\n                </div>\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko if: svr.fEnableShowPickerCredObservable --\x3e\n                    \x3c!-- ko if: showCredPicker() --\x3e\n                    <div class="form-group" data-bind="\n                        component: { name: \'cred-switch-link-control\',\n                            params: {\n                                serverData: svr,\n                                availableCreds: availableCredsWithoutUsername(),\n                                showForgotUsername: svr.fShowForgotUsernameLink },\n                            event: {\n                                switchView: noUsernameCredSwitchLink_onSwitchView,\n                                redirect: onRedirect,\n                                registerDialog: onRegisterDialog,\n                                unregisterDialog: onUnregisterDialog,\n                                showDialog: onShowDialog } }">\n                    </div>\n                    \x3c!-- /ko --\x3e\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko ifnot: svr.fEnableShowPickerCredObservable --\x3e\n                    \x3c!-- ko if: showCredPicker --\x3e\n                    <div class="form-group" data-bind="\n                        component: { name: \'cred-switch-link-control\',\n                            params: {\n                                serverData: svr,\n                                availableCreds: availableCredsWithoutUsername(),\n                                showForgotUsername: svr.fShowForgotUsernameLink },\n                            event: {\n                                switchView: noUsernameCredSwitchLink_onSwitchView,\n                                redirect: onRedirect,\n                                registerDialog: onRegisterDialog,\n                                unregisterDialog: onUnregisterDialog,\n                                showDialog: onShowDialog } }">\n                    </div>\n                    \x3c!-- /ko --\x3e\n                \x3c!-- /ko --\x3e\n\n                \x3c!-- ko if: svr.urlSkipZtd --\x3e\n                <div class="form-group">\n                    <a id="idSkipZtdLink" name="ztdChangeAccount" href="#" data-bind="\n                        text: str[\'CT_Win10_STR_ChangeAccount\'],\n                        click: skipZtd_onClick"></a>\n                </div>\n                \x3c!-- /ko --\x3e\n            </div>\n        </div>\n    </div>\n</div>\n\n\x3c!-- ko if: svr.fShowLegalMessagingInline --\x3e\n<div data-bind="component: { name: \'footer-control\',\n    params: {\n        serverData: svr,\n        showLinks: true,\n        hideFooter: true },\n    event: {\n        switchView: onSwitchView,\n        agreementClick: footer_agreementClick } }">\n</div>\n\x3c!-- /ko --\x3e\n\n<div class="win-button-pin-bottom" data-bind="css : { \'boilerplate-button-bottom\': tenantBranding.BoilerPlateText || boilerPlateText }">\n    <div class="row" data-bind="css: { \'move-buttons\': tenantBranding.BoilerPlateText || boilerPlateText }">\n        <div data-bind="component: { name: \'footer-buttons-field\',\n            params: {\n                serverData: svr,\n                isPrimaryButtonEnabled: !isRequestPending(),\n                isPrimaryButtonVisible: svr.fShowButtons,\n                isSecondaryButtonEnabled: true,\n                isSecondaryButtonVisible: svr.fShowButtons && isSecondaryButtonVisible(),\n                secondaryButtonText: secondaryButtonText() },\n            event: {\n                primaryButtonClick: primaryButton_onClick,\n                secondaryButtonClick: secondaryButton_onClick } }">\n        </div>\n    </div>\n</div>\n\n\x3c!-- ko if: tenantBranding.BoilerPlateText || boilerPlateText --\x3e\n<div id="idBoilerPlateText" class="wrap-content" data-bind="\n    htmlWithMods: svr.fEnableLivePreview ? boilerPlateText : tenantBranding.BoilerPlateText,\n    htmlMods: { filterLinks: svr.fIsHosted },\n    css: { \'transparent-lightbox\': tenantBranding.UseTransparentLightBox },\n    externalCss: { \'boilerplate-text\': true }"></div>\n\x3c!-- /ko --\x3e')},function(e,n){e.exports='\x3c!-- ko withProperties: { \'$placeholderText\': placeholderText } --\x3e\n    \x3c!-- ko template: { nodes: $componentTemplateNodes, data: $parent } --\x3e\x3c!-- /ko --\x3e\n\x3c!-- /ko --\x3e\n\x3c!-- ko ifnot: usePlaceholderAttribute --\x3e\n<div class="placeholderInnerContainer" data-bind="visible: placeholderVisible, click: placeholder_onClick">\n    <div class="placeholder" aria-hidden="true" data-bind="text: hintText, css: hintCss"></div>\n</div>\n\x3c!-- /ko --\x3e'},function(e,n,t){e.exports="\x3c!-- "+(t(33),' --\x3e\n\n<div class="form-group">\n    \x3c!-- ko if: showSwitchToCredPickerLink --\x3e\n    <a id="idA_PWD_SwitchToCredPicker" href="#" data-bind="\n        text: isUserKnown ? str[\'CT_PWD_STR_SwitchToCredPicker_Link\'] : str[\'CT_PWD_STR_SwitchToCredPicker_Link_NoUser\'],\n        ariaDescribedBy: ariaDescribedBy,\n        click: switchToCredPicker_onClick,\n        hasFocusEx: setFocus"></a>\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko if: credentialCount === 1 && !(showForgotUsername || selectedCredShownOnlyOnPicker) --\x3e\n        \x3c!-- ko ifnot: hideCredSwitchLink --\x3e\n        <a href="#" data-bind="\n            attr: { \'id\': switchToCredId },\n            text: switchToCredText,\n            ariaDescribedBy: ariaDescribedBy,\n            click: switchToCred_onClick,\n            hasFocusEx: !showSwitchToCredPickerLink && setFocus"></a>\n        \x3c!-- /ko --\x3e\n\n        \x3c!-- ko if: displayHelp && selectedCredType === ')+t(0).CredentialType.Fido+' --\x3e\n            \x3c!-- ko component: { name: \'fido-help-button-control\',\n                params: {\n                    isPlatformAuthenticatorAvailable: isPlatformAuthenticatorAvailable() },\n                event: {\n                    registerDialog: onRegisterDialog,\n                    unregisterDialog: onUnregisterDialog,\n                    showDialog: onShowDialog } } --\x3e\n            \x3c!-- /ko --\x3e\n        \x3c!-- /ko --\x3e\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko if: credentialCount === 0 && showForgotUsername --\x3e\n    <a id="forgotUsername" data-bind="\n        href: svr.urlForgotUsername,\n        text: str[\'WF_STR_ForgotUsernameLink_Text\'],\n        ariaDescribedBy: ariaDescribedBy,\n        hasFocusEx: !showSwitchToCredPickerLink && setFocus"></a>\n    \x3c!-- /ko --\x3e\n</div>\n\n\x3c!-- ko if: credLinkError --\x3e\n<div class="row">\n    <div class="col-md-24 alert-margin-bottom" role="alert" aria-live="assertive" data-bind="text: credLinkError, externalCss: { \'error\': true }"></div>\n</div>\n\x3c!-- /ko --\x3e'},function(e,n,t){e.exports="\x3c!-- "+(t(6),t(67),' --\x3e\n\n<span class="help-button" role="button" tabindex="0" data-bind="\n    click: fidoHelp_onClick,\n    pressEnter: fidoHelp_onClick,\n    hasFocus: hasFocus,\n    ariaLabel: isPlatformAuthenticatorAvailable ? str[\'CT_STR_CredentialPicker_Help_Desc_Fido\'] : str[\'CT_STR_CredentialPicker_Help_Desc_FidoCrossPlatform\']">\n\n    \x3c!-- ko component: \'accessible-image-control\' --\x3e\n    <img role="presentation" pngSrc="')+t(19)+'" svgSrc="'+t(20)+'" data-bind="imgSrc" />\n    <img role="presentation" pngSrc="'+t(21)+'" svgSrc="'+t(22)+'" data-bind="imgSrc" />\n    \x3c!-- /ko --\x3e\n</span>\n\n<div data-bind="component: { name: \'fido-help-dialog-content-control\',\n    params: {\n        isPlatformAuthenticatorAvailable: isPlatformAuthenticatorAvailable },\n    event: {\n        registerDialog: onRegisterDialog,\n        unregisterDialog: onUnregisterDialog } }">\n</div>'},function(e,n,t){e.exports="\x3c!-- "+(t(6),t(29)," --\x3e\n\n<div data-bind=\"component: { name: 'dialog-content-control',\n    params: {\n        dialogId: ")+t(0).DialogId.FidoHelp+',\n        data: {\n            labelledBy: \'fidoDialogTitle\',\n            describedBy: \'fidoDialogDesc fidoDialogDesc2\',\n            primaryButtonPreventTabbing: { direction: \'down\' },\n            isPlatformAuthenticatorAvailable: isPlatformAuthenticatorAvailable } },\n    event: {\n        registerDialog: onRegisterDialog,\n        unregisterDialog: onUnregisterDialog } }">\n\n    \x3c!-- ko ifnot: svr.fIsPasskeySupportEnabled --\x3e\n    <div id="fidoDialogTitle" class="row" role="heading" aria-level="1" data-bind="externalCss: { \'title\': true }">\n        \x3c!-- ko component: \'accessible-image-control\' --\x3e\n        <img role="presentation" pngSrc="'+t(44)+'" svgSrc="'+t(45)+'" data-bind="imgSrc" />\n        <img role="presentation" pngSrc="'+t(46)+'" svgSrc="'+t(47)+'" data-bind="imgSrc" />\n        \x3c!-- /ko --\x3e\n\n        <span data-bind="text: isPlatformAuthenticatorAvailable ? str[\'CT_PWD_STR_SwitchToFido_Link\'] : str[\'CT_PWD_STR_SwitchToFidoCrossPlatform_Link\']"></span>\n    </div>\n\n    <div id="fidoDialogDesc" class="row text-body no-margin-top" data-bind="\n        text: isPlatformAuthenticatorAvailable ? str[\'CT_STR_FidoDialog_Desc\'] : str[\'CT_STR_FidoDialog_Desc_CrossPlatform\']">\n    </div>\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko if: svr.fIsPasskeySupportEnabled --\x3e\n    <div id="fidoDialogTitle" class="row" role="heading" aria-level="1" data-bind="externalCss: { \'title\': true }">\n        \x3c!-- ko component: \'accessible-image-control\' --\x3e\n        <img role="presentation" pngSrc="'+t(48)+'" svgSrc="'+t(49)+'" data-bind="imgSrc" />\n        <img role="presentation" pngSrc="'+t(50)+'" svgSrc="'+t(51)+'" data-bind="imgSrc" />\n        \x3c!-- /ko --\x3e\n\n        <span data-bind="text: str[\'CT_STR_CredentialPicker_Option_Passkey\']"></span>\n    </div>\n\n    <div id="fidoDialogDesc" class="row text-body no-margin-top" data-bind="\n        text: str[\'CT_STR_PasskeyDialog_Desc\']">\n    </div>\n    \x3c!-- /ko --\x3e\n\n    \x3c!-- ko if: !svr.fIsOOBE && svr.urlFidoHelp && !svr.fIsHosted--\x3e\n    <div id="fidoDialogDesc2" class="row text-body" data-bind="\n        htmlWithBindings: html[\'CT_STR_FidoDialog_Desc2\'],\n        childBindings: {\n            \'fidoHelpLink\': {\n                href: svr.urlFidoHelp,\n                preventTabbing: { direction: \'up\' },\n                attr: { \'target\': \'_blank\' } } }">\n    </div>\n    \x3c!-- /ko --\x3e\n</div>'},function(e,n){e.exports='<div>\n    <div class="row" id="loginHeader" data-bind="externalCss: { \'title\': true }">\n        <div role="heading" aria-level="1" data-bind="text: title"></div>\n        \x3c!-- ko if: isSubtitleVisible --\x3e\n        <div data-bind="text: subtitle || str[\'WF_STR_App_Title\'], externalCss: { \'subtitle\': true }"></div>\n        \x3c!-- /ko --\x3e\n    </div>\n\n    \x3c!-- ko if: headerDescription --\x3e\n    <div id="loginHeaderDescription" class="form-group" data-bind="text: headerDescription, externalCss: { \'header-description\': true }"></div>\n    \x3c!-- /ko --\x3e\n</div>'},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=t(0),s=window,c=r.PaginatedState;function d(e){var n=this,t=e.serverData,a=t.urlOobeServicesInformationLink;n.onSwitchView=o.create(),n.serverData=t,n.iFrameReady=i.observable(!1),n.iFrameTarget=function(){return a},n.saveSharedData=function(){},n.getState=function(){return null},n.restoreState=function(){},n.secondaryButton_onClick=function(){n.onSwitchView(c.Previous)},n.iframeMessage=function(e){var t=new URL(a).origin;if(e.origin===t){var i=document.getElementById("sdxiframe");i&&(i.style.height=e.data,i.style.setProperty("visibility","visible")),n.iFrameReady(!0)}},window.addEventListener("message",n.iframeMessage,!1)}i.components.register("see-how-data-is-managed-view",{viewModel:d,template:t(496),synchronous:!s.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(s.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=d},function(e,n,t){e.exports="\x3c!-- "+(t(16),t(13),' --\x3e\n\n<div class="section">\n    \x3c!-- ko ifnot: iFrameReady() --\x3e\n    <div class="row progress-container">\n        <div class="progress" role="alert" tabindex="-1"\n            data-bind="component: \'marching-ants-control\', ariaLabel: str[\'WF_STR_ProgressText\']"></div>\n    </div>\n    \x3c!-- /ko --\x3e\n    <div id="sdxcontainer">\n        <iframe id="sdxiframe" data-bind="attr: { src: iFrameTarget() }"></iframe>\n    </div>\n\n    <div class="win-button-pin-bottom">\n        <div class="row">\n            <div data-bind="component: { name: \'footer-buttons-field\',\n                params: {\n                    serverData: svr,\n                    isPrimaryButtonVisible: false,\n                    isSecondaryButtonVisible: true,\n                    focusOnSecondaryButton: true },\n                event: {\n                    secondaryButtonClick: secondaryButton_onClick } }">\n            </div>\n        </div>\n    </div>\n</div>')},function(e,n,t){var i=t(2),a=t(1),o=t(4),r=t(0),s=window,c=r.PaginatedState;function d(e){var n=this,t=e.serverData,i=e.isInitialView,a=e.moreInfoTitle,r=e.moreInfo,s=!!e.showErrorText,d=t.fShowButtons,l=t.fAllowCancel;n.onSwitchView=o.create(),n.onSetBackButtonState=o.create(),n.moreInfoTitle=a,n.moreInfo=r,n.showErrorText=s,n.saveSharedData=function(){},n.getState=function(){},n.restoreState=function(){},n.secondaryButton_onClick=function(){n.onSwitchView(c.Previous)},n.onSetBackButtonState(d&&(!i||l))}i.components.register("more-info-view",{viewModel:d,template:t(498),synchronous:!s.ServerData.iMaxStackForKnockoutAsyncComponents||a.Helper.isStackSizeGreaterThan(s.ServerData.iMaxStackForKnockoutAsyncComponents),enableExtensions:!0}),e.exports=d},function(e,n,t){e.exports="\x3c!-- "+(t(13),' --\x3e\n\n\x3c!-- ko if: moreInfoTitle --\x3e\n<div id="moreInfoHeader" class="row" role="heading" aria-level="1" data-bind="text: moreInfoTitle, externalCss: { \'title\': true }"></div>\n\x3c!-- /ko --\x3e\n\n<div class="row text-body">\n    <div id="moreInfoText" class="text-block-body overflow-hidden" data-bind="text: moreInfo, externalCss: { \'error\': showErrorText }"></div>\n</div>\n\n<div class="row">\n    <div data-bind="component: { name: \'footer-buttons-field\',\n        params: {\n            serverData: svr,\n            isPrimaryButtonVisible: false,\n            isSecondaryButtonVisible: true,\n            focusOnSecondaryButton: true,\n            secondaryButtonDescribedBy: \'moreInfoHeader moreInfoText\' },\n        event: {\n            secondaryButtonClick: secondaryButton_onClick } }">\n    </div>\n</div>')}]),window.__ConvergedLogin_PCore=!0;
    //# sourceMappingURL=b30ad0eaaeb0a793041e.map